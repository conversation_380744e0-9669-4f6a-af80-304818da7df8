import { Injectable, Inject, PLATFORM_ID } from '@angular/core';
import { isPlatformBrowser } from '@angular/common';
import { BehaviorSubject, Observable, of } from 'rxjs';
import { User, LoginCredentials, RegisterData } from '../models';

@Injectable({
  providedIn: 'root'
})
export class AuthService {
  private currentUserSubject = new BehaviorSubject<User | null>(null);
  public currentUser$ = this.currentUserSubject.asObservable();
  private isBrowser: boolean;

  constructor(@Inject(PLATFORM_ID) private platformId: Object) {
    this.isBrowser = isPlatformBrowser(this.platformId);
    this.loadUserFromStorage();
  }

  private loadUserFromStorage(): void {
    if (this.isBrowser) {
      try {
        const userData = localStorage.getItem('currentUser');
        if (userData) {
          const user = JSON.parse(userData);
          this.currentUserSubject.next(user);
        }
      } catch (error) {
        console.error('Error loading user from storage:', error);
      }
    }
  }

  private saveUserToStorage(user: User | null): void {
    if (this.isBrowser) {
      try {
        if (user) {
          localStorage.setItem('currentUser', JSON.stringify(user));
        } else {
          localStorage.removeItem('currentUser');
        }
      } catch (error) {
        console.error('Error saving user to storage:', error);
      }
    }
  }

  login(credentials: LoginCredentials): Observable<User> {
    return new Promise<User>((resolve, reject) => {
      // Mock authentication logic
      setTimeout(() => {
        if (credentials.email === '<EMAIL>' && credentials.password === 'admin') {
          const adminUser: User = {
            id: '1',
            name: 'Admin User',
            email: '<EMAIL>',
            role: 'admin',
            createdAt: new Date()
          };
          this.currentUserSubject.next(adminUser);
          this.saveUserToStorage(adminUser);
          resolve(adminUser);
        } else if (credentials.email && credentials.password) {
          const clientUser: User = {
            id: '2',
            name: 'Client User',
            email: credentials.email,
            role: 'client',
            createdAt: new Date()
          };
          this.currentUserSubject.next(clientUser);
          this.saveUserToStorage(clientUser);
          resolve(clientUser);
        } else {
          reject(new Error('Invalid credentials'));
        }
      }, 1000);
    }) as any;
  }

  register(userData: RegisterData): Observable<User> {
    return new Promise<User>((resolve, reject) => {
      // Mock registration logic
      setTimeout(() => {
        if (userData.email && userData.password && userData.name) {
          const newUser: User = {
            id: Date.now().toString(),
            name: userData.name,
            email: userData.email,
            role: 'client',
            createdAt: new Date()
          };
          this.currentUserSubject.next(newUser);
          this.saveUserToStorage(newUser);
          resolve(newUser);
        } else {
          reject(new Error('Invalid registration data'));
        }
      }, 1000);
    }) as any;
  }

  logout(): void {
    this.currentUserSubject.next(null);
    this.saveUserToStorage(null);
  }

  getCurrentUser(): User | null {
    return this.currentUserSubject.value;
  }

  isAuthenticated(): boolean {
    return this.currentUserSubject.value !== null;
  }

  isAdmin(): boolean {
    const user = this.currentUserSubject.value;
    return user?.role === 'admin';
  }

  isClient(): boolean {
    const user = this.currentUserSubject.value;
    return user?.role === 'client';
  }
}
