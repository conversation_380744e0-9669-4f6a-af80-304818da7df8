import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, of } from 'rxjs';
import { User, UserRole, LoginCredentials, RegisterData } from '../models';

@Injectable({
  providedIn: 'root'
})
export class AuthService {
  private currentUserSubject = new BehaviorSubject<User | null>(null);
  public currentUser$ = this.currentUserSubject.asObservable();

  // Hardcoded users for demo purposes
  private users: User[] = [
    {
      id: 1,
      email: '<EMAIL>',
      name: 'Admin User',
      role: UserRole.ADMIN
    }
  ];

  constructor() {
    // Check if user is already logged in
    const savedUser = localStorage.getItem('currentUser');
    if (savedUser) {
      this.currentUserSubject.next(JSON.parse(savedUser));
    }
  }

  login(credentials: LoginCredentials): Observable<User | null> {
    // Check for hardcoded admin credentials
    if (credentials.email === '<EMAIL>' && credentials.password === 'admin') {
      const adminUser = this.users.find(u => u.email === credentials.email);
      if (adminUser) {
        this.setCurrentUser(adminUser);
        return of(adminUser);
      }
    }

    // Check for registered client users
    const user = this.users.find(u => 
      u.email === credentials.email && 
      u.role === UserRole.CLIENT
    );

    if (user) {
      // In a real app, you'd verify the password here
      this.setCurrentUser(user);
      return of(user);
    }

    return of(null);
  }

  register(registerData: RegisterData): Observable<User | null> {
    // Check if user already exists
    const existingUser = this.users.find(u => u.email === registerData.email);
    if (existingUser) {
      return of(null); // User already exists
    }

    // Create new client user
    const newUser: User = {
      id: this.users.length + 1,
      email: registerData.email,
      name: registerData.name,
      role: UserRole.CLIENT,
      createdAt: new Date()
    };

    this.users.push(newUser);
    this.setCurrentUser(newUser);
    return of(newUser);
  }

  logout(): void {
    localStorage.removeItem('currentUser');
    this.currentUserSubject.next(null);
  }

  getCurrentUser(): User | null {
    return this.currentUserSubject.value;
  }

  isLoggedIn(): boolean {
    return this.currentUserSubject.value !== null;
  }

  isAdmin(): boolean {
    const user = this.getCurrentUser();
    return user?.role === UserRole.ADMIN;
  }

  isClient(): boolean {
    const user = this.getCurrentUser();
    return user?.role === UserRole.CLIENT;
  }

  private setCurrentUser(user: User): void {
    localStorage.setItem('currentUser', JSON.stringify(user));
    this.currentUserSubject.next(user);
  }
}
