@use '@angular/cdk';

@mixin private-form-field-high-contrast() {
  $focus-indicator-width: 3px;
  $focus-indicator-style: dashed;

  .mat-form-field-appearance-fill {
    // The outline of the `fill` appearance is achieved through a background color
    // which won't be visible in high contrast mode. Add an outline to replace it.
    .mat-mdc-text-field-wrapper {
      @include cdk.high-contrast {
        outline: solid 1px;
      }
    }

    // Use GreyText for the disabled outline color which will account for the user's configuration.
    &.mat-form-field-disabled .mat-mdc-text-field-wrapper {
      @include cdk.high-contrast {
        outline-color: GrayText;
      }
    }
  }

  // If a form field with fill appearance is focused, update the outline to be
  // dashed and thicker to indicate focus.
  .mat-form-field-appearance-fill.mat-focused .mat-mdc-text-field-wrapper {
    @include cdk.high-contrast {
      outline: $focus-indicator-style $focus-indicator-width;
    }
  }

  // For form fields with outline appearance, we show a dashed thick border on top
  // of the solid notched-outline border to indicate focus.
  .mat-mdc-form-field.mat-focused .mdc-notched-outline {
    @include cdk.high-contrast {
      border: $focus-indicator-style $focus-indicator-width;
    }
  }
}
