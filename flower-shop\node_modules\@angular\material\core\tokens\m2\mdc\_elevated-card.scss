@use '../../../theming/inspection';
@use '../../../style/elevation';
@use '../../../style/sass-utils';
@use '../../token-definition';

// The prefix used to generate the fully qualified name for tokens in this file.
$prefix: (mdc, elevated-card);

// Tokens that can't be configured through Angular Material's current theming API,
// but may be in a future version of the theming API.
//
// Tokens that are available in MDC, but not used in Angular Material should be mapped to `null`.
// `null` indicates that we are intentionally choosing not to emit a slot or value for the token in
// our CSS.
@function get-unthemable-tokens() {
  @return (
    // The border-radius of the card.
    container-shape: 4px,
    // =============================================================================================
    // = TOKENS NOT USED IN ANGULAR MATERIAL                                                       =
    // =============================================================================================
    // Angular Material's card is not an interactive element, and therefore does not support states.
    disabled-container-color: null,
    disabled-container-elevation: null,
    disabled-container-opacity: null,
    dragged-container-elevation: null,
    dragged-state-layer-color: null,
    dragged-state-layer-opacity: null,
    focus-container-elevation: null,
    focus-state-layer-color:null,
    focus-state-layer-opacity: null,
    hover-container-elevation: null,
    hover-state-layer-color: null,
    hover-state-layer-opacity: null,
    pressed-container-elevation: null,
    pressed-state-layer-color: null,
    pressed-state-layer-opacity: null,
    container-shadow-color: null,
    // Angular Material does not currently support surface tint.
    container-surface-tint-layer-color: null,
    // MDC does not seem to use these tokens.
    icon-color: null,
    icon-size: null,
  );
}

// Tokens that can be configured through Angular Material's color theming API.
@function get-color-tokens($theme) {
  $elevation: inspection.get-theme-color($theme, foreground, elevation);

  @return (
    // The background color of the card.
    container-color: inspection.get-theme-color($theme, background, card),
    container-elevation: elevation.get-box-shadow(1),
  );
}

// Tokens that can be configured through Angular Material's typography theming API.
@function get-typography-tokens($theme) {
  @return ();
}

// Tokens that can be configured through Angular Material's density theming API.
@function get-density-tokens($theme) {
  @return ();
}

// Combines the tokens generated by the above functions into a single map with placeholder values.
// This is used to create token slots.
@function get-token-slots() {
  @return sass-utils.deep-merge-all(
      get-unthemable-tokens(),
      get-color-tokens(token-definition.$placeholder-color-config),
      get-typography-tokens(token-definition.$placeholder-typography-config),
      get-density-tokens(token-definition.$placeholder-density-config)
  );
}
