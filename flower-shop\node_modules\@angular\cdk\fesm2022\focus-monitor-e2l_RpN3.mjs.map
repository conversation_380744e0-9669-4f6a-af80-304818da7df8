{"version": 3, "file": "focus-monitor-e2l_RpN3.mjs", "sources": ["../../../../../k8-fastbuild-ST-46c76129e412/bin/src/cdk/a11y/input-modality/input-modality-detector.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/cdk/a11y/focus-monitor/focus-monitor.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {ALT, CONTROL, MAC_META, META, SHIFT} from '../../keycodes';\nimport {\n  Injectable,\n  InjectionToken,\n  OnDestroy,\n  NgZone,\n  inject,\n  RendererFactory2,\n} from '@angular/core';\nimport {Platform, _bindEventWithOptions, _getEventTarget} from '../../platform';\nimport {DOCUMENT} from '@angular/common';\nimport {BehaviorSubject, Observable} from 'rxjs';\nimport {distinctUntilChanged, skip} from 'rxjs/operators';\nimport {\n  isFakeMousedownFromScreenReader,\n  isFakeTouchstartFromScreenReader,\n} from '../fake-event-detection';\n\n/**\n * The input modalities detected by this service. Null is used if the input modality is unknown.\n */\nexport type InputModality = 'keyboard' | 'mouse' | 'touch' | null;\n\n/** Options to configure the behavior of the InputModalityDetector. */\nexport interface InputModalityDetectorOptions {\n  /** Keys to ignore when detecting keyboard input modality. */\n  ignoreKeys?: number[];\n}\n\n/**\n * Injectable options for the InputModalityDetector. These are shallowly merged with the default\n * options.\n */\nexport const INPUT_MODALITY_DETECTOR_OPTIONS = new InjectionToken<InputModalityDetectorOptions>(\n  'cdk-input-modality-detector-options',\n);\n\n/**\n * Default options for the InputModalityDetector.\n *\n * Modifier keys are ignored by default (i.e. when pressed won't cause the service to detect\n * keyboard input modality) for two reasons:\n *\n * 1. Modifier keys are commonly used with mouse to perform actions such as 'right click' or 'open\n *    in new tab', and are thus less representative of actual keyboard interaction.\n * 2. VoiceOver triggers some keyboard events when linearly navigating with Control + Option (but\n *    confusingly not with Caps Lock). Thus, to have parity with other screen readers, we ignore\n *    these keys so as to not update the input modality.\n *\n * Note that we do not by default ignore the right Meta key on Safari because it has the same key\n * code as the ContextMenu key on other browsers. When we switch to using event.key, we can\n * distinguish between the two.\n */\nexport const INPUT_MODALITY_DETECTOR_DEFAULT_OPTIONS: InputModalityDetectorOptions = {\n  ignoreKeys: [ALT, CONTROL, MAC_META, META, SHIFT],\n};\n\n/**\n * The amount of time needed to pass after a touchstart event in order for a subsequent mousedown\n * event to be attributed as mouse and not touch.\n *\n * This is the value used by AngularJS Material. Through trial and error (on iPhone 6S) they found\n * that a value of around 650ms seems appropriate.\n */\nexport const TOUCH_BUFFER_MS = 650;\n\n/**\n * Event listener options that enable capturing and also mark the listener as passive if the browser\n * supports it.\n */\nconst modalityEventListenerOptions = {\n  passive: true,\n  capture: true,\n};\n\n/**\n * Service that detects the user's input modality.\n *\n * This service does not update the input modality when a user navigates with a screen reader\n * (e.g. linear navigation with VoiceOver, object navigation / browse mode with NVDA, virtual PC\n * cursor mode with JAWS). This is in part due to technical limitations (i.e. keyboard events do not\n * fire as expected in these modes) but is also arguably the correct behavior. Navigating with a\n * screen reader is akin to visually scanning a page, and should not be interpreted as actual user\n * input interaction.\n *\n * When a user is not navigating but *interacting* with a screen reader, this service attempts to\n * update the input modality to keyboard, but in general this service's behavior is largely\n * undefined.\n */\n@Injectable({providedIn: 'root'})\nexport class InputModalityDetector implements OnDestroy {\n  private readonly _platform = inject(Platform);\n  private readonly _listenerCleanups: (() => void)[] | undefined;\n\n  /** Emits whenever an input modality is detected. */\n  readonly modalityDetected: Observable<InputModality>;\n\n  /** Emits when the input modality changes. */\n  readonly modalityChanged: Observable<InputModality>;\n\n  /** The most recently detected input modality. */\n  get mostRecentModality(): InputModality {\n    return this._modality.value;\n  }\n\n  /**\n   * The most recently detected input modality event target. Is null if no input modality has been\n   * detected or if the associated event target is null for some unknown reason.\n   */\n  _mostRecentTarget: HTMLElement | null = null;\n\n  /** The underlying BehaviorSubject that emits whenever an input modality is detected. */\n  private readonly _modality = new BehaviorSubject<InputModality>(null);\n\n  /** Options for this InputModalityDetector. */\n  private readonly _options: InputModalityDetectorOptions;\n\n  /**\n   * The timestamp of the last touch input modality. Used to determine whether mousedown events\n   * should be attributed to mouse or touch.\n   */\n  private _lastTouchMs = 0;\n\n  /**\n   * Handles keydown events. Must be an arrow function in order to preserve the context when it gets\n   * bound.\n   */\n  private _onKeydown = (event: KeyboardEvent) => {\n    // If this is one of the keys we should ignore, then ignore it and don't update the input\n    // modality to keyboard.\n    if (this._options?.ignoreKeys?.some(keyCode => keyCode === event.keyCode)) {\n      return;\n    }\n\n    this._modality.next('keyboard');\n    this._mostRecentTarget = _getEventTarget(event);\n  };\n\n  /**\n   * Handles mousedown events. Must be an arrow function in order to preserve the context when it\n   * gets bound.\n   */\n  private _onMousedown = (event: MouseEvent) => {\n    // Touches trigger both touch and mouse events, so we need to distinguish between mouse events\n    // that were triggered via mouse vs touch. To do so, check if the mouse event occurs closely\n    // after the previous touch event.\n    if (Date.now() - this._lastTouchMs < TOUCH_BUFFER_MS) {\n      return;\n    }\n\n    // Fake mousedown events are fired by some screen readers when controls are activated by the\n    // screen reader. Attribute them to keyboard input modality.\n    this._modality.next(isFakeMousedownFromScreenReader(event) ? 'keyboard' : 'mouse');\n    this._mostRecentTarget = _getEventTarget(event);\n  };\n\n  /**\n   * Handles touchstart events. Must be an arrow function in order to preserve the context when it\n   * gets bound.\n   */\n  private _onTouchstart = (event: TouchEvent) => {\n    // Same scenario as mentioned in _onMousedown, but on touch screen devices, fake touchstart\n    // events are fired. Again, attribute to keyboard input modality.\n    if (isFakeTouchstartFromScreenReader(event)) {\n      this._modality.next('keyboard');\n      return;\n    }\n\n    // Store the timestamp of this touch event, as it's used to distinguish between mouse events\n    // triggered via mouse vs touch.\n    this._lastTouchMs = Date.now();\n\n    this._modality.next('touch');\n    this._mostRecentTarget = _getEventTarget(event);\n  };\n\n  constructor(...args: unknown[]);\n\n  constructor() {\n    const ngZone = inject(NgZone);\n    const document = inject<Document>(DOCUMENT);\n    const options = inject(INPUT_MODALITY_DETECTOR_OPTIONS, {optional: true});\n\n    this._options = {\n      ...INPUT_MODALITY_DETECTOR_DEFAULT_OPTIONS,\n      ...options,\n    };\n\n    // Skip the first emission as it's null.\n    this.modalityDetected = this._modality.pipe(skip(1));\n    this.modalityChanged = this.modalityDetected.pipe(distinctUntilChanged());\n\n    // If we're not in a browser, this service should do nothing, as there's no relevant input\n    // modality to detect.\n    if (this._platform.isBrowser) {\n      const renderer = inject(RendererFactory2).createRenderer(null, null);\n\n      this._listenerCleanups = ngZone.runOutsideAngular(() => {\n        return [\n          _bindEventWithOptions(\n            renderer,\n            document,\n            'keydown',\n            this._onKeydown,\n            modalityEventListenerOptions,\n          ),\n          _bindEventWithOptions(\n            renderer,\n            document,\n            'mousedown',\n            this._onMousedown,\n            modalityEventListenerOptions,\n          ),\n          _bindEventWithOptions(\n            renderer,\n            document,\n            'touchstart',\n            this._onTouchstart,\n            modalityEventListenerOptions,\n          ),\n        ];\n      });\n    }\n  }\n\n  ngOnDestroy() {\n    this._modality.complete();\n    this._listenerCleanups?.forEach(cleanup => cleanup());\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  Platform,\n  normalizePassiveListenerOptions,\n  _getShadowRoot,\n  _getEventTarget,\n} from '../../platform';\nimport {\n  Directive,\n  ElementRef,\n  EventEmitter,\n  Injectable,\n  InjectionToken,\n  NgZone,\n  OnDestroy,\n  Output,\n  AfterViewInit,\n  inject,\n} from '@angular/core';\nimport {Observable, of as observableOf, Subject, Subscription} from 'rxjs';\nimport {takeUntil} from 'rxjs/operators';\nimport {coerceElement} from '../../coercion';\nimport {DOCUMENT} from '@angular/common';\nimport {InputModalityDetector, TOUCH_BUFFER_MS} from '../input-modality/input-modality-detector';\n\nexport type FocusOrigin = 'touch' | 'mouse' | 'keyboard' | 'program' | null;\n\n/**\n * Corresponds to the options that can be passed to the native `focus` event.\n * via https://developer.mozilla.org/en-US/docs/Web/API/HTMLElement/focus\n */\nexport interface FocusOptions {\n  /** Whether the browser should scroll to the element when it is focused. */\n  preventScroll?: boolean;\n}\n\n/** Detection mode used for attributing the origin of a focus event. */\nexport enum FocusMonitorDetectionMode {\n  /**\n   * Any mousedown, keydown, or touchstart event that happened in the previous\n   * tick or the current tick will be used to assign a focus event's origin (to\n   * either mouse, keyboard, or touch). This is the default option.\n   */\n  IMMEDIATE,\n  /**\n   * A focus event's origin is always attributed to the last corresponding\n   * mousedown, keydown, or touchstart event, no matter how long ago it occurred.\n   */\n  EVENTUAL,\n}\n\n/** Injectable service-level options for FocusMonitor. */\nexport interface FocusMonitorOptions {\n  detectionMode?: FocusMonitorDetectionMode;\n}\n\n/** InjectionToken for FocusMonitorOptions. */\nexport const FOCUS_MONITOR_DEFAULT_OPTIONS = new InjectionToken<FocusMonitorOptions>(\n  'cdk-focus-monitor-default-options',\n);\n\ntype MonitoredElementInfo = {\n  checkChildren: boolean;\n  readonly subject: Subject<FocusOrigin>;\n  rootNode: HTMLElement | ShadowRoot | Document;\n};\n\n/**\n * Event listener options that enable capturing and also\n * mark the listener as passive if the browser supports it.\n */\nconst captureEventListenerOptions = normalizePassiveListenerOptions({\n  passive: true,\n  capture: true,\n});\n\n/** Monitors mouse and keyboard events to determine the cause of focus events. */\n@Injectable({providedIn: 'root'})\nexport class FocusMonitor implements OnDestroy {\n  private _ngZone = inject(NgZone);\n  private _platform = inject(Platform);\n  private readonly _inputModalityDetector = inject(InputModalityDetector);\n\n  /** The focus origin that the next focus event is a result of. */\n  private _origin: FocusOrigin = null;\n\n  /** The FocusOrigin of the last focus event tracked by the FocusMonitor. */\n  private _lastFocusOrigin: FocusOrigin;\n\n  /** Whether the window has just been focused. */\n  private _windowFocused = false;\n\n  /** The timeout id of the window focus timeout. */\n  private _windowFocusTimeoutId: ReturnType<typeof setTimeout>;\n\n  /** The timeout id of the origin clearing timeout. */\n  private _originTimeoutId: ReturnType<typeof setTimeout>;\n\n  /**\n   * Whether the origin was determined via a touch interaction. Necessary as properly attributing\n   * focus events to touch interactions requires special logic.\n   */\n  private _originFromTouchInteraction = false;\n\n  /** Map of elements being monitored to their info. */\n  private _elementInfo = new Map<HTMLElement, MonitoredElementInfo>();\n\n  /** The number of elements currently being monitored. */\n  private _monitoredElementCount = 0;\n\n  /**\n   * Keeps track of the root nodes to which we've currently bound a focus/blur handler,\n   * as well as the number of monitored elements that they contain. We have to treat focus/blur\n   * handlers differently from the rest of the events, because the browser won't emit events\n   * to the document when focus moves inside of a shadow root.\n   */\n  private _rootNodeFocusListenerCount = new Map<HTMLElement | Document | ShadowRoot, number>();\n\n  /**\n   * The specified detection mode, used for attributing the origin of a focus\n   * event.\n   */\n  private readonly _detectionMode: FocusMonitorDetectionMode;\n\n  /**\n   * Event listener for `focus` events on the window.\n   * Needs to be an arrow function in order to preserve the context when it gets bound.\n   */\n  private _windowFocusListener = () => {\n    // Make a note of when the window regains focus, so we can\n    // restore the origin info for the focused element.\n    this._windowFocused = true;\n    this._windowFocusTimeoutId = setTimeout(() => (this._windowFocused = false));\n  };\n\n  /** Used to reference correct document/window */\n  protected _document? = inject(DOCUMENT, {optional: true});\n\n  /** Subject for stopping our InputModalityDetector subscription. */\n  private readonly _stopInputModalityDetector = new Subject<void>();\n\n  constructor(...args: unknown[]);\n\n  constructor() {\n    const options = inject<FocusMonitorOptions | null>(FOCUS_MONITOR_DEFAULT_OPTIONS, {\n      optional: true,\n    });\n\n    this._detectionMode = options?.detectionMode || FocusMonitorDetectionMode.IMMEDIATE;\n  }\n  /**\n   * Event listener for `focus` and 'blur' events on the document.\n   * Needs to be an arrow function in order to preserve the context when it gets bound.\n   */\n  private _rootNodeFocusAndBlurListener = (event: Event) => {\n    const target = _getEventTarget<HTMLElement>(event);\n\n    // We need to walk up the ancestor chain in order to support `checkChildren`.\n    for (let element = target; element; element = element.parentElement) {\n      if (event.type === 'focus') {\n        this._onFocus(event as FocusEvent, element);\n      } else {\n        this._onBlur(event as FocusEvent, element);\n      }\n    }\n  };\n\n  /**\n   * Monitors focus on an element and applies appropriate CSS classes.\n   * @param element The element to monitor\n   * @param checkChildren Whether to count the element as focused when its children are focused.\n   * @returns An observable that emits when the focus state of the element changes.\n   *     When the element is blurred, null will be emitted.\n   */\n  monitor(element: HTMLElement, checkChildren?: boolean): Observable<FocusOrigin>;\n\n  /**\n   * Monitors focus on an element and applies appropriate CSS classes.\n   * @param element The element to monitor\n   * @param checkChildren Whether to count the element as focused when its children are focused.\n   * @returns An observable that emits when the focus state of the element changes.\n   *     When the element is blurred, null will be emitted.\n   */\n  monitor(element: ElementRef<HTMLElement>, checkChildren?: boolean): Observable<FocusOrigin>;\n\n  monitor(\n    element: HTMLElement | ElementRef<HTMLElement>,\n    checkChildren: boolean = false,\n  ): Observable<FocusOrigin> {\n    const nativeElement = coerceElement(element);\n\n    // Do nothing if we're not on the browser platform or the passed in node isn't an element.\n    if (!this._platform.isBrowser || nativeElement.nodeType !== 1) {\n      // Note: we don't want the observable to emit at all so we don't pass any parameters.\n      return observableOf();\n    }\n\n    // If the element is inside the shadow DOM, we need to bind our focus/blur listeners to\n    // the shadow root, rather than the `document`, because the browser won't emit focus events\n    // to the `document`, if focus is moving within the same shadow root.\n    const rootNode = _getShadowRoot(nativeElement) || this._getDocument();\n    const cachedInfo = this._elementInfo.get(nativeElement);\n\n    // Check if we're already monitoring this element.\n    if (cachedInfo) {\n      if (checkChildren) {\n        // TODO(COMP-318): this can be problematic, because it'll turn all non-checkChildren\n        // observers into ones that behave as if `checkChildren` was turned on. We need a more\n        // robust solution.\n        cachedInfo.checkChildren = true;\n      }\n\n      return cachedInfo.subject;\n    }\n\n    // Create monitored element info.\n    const info: MonitoredElementInfo = {\n      checkChildren: checkChildren,\n      subject: new Subject<FocusOrigin>(),\n      rootNode,\n    };\n    this._elementInfo.set(nativeElement, info);\n    this._registerGlobalListeners(info);\n\n    return info.subject;\n  }\n\n  /**\n   * Stops monitoring an element and removes all focus classes.\n   * @param element The element to stop monitoring.\n   */\n  stopMonitoring(element: HTMLElement): void;\n\n  /**\n   * Stops monitoring an element and removes all focus classes.\n   * @param element The element to stop monitoring.\n   */\n  stopMonitoring(element: ElementRef<HTMLElement>): void;\n\n  stopMonitoring(element: HTMLElement | ElementRef<HTMLElement>): void {\n    const nativeElement = coerceElement(element);\n    const elementInfo = this._elementInfo.get(nativeElement);\n\n    if (elementInfo) {\n      elementInfo.subject.complete();\n\n      this._setClasses(nativeElement);\n      this._elementInfo.delete(nativeElement);\n      this._removeGlobalListeners(elementInfo);\n    }\n  }\n\n  /**\n   * Focuses the element via the specified focus origin.\n   * @param element Element to focus.\n   * @param origin Focus origin.\n   * @param options Options that can be used to configure the focus behavior.\n   */\n  focusVia(element: HTMLElement, origin: FocusOrigin, options?: FocusOptions): void;\n\n  /**\n   * Focuses the element via the specified focus origin.\n   * @param element Element to focus.\n   * @param origin Focus origin.\n   * @param options Options that can be used to configure the focus behavior.\n   */\n  focusVia(element: ElementRef<HTMLElement>, origin: FocusOrigin, options?: FocusOptions): void;\n\n  focusVia(\n    element: HTMLElement | ElementRef<HTMLElement>,\n    origin: FocusOrigin,\n    options?: FocusOptions,\n  ): void {\n    const nativeElement = coerceElement(element);\n    const focusedElement = this._getDocument().activeElement;\n\n    // If the element is focused already, calling `focus` again won't trigger the event listener\n    // which means that the focus classes won't be updated. If that's the case, update the classes\n    // directly without waiting for an event.\n    if (nativeElement === focusedElement) {\n      this._getClosestElementsInfo(nativeElement).forEach(([currentElement, info]) =>\n        this._originChanged(currentElement, origin, info),\n      );\n    } else {\n      this._setOrigin(origin);\n\n      // `focus` isn't available on the server\n      if (typeof nativeElement.focus === 'function') {\n        nativeElement.focus(options);\n      }\n    }\n  }\n\n  ngOnDestroy() {\n    this._elementInfo.forEach((_info, element) => this.stopMonitoring(element));\n  }\n\n  /** Access injected document if available or fallback to global document reference */\n  private _getDocument(): Document {\n    return this._document || document;\n  }\n\n  /** Use defaultView of injected document if available or fallback to global window reference */\n  private _getWindow(): Window {\n    const doc = this._getDocument();\n    return doc.defaultView || window;\n  }\n\n  private _getFocusOrigin(focusEventTarget: HTMLElement | null): FocusOrigin {\n    if (this._origin) {\n      // If the origin was realized via a touch interaction, we need to perform additional checks\n      // to determine whether the focus origin should be attributed to touch or program.\n      if (this._originFromTouchInteraction) {\n        return this._shouldBeAttributedToTouch(focusEventTarget) ? 'touch' : 'program';\n      } else {\n        return this._origin;\n      }\n    }\n\n    // If the window has just regained focus, we can restore the most recent origin from before the\n    // window blurred. Otherwise, we've reached the point where we can't identify the source of the\n    // focus. This typically means one of two things happened:\n    //\n    // 1) The element was programmatically focused, or\n    // 2) The element was focused via screen reader navigation (which generally doesn't fire\n    //    events).\n    //\n    // Because we can't distinguish between these two cases, we default to setting `program`.\n    if (this._windowFocused && this._lastFocusOrigin) {\n      return this._lastFocusOrigin;\n    }\n\n    // If the interaction is coming from an input label, we consider it a mouse interactions.\n    // This is a special case where focus moves on `click`, rather than `mousedown` which breaks\n    // our detection, because all our assumptions are for `mousedown`. We need to handle this\n    // special case, because it's very common for checkboxes and radio buttons.\n    if (focusEventTarget && this._isLastInteractionFromInputLabel(focusEventTarget)) {\n      return 'mouse';\n    }\n\n    return 'program';\n  }\n\n  /**\n   * Returns whether the focus event should be attributed to touch. Recall that in IMMEDIATE mode, a\n   * touch origin isn't immediately reset at the next tick (see _setOrigin). This means that when we\n   * handle a focus event following a touch interaction, we need to determine whether (1) the focus\n   * event was directly caused by the touch interaction or (2) the focus event was caused by a\n   * subsequent programmatic focus call triggered by the touch interaction.\n   * @param focusEventTarget The target of the focus event under examination.\n   */\n  private _shouldBeAttributedToTouch(focusEventTarget: HTMLElement | null): boolean {\n    // Please note that this check is not perfect. Consider the following edge case:\n    //\n    // <div #parent tabindex=\"0\">\n    //   <div #child tabindex=\"0\" (click)=\"#parent.focus()\"></div>\n    // </div>\n    //\n    // Suppose there is a FocusMonitor in IMMEDIATE mode attached to #parent. When the user touches\n    // #child, #parent is programmatically focused. This code will attribute the focus to touch\n    // instead of program. This is a relatively minor edge-case that can be worked around by using\n    // focusVia(parent, 'program') to focus #parent.\n    return (\n      this._detectionMode === FocusMonitorDetectionMode.EVENTUAL ||\n      !!focusEventTarget?.contains(this._inputModalityDetector._mostRecentTarget)\n    );\n  }\n\n  /**\n   * Sets the focus classes on the element based on the given focus origin.\n   * @param element The element to update the classes on.\n   * @param origin The focus origin.\n   */\n  private _setClasses(element: HTMLElement, origin?: FocusOrigin): void {\n    element.classList.toggle('cdk-focused', !!origin);\n    element.classList.toggle('cdk-touch-focused', origin === 'touch');\n    element.classList.toggle('cdk-keyboard-focused', origin === 'keyboard');\n    element.classList.toggle('cdk-mouse-focused', origin === 'mouse');\n    element.classList.toggle('cdk-program-focused', origin === 'program');\n  }\n\n  /**\n   * Updates the focus origin. If we're using immediate detection mode, we schedule an async\n   * function to clear the origin at the end of a timeout. The duration of the timeout depends on\n   * the origin being set.\n   * @param origin The origin to set.\n   * @param isFromInteraction Whether we are setting the origin from an interaction event.\n   */\n  private _setOrigin(origin: FocusOrigin, isFromInteraction = false): void {\n    this._ngZone.runOutsideAngular(() => {\n      this._origin = origin;\n      this._originFromTouchInteraction = origin === 'touch' && isFromInteraction;\n\n      // If we're in IMMEDIATE mode, reset the origin at the next tick (or in `TOUCH_BUFFER_MS` ms\n      // for a touch event). We reset the origin at the next tick because Firefox focuses one tick\n      // after the interaction event. We wait `TOUCH_BUFFER_MS` ms before resetting the origin for\n      // a touch event because when a touch event is fired, the associated focus event isn't yet in\n      // the event queue. Before doing so, clear any pending timeouts.\n      if (this._detectionMode === FocusMonitorDetectionMode.IMMEDIATE) {\n        clearTimeout(this._originTimeoutId);\n        const ms = this._originFromTouchInteraction ? TOUCH_BUFFER_MS : 1;\n        this._originTimeoutId = setTimeout(() => (this._origin = null), ms);\n      }\n    });\n  }\n\n  /**\n   * Handles focus events on a registered element.\n   * @param event The focus event.\n   * @param element The monitored element.\n   */\n  private _onFocus(event: FocusEvent, element: HTMLElement) {\n    // NOTE(mmalerba): We currently set the classes based on the focus origin of the most recent\n    // focus event affecting the monitored element. If we want to use the origin of the first event\n    // instead we should check for the cdk-focused class here and return if the element already has\n    // it. (This only matters for elements that have includesChildren = true).\n\n    // If we are not counting child-element-focus as focused, make sure that the event target is the\n    // monitored element itself.\n    const elementInfo = this._elementInfo.get(element);\n    const focusEventTarget = _getEventTarget<HTMLElement>(event);\n    if (!elementInfo || (!elementInfo.checkChildren && element !== focusEventTarget)) {\n      return;\n    }\n\n    this._originChanged(element, this._getFocusOrigin(focusEventTarget), elementInfo);\n  }\n\n  /**\n   * Handles blur events on a registered element.\n   * @param event The blur event.\n   * @param element The monitored element.\n   */\n  _onBlur(event: FocusEvent, element: HTMLElement) {\n    // If we are counting child-element-focus as focused, make sure that we aren't just blurring in\n    // order to focus another child of the monitored element.\n    const elementInfo = this._elementInfo.get(element);\n\n    if (\n      !elementInfo ||\n      (elementInfo.checkChildren &&\n        event.relatedTarget instanceof Node &&\n        element.contains(event.relatedTarget))\n    ) {\n      return;\n    }\n\n    this._setClasses(element);\n    this._emitOrigin(elementInfo, null);\n  }\n\n  private _emitOrigin(info: MonitoredElementInfo, origin: FocusOrigin) {\n    if (info.subject.observers.length) {\n      this._ngZone.run(() => info.subject.next(origin));\n    }\n  }\n\n  private _registerGlobalListeners(elementInfo: MonitoredElementInfo) {\n    if (!this._platform.isBrowser) {\n      return;\n    }\n\n    const rootNode = elementInfo.rootNode;\n    const rootNodeFocusListeners = this._rootNodeFocusListenerCount.get(rootNode) || 0;\n\n    if (!rootNodeFocusListeners) {\n      this._ngZone.runOutsideAngular(() => {\n        rootNode.addEventListener(\n          'focus',\n          this._rootNodeFocusAndBlurListener,\n          captureEventListenerOptions,\n        );\n        rootNode.addEventListener(\n          'blur',\n          this._rootNodeFocusAndBlurListener,\n          captureEventListenerOptions,\n        );\n      });\n    }\n\n    this._rootNodeFocusListenerCount.set(rootNode, rootNodeFocusListeners + 1);\n\n    // Register global listeners when first element is monitored.\n    if (++this._monitoredElementCount === 1) {\n      // Note: we listen to events in the capture phase so we\n      // can detect them even if the user stops propagation.\n      this._ngZone.runOutsideAngular(() => {\n        const window = this._getWindow();\n        window.addEventListener('focus', this._windowFocusListener);\n      });\n\n      // The InputModalityDetector is also just a collection of global listeners.\n      this._inputModalityDetector.modalityDetected\n        .pipe(takeUntil(this._stopInputModalityDetector))\n        .subscribe(modality => {\n          this._setOrigin(modality, true /* isFromInteraction */);\n        });\n    }\n  }\n\n  private _removeGlobalListeners(elementInfo: MonitoredElementInfo) {\n    const rootNode = elementInfo.rootNode;\n\n    if (this._rootNodeFocusListenerCount.has(rootNode)) {\n      const rootNodeFocusListeners = this._rootNodeFocusListenerCount.get(rootNode)!;\n\n      if (rootNodeFocusListeners > 1) {\n        this._rootNodeFocusListenerCount.set(rootNode, rootNodeFocusListeners - 1);\n      } else {\n        rootNode.removeEventListener(\n          'focus',\n          this._rootNodeFocusAndBlurListener,\n          captureEventListenerOptions,\n        );\n        rootNode.removeEventListener(\n          'blur',\n          this._rootNodeFocusAndBlurListener,\n          captureEventListenerOptions,\n        );\n        this._rootNodeFocusListenerCount.delete(rootNode);\n      }\n    }\n\n    // Unregister global listeners when last element is unmonitored.\n    if (!--this._monitoredElementCount) {\n      const window = this._getWindow();\n      window.removeEventListener('focus', this._windowFocusListener);\n\n      // Equivalently, stop our InputModalityDetector subscription.\n      this._stopInputModalityDetector.next();\n\n      // Clear timeouts for all potentially pending timeouts to prevent the leaks.\n      clearTimeout(this._windowFocusTimeoutId);\n      clearTimeout(this._originTimeoutId);\n    }\n  }\n\n  /** Updates all the state on an element once its focus origin has changed. */\n  private _originChanged(\n    element: HTMLElement,\n    origin: FocusOrigin,\n    elementInfo: MonitoredElementInfo,\n  ) {\n    this._setClasses(element, origin);\n    this._emitOrigin(elementInfo, origin);\n    this._lastFocusOrigin = origin;\n  }\n\n  /**\n   * Collects the `MonitoredElementInfo` of a particular element and\n   * all of its ancestors that have enabled `checkChildren`.\n   * @param element Element from which to start the search.\n   */\n  private _getClosestElementsInfo(element: HTMLElement): [HTMLElement, MonitoredElementInfo][] {\n    const results: [HTMLElement, MonitoredElementInfo][] = [];\n\n    this._elementInfo.forEach((info, currentElement) => {\n      if (currentElement === element || (info.checkChildren && currentElement.contains(element))) {\n        results.push([currentElement, info]);\n      }\n    });\n\n    return results;\n  }\n\n  /**\n   * Returns whether an interaction is likely to have come from the user clicking the `label` of\n   * an `input` or `textarea` in order to focus it.\n   * @param focusEventTarget Target currently receiving focus.\n   */\n  private _isLastInteractionFromInputLabel(focusEventTarget: HTMLElement): boolean {\n    const {_mostRecentTarget: mostRecentTarget, mostRecentModality} = this._inputModalityDetector;\n\n    // If the last interaction used the mouse on an element contained by one of the labels\n    // of an `input`/`textarea` that is currently focused, it is very likely that the\n    // user redirected focus using the label.\n    if (\n      mostRecentModality !== 'mouse' ||\n      !mostRecentTarget ||\n      mostRecentTarget === focusEventTarget ||\n      (focusEventTarget.nodeName !== 'INPUT' && focusEventTarget.nodeName !== 'TEXTAREA') ||\n      (focusEventTarget as HTMLInputElement | HTMLTextAreaElement).disabled\n    ) {\n      return false;\n    }\n\n    const labels = (focusEventTarget as HTMLInputElement | HTMLTextAreaElement).labels;\n\n    if (labels) {\n      for (let i = 0; i < labels.length; i++) {\n        if (labels[i].contains(mostRecentTarget)) {\n          return true;\n        }\n      }\n    }\n\n    return false;\n  }\n}\n\n/**\n * Directive that determines how a particular element was focused (via keyboard, mouse, touch, or\n * programmatically) and adds corresponding classes to the element.\n *\n * There are two variants of this directive:\n * 1) cdkMonitorElementFocus: does not consider an element to be focused if one of its children is\n *    focused.\n * 2) cdkMonitorSubtreeFocus: considers an element focused if it or any of its children are focused.\n */\n@Directive({\n  selector: '[cdkMonitorElementFocus], [cdkMonitorSubtreeFocus]',\n  exportAs: 'cdkMonitorFocus',\n})\nexport class CdkMonitorFocus implements AfterViewInit, OnDestroy {\n  private _elementRef = inject<ElementRef<HTMLElement>>(ElementRef);\n  private _focusMonitor = inject(FocusMonitor);\n\n  private _monitorSubscription: Subscription;\n  private _focusOrigin: FocusOrigin = null;\n\n  @Output() readonly cdkFocusChange = new EventEmitter<FocusOrigin>();\n\n  constructor(...args: unknown[]);\n  constructor() {}\n\n  get focusOrigin(): FocusOrigin {\n    return this._focusOrigin;\n  }\n\n  ngAfterViewInit() {\n    const element = this._elementRef.nativeElement;\n    this._monitorSubscription = this._focusMonitor\n      .monitor(element, element.nodeType === 1 && element.hasAttribute('cdkMonitorSubtreeFocus'))\n      .subscribe(origin => {\n        this._focusOrigin = origin;\n        this.cdkFocusChange.emit(origin);\n      });\n  }\n\n  ngOnDestroy() {\n    this._focusMonitor.stopMonitoring(this._elementRef);\n\n    if (this._monitorSubscription) {\n      this._monitorSubscription.unsubscribe();\n    }\n  }\n}\n"], "names": ["observableOf"], "mappings": ";;;;;;;;;;;;;AAqCA;;;AAGG;MACU,+BAA+B,GAAG,IAAI,cAAc,CAC/D,qCAAqC;AAGvC;;;;;;;;;;;;;;;AAeG;AACU,MAAA,uCAAuC,GAAiC;IACnF,UAAU,EAAE,CAAC,GAAG,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,CAAC;;AAGnD;;;;;;AAMG;AACI,MAAM,eAAe,GAAG,GAAG;AAElC;;;AAGG;AACH,MAAM,4BAA4B,GAAG;AACnC,IAAA,OAAO,EAAE,IAAI;AACb,IAAA,OAAO,EAAE,IAAI;CACd;AAED;;;;;;;;;;;;;AAaG;MAEU,qBAAqB,CAAA;AACf,IAAA,SAAS,GAAG,MAAM,CAAC,QAAQ,CAAC;AAC5B,IAAA,iBAAiB;;AAGzB,IAAA,gBAAgB;;AAGhB,IAAA,eAAe;;AAGxB,IAAA,IAAI,kBAAkB,GAAA;AACpB,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK;;AAG7B;;;AAGG;IACH,iBAAiB,GAAuB,IAAI;;AAG3B,IAAA,SAAS,GAAG,IAAI,eAAe,CAAgB,IAAI,CAAC;;AAGpD,IAAA,QAAQ;AAEzB;;;AAGG;IACK,YAAY,GAAG,CAAC;AAExB;;;AAGG;AACK,IAAA,UAAU,GAAG,CAAC,KAAoB,KAAI;;;AAG5C,QAAA,IAAI,IAAI,CAAC,QAAQ,EAAE,UAAU,EAAE,IAAI,CAAC,OAAO,IAAI,OAAO,KAAK,KAAK,CAAC,OAAO,CAAC,EAAE;YACzE;;AAGF,QAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC;AAC/B,QAAA,IAAI,CAAC,iBAAiB,GAAG,eAAe,CAAC,KAAK,CAAC;AACjD,KAAC;AAED;;;AAGG;AACK,IAAA,YAAY,GAAG,CAAC,KAAiB,KAAI;;;;QAI3C,IAAI,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,YAAY,GAAG,eAAe,EAAE;YACpD;;;;AAKF,QAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,+BAA+B,CAAC,KAAK,CAAC,GAAG,UAAU,GAAG,OAAO,CAAC;AAClF,QAAA,IAAI,CAAC,iBAAiB,GAAG,eAAe,CAAC,KAAK,CAAC;AACjD,KAAC;AAED;;;AAGG;AACK,IAAA,aAAa,GAAG,CAAC,KAAiB,KAAI;;;AAG5C,QAAA,IAAI,gCAAgC,CAAC,KAAK,CAAC,EAAE;AAC3C,YAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC;YAC/B;;;;AAKF,QAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,GAAG,EAAE;AAE9B,QAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC;AAC5B,QAAA,IAAI,CAAC,iBAAiB,GAAG,eAAe,CAAC,KAAK,CAAC;AACjD,KAAC;AAID,IAAA,WAAA,GAAA;AACE,QAAA,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;AAC7B,QAAA,MAAM,QAAQ,GAAG,MAAM,CAAW,QAAQ,CAAC;AAC3C,QAAA,MAAM,OAAO,GAAG,MAAM,CAAC,+BAA+B,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;QAEzE,IAAI,CAAC,QAAQ,GAAG;AACd,YAAA,GAAG,uCAAuC;AAC1C,YAAA,GAAG,OAAO;SACX;;AAGD,QAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AACpD,QAAA,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,oBAAoB,EAAE,CAAC;;;AAIzE,QAAA,IAAI,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE;AAC5B,YAAA,MAAM,QAAQ,GAAG,MAAM,CAAC,gBAAgB,CAAC,CAAC,cAAc,CAAC,IAAI,EAAE,IAAI,CAAC;YAEpE,IAAI,CAAC,iBAAiB,GAAG,MAAM,CAAC,iBAAiB,CAAC,MAAK;gBACrD,OAAO;AACL,oBAAA,qBAAqB,CACnB,QAAQ,EACR,QAAQ,EACR,SAAS,EACT,IAAI,CAAC,UAAU,EACf,4BAA4B,CAC7B;AACD,oBAAA,qBAAqB,CACnB,QAAQ,EACR,QAAQ,EACR,WAAW,EACX,IAAI,CAAC,YAAY,EACjB,4BAA4B,CAC7B;AACD,oBAAA,qBAAqB,CACnB,QAAQ,EACR,QAAQ,EACR,YAAY,EACZ,IAAI,CAAC,aAAa,EAClB,4BAA4B,CAC7B;iBACF;AACH,aAAC,CAAC;;;IAIN,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE;AACzB,QAAA,IAAI,CAAC,iBAAiB,EAAE,OAAO,CAAC,OAAO,IAAI,OAAO,EAAE,CAAC;;uGAzI5C,qBAAqB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA;AAArB,IAAA,OAAA,KAAA,GAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,qBAAqB,cADT,MAAM,EAAA,CAAA;;2FAClB,qBAAqB,EAAA,UAAA,EAAA,CAAA;kBADjC,UAAU;mBAAC,EAAC,UAAU,EAAE,MAAM,EAAC;;;ACtDhC;IACY;AAAZ,CAAA,UAAY,yBAAyB,EAAA;AACnC;;;;AAIG;AACH,IAAA,yBAAA,CAAA,yBAAA,CAAA,WAAA,CAAA,GAAA,CAAA,CAAA,GAAA,WAAS;AACT;;;AAGG;AACH,IAAA,yBAAA,CAAA,yBAAA,CAAA,UAAA,CAAA,GAAA,CAAA,CAAA,GAAA,UAAQ;AACV,CAAC,EAZW,yBAAyB,KAAzB,yBAAyB,GAYpC,EAAA,CAAA,CAAA;AAOD;MACa,6BAA6B,GAAG,IAAI,cAAc,CAC7D,mCAAmC;AASrC;;;AAGG;AACH,MAAM,2BAA2B,GAAG,+BAA+B,CAAC;AAClE,IAAA,OAAO,EAAE,IAAI;AACb,IAAA,OAAO,EAAE,IAAI;AACd,CAAA,CAAC;AAEF;MAEa,YAAY,CAAA;AACf,IAAA,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC;AACxB,IAAA,SAAS,GAAG,MAAM,CAAC,QAAQ,CAAC;AACnB,IAAA,sBAAsB,GAAG,MAAM,CAAC,qBAAqB,CAAC;;IAG/D,OAAO,GAAgB,IAAI;;AAG3B,IAAA,gBAAgB;;IAGhB,cAAc,GAAG,KAAK;;AAGtB,IAAA,qBAAqB;;AAGrB,IAAA,gBAAgB;AAExB;;;AAGG;IACK,2BAA2B,GAAG,KAAK;;AAGnC,IAAA,YAAY,GAAG,IAAI,GAAG,EAAqC;;IAG3D,sBAAsB,GAAG,CAAC;AAElC;;;;;AAKG;AACK,IAAA,2BAA2B,GAAG,IAAI,GAAG,EAA+C;AAE5F;;;AAGG;AACc,IAAA,cAAc;AAE/B;;;AAGG;IACK,oBAAoB,GAAG,MAAK;;;AAGlC,QAAA,IAAI,CAAC,cAAc,GAAG,IAAI;AAC1B,QAAA,IAAI,CAAC,qBAAqB,GAAG,UAAU,CAAC,OAAO,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC,CAAC;AAC9E,KAAC;;IAGS,SAAS,GAAI,MAAM,CAAC,QAAQ,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;;AAGxC,IAAA,0BAA0B,GAAG,IAAI,OAAO,EAAQ;AAIjE,IAAA,WAAA,GAAA;AACE,QAAA,MAAM,OAAO,GAAG,MAAM,CAA6B,6BAA6B,EAAE;AAChF,YAAA,QAAQ,EAAE,IAAI;AACf,SAAA,CAAC;QAEF,IAAI,CAAC,cAAc,GAAG,OAAO,EAAE,aAAa,IAAI,yBAAyB,CAAC,SAAS;;AAErF;;;AAGG;AACK,IAAA,6BAA6B,GAAG,CAAC,KAAY,KAAI;AACvD,QAAA,MAAM,MAAM,GAAG,eAAe,CAAc,KAAK,CAAC;;AAGlD,QAAA,KAAK,IAAI,OAAO,GAAG,MAAM,EAAE,OAAO,EAAE,OAAO,GAAG,OAAO,CAAC,aAAa,EAAE;AACnE,YAAA,IAAI,KAAK,CAAC,IAAI,KAAK,OAAO,EAAE;AAC1B,gBAAA,IAAI,CAAC,QAAQ,CAAC,KAAmB,EAAE,OAAO,CAAC;;iBACtC;AACL,gBAAA,IAAI,CAAC,OAAO,CAAC,KAAmB,EAAE,OAAO,CAAC;;;AAGhD,KAAC;AAoBD,IAAA,OAAO,CACL,OAA8C,EAC9C,aAAA,GAAyB,KAAK,EAAA;AAE9B,QAAA,MAAM,aAAa,GAAG,aAAa,CAAC,OAAO,CAAC;;AAG5C,QAAA,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,IAAI,aAAa,CAAC,QAAQ,KAAK,CAAC,EAAE;;YAE7D,OAAOA,EAAY,EAAE;;;;;QAMvB,MAAM,QAAQ,GAAG,cAAc,CAAC,aAAa,CAAC,IAAI,IAAI,CAAC,YAAY,EAAE;QACrE,MAAM,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,aAAa,CAAC;;QAGvD,IAAI,UAAU,EAAE;YACd,IAAI,aAAa,EAAE;;;;AAIjB,gBAAA,UAAU,CAAC,aAAa,GAAG,IAAI;;YAGjC,OAAO,UAAU,CAAC,OAAO;;;AAI3B,QAAA,MAAM,IAAI,GAAyB;AACjC,YAAA,aAAa,EAAE,aAAa;YAC5B,OAAO,EAAE,IAAI,OAAO,EAAe;YACnC,QAAQ;SACT;QACD,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,aAAa,EAAE,IAAI,CAAC;AAC1C,QAAA,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC;QAEnC,OAAO,IAAI,CAAC,OAAO;;AAerB,IAAA,cAAc,CAAC,OAA8C,EAAA;AAC3D,QAAA,MAAM,aAAa,GAAG,aAAa,CAAC,OAAO,CAAC;QAC5C,MAAM,WAAW,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,aAAa,CAAC;QAExD,IAAI,WAAW,EAAE;AACf,YAAA,WAAW,CAAC,OAAO,CAAC,QAAQ,EAAE;AAE9B,YAAA,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC;AAC/B,YAAA,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,aAAa,CAAC;AACvC,YAAA,IAAI,CAAC,sBAAsB,CAAC,WAAW,CAAC;;;AAoB5C,IAAA,QAAQ,CACN,OAA8C,EAC9C,MAAmB,EACnB,OAAsB,EAAA;AAEtB,QAAA,MAAM,aAAa,GAAG,aAAa,CAAC,OAAO,CAAC;QAC5C,MAAM,cAAc,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC,aAAa;;;;AAKxD,QAAA,IAAI,aAAa,KAAK,cAAc,EAAE;AACpC,YAAA,IAAI,CAAC,uBAAuB,CAAC,aAAa,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,cAAc,EAAE,IAAI,CAAC,KACzE,IAAI,CAAC,cAAc,CAAC,cAAc,EAAE,MAAM,EAAE,IAAI,CAAC,CAClD;;aACI;AACL,YAAA,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC;;AAGvB,YAAA,IAAI,OAAO,aAAa,CAAC,KAAK,KAAK,UAAU,EAAE;AAC7C,gBAAA,aAAa,CAAC,KAAK,CAAC,OAAO,CAAC;;;;IAKlC,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,OAAO,KAAK,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;;;IAIrE,YAAY,GAAA;AAClB,QAAA,OAAO,IAAI,CAAC,SAAS,IAAI,QAAQ;;;IAI3B,UAAU,GAAA;AAChB,QAAA,MAAM,GAAG,GAAG,IAAI,CAAC,YAAY,EAAE;AAC/B,QAAA,OAAO,GAAG,CAAC,WAAW,IAAI,MAAM;;AAG1B,IAAA,eAAe,CAAC,gBAAoC,EAAA;AAC1D,QAAA,IAAI,IAAI,CAAC,OAAO,EAAE;;;AAGhB,YAAA,IAAI,IAAI,CAAC,2BAA2B,EAAE;AACpC,gBAAA,OAAO,IAAI,CAAC,0BAA0B,CAAC,gBAAgB,CAAC,GAAG,OAAO,GAAG,SAAS;;iBACzE;gBACL,OAAO,IAAI,CAAC,OAAO;;;;;;;;;;;;QAavB,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,gBAAgB,EAAE;YAChD,OAAO,IAAI,CAAC,gBAAgB;;;;;;QAO9B,IAAI,gBAAgB,IAAI,IAAI,CAAC,gCAAgC,CAAC,gBAAgB,CAAC,EAAE;AAC/E,YAAA,OAAO,OAAO;;AAGhB,QAAA,OAAO,SAAS;;AAGlB;;;;;;;AAOG;AACK,IAAA,0BAA0B,CAAC,gBAAoC,EAAA;;;;;;;;;;;AAWrE,QAAA,QACE,IAAI,CAAC,cAAc,KAAK,yBAAyB,CAAC,QAAQ;AAC1D,YAAA,CAAC,CAAC,gBAAgB,EAAE,QAAQ,CAAC,IAAI,CAAC,sBAAsB,CAAC,iBAAiB,CAAC;;AAI/E;;;;AAIG;IACK,WAAW,CAAC,OAAoB,EAAE,MAAoB,EAAA;QAC5D,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,aAAa,EAAE,CAAC,CAAC,MAAM,CAAC;QACjD,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,mBAAmB,EAAE,MAAM,KAAK,OAAO,CAAC;QACjE,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,sBAAsB,EAAE,MAAM,KAAK,UAAU,CAAC;QACvE,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,mBAAmB,EAAE,MAAM,KAAK,OAAO,CAAC;QACjE,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,qBAAqB,EAAE,MAAM,KAAK,SAAS,CAAC;;AAGvE;;;;;;AAMG;AACK,IAAA,UAAU,CAAC,MAAmB,EAAE,iBAAiB,GAAG,KAAK,EAAA;AAC/D,QAAA,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,MAAK;AAClC,YAAA,IAAI,CAAC,OAAO,GAAG,MAAM;YACrB,IAAI,CAAC,2BAA2B,GAAG,MAAM,KAAK,OAAO,IAAI,iBAAiB;;;;;;YAO1E,IAAI,IAAI,CAAC,cAAc,KAAK,yBAAyB,CAAC,SAAS,EAAE;AAC/D,gBAAA,YAAY,CAAC,IAAI,CAAC,gBAAgB,CAAC;AACnC,gBAAA,MAAM,EAAE,GAAG,IAAI,CAAC,2BAA2B,GAAG,eAAe,GAAG,CAAC;AACjE,gBAAA,IAAI,CAAC,gBAAgB,GAAG,UAAU,CAAC,OAAO,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC;;AAEvE,SAAC,CAAC;;AAGJ;;;;AAIG;IACK,QAAQ,CAAC,KAAiB,EAAE,OAAoB,EAAA;;;;;;;QAQtD,MAAM,WAAW,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC;AAClD,QAAA,MAAM,gBAAgB,GAAG,eAAe,CAAc,KAAK,CAAC;AAC5D,QAAA,IAAI,CAAC,WAAW,KAAK,CAAC,WAAW,CAAC,aAAa,IAAI,OAAO,KAAK,gBAAgB,CAAC,EAAE;YAChF;;AAGF,QAAA,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,IAAI,CAAC,eAAe,CAAC,gBAAgB,CAAC,EAAE,WAAW,CAAC;;AAGnF;;;;AAIG;IACH,OAAO,CAAC,KAAiB,EAAE,OAAoB,EAAA;;;QAG7C,MAAM,WAAW,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC;AAElD,QAAA,IACE,CAAC,WAAW;aACX,WAAW,CAAC,aAAa;gBACxB,KAAK,CAAC,aAAa,YAAY,IAAI;gBACnC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC,EACxC;YACA;;AAGF,QAAA,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC;AACzB,QAAA,IAAI,CAAC,WAAW,CAAC,WAAW,EAAE,IAAI,CAAC;;IAG7B,WAAW,CAAC,IAA0B,EAAE,MAAmB,EAAA;QACjE,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,MAAM,EAAE;AACjC,YAAA,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;;;AAI7C,IAAA,wBAAwB,CAAC,WAAiC,EAAA;AAChE,QAAA,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE;YAC7B;;AAGF,QAAA,MAAM,QAAQ,GAAG,WAAW,CAAC,QAAQ;AACrC,QAAA,MAAM,sBAAsB,GAAG,IAAI,CAAC,2BAA2B,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC;QAElF,IAAI,CAAC,sBAAsB,EAAE;AAC3B,YAAA,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,MAAK;gBAClC,QAAQ,CAAC,gBAAgB,CACvB,OAAO,EACP,IAAI,CAAC,6BAA6B,EAClC,2BAA2B,CAC5B;gBACD,QAAQ,CAAC,gBAAgB,CACvB,MAAM,EACN,IAAI,CAAC,6BAA6B,EAClC,2BAA2B,CAC5B;AACH,aAAC,CAAC;;QAGJ,IAAI,CAAC,2BAA2B,CAAC,GAAG,CAAC,QAAQ,EAAE,sBAAsB,GAAG,CAAC,CAAC;;AAG1E,QAAA,IAAI,EAAE,IAAI,CAAC,sBAAsB,KAAK,CAAC,EAAE;;;AAGvC,YAAA,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,MAAK;AAClC,gBAAA,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,EAAE;gBAChC,MAAM,CAAC,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAC,oBAAoB,CAAC;AAC7D,aAAC,CAAC;;YAGF,IAAI,CAAC,sBAAsB,CAAC;AACzB,iBAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,0BAA0B,CAAC;iBAC/C,SAAS,CAAC,QAAQ,IAAG;gBACpB,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,IAAI,yBAAyB;AACzD,aAAC,CAAC;;;AAIA,IAAA,sBAAsB,CAAC,WAAiC,EAAA;AAC9D,QAAA,MAAM,QAAQ,GAAG,WAAW,CAAC,QAAQ;QAErC,IAAI,IAAI,CAAC,2BAA2B,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE;YAClD,MAAM,sBAAsB,GAAG,IAAI,CAAC,2BAA2B,CAAC,GAAG,CAAC,QAAQ,CAAE;AAE9E,YAAA,IAAI,sBAAsB,GAAG,CAAC,EAAE;gBAC9B,IAAI,CAAC,2BAA2B,CAAC,GAAG,CAAC,QAAQ,EAAE,sBAAsB,GAAG,CAAC,CAAC;;iBACrE;gBACL,QAAQ,CAAC,mBAAmB,CAC1B,OAAO,EACP,IAAI,CAAC,6BAA6B,EAClC,2BAA2B,CAC5B;gBACD,QAAQ,CAAC,mBAAmB,CAC1B,MAAM,EACN,IAAI,CAAC,6BAA6B,EAClC,2BAA2B,CAC5B;AACD,gBAAA,IAAI,CAAC,2BAA2B,CAAC,MAAM,CAAC,QAAQ,CAAC;;;;AAKrD,QAAA,IAAI,CAAC,EAAE,IAAI,CAAC,sBAAsB,EAAE;AAClC,YAAA,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,EAAE;YAChC,MAAM,CAAC,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAAC,oBAAoB,CAAC;;AAG9D,YAAA,IAAI,CAAC,0BAA0B,CAAC,IAAI,EAAE;;AAGtC,YAAA,YAAY,CAAC,IAAI,CAAC,qBAAqB,CAAC;AACxC,YAAA,YAAY,CAAC,IAAI,CAAC,gBAAgB,CAAC;;;;AAK/B,IAAA,cAAc,CACpB,OAAoB,EACpB,MAAmB,EACnB,WAAiC,EAAA;AAEjC,QAAA,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,MAAM,CAAC;AACjC,QAAA,IAAI,CAAC,WAAW,CAAC,WAAW,EAAE,MAAM,CAAC;AACrC,QAAA,IAAI,CAAC,gBAAgB,GAAG,MAAM;;AAGhC;;;;AAIG;AACK,IAAA,uBAAuB,CAAC,OAAoB,EAAA;QAClD,MAAM,OAAO,GAA0C,EAAE;QAEzD,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,cAAc,KAAI;AACjD,YAAA,IAAI,cAAc,KAAK,OAAO,KAAK,IAAI,CAAC,aAAa,IAAI,cAAc,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,EAAE;gBAC1F,OAAO,CAAC,IAAI,CAAC,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC;;AAExC,SAAC,CAAC;AAEF,QAAA,OAAO,OAAO;;AAGhB;;;;AAIG;AACK,IAAA,gCAAgC,CAAC,gBAA6B,EAAA;QACpE,MAAM,EAAC,iBAAiB,EAAE,gBAAgB,EAAE,kBAAkB,EAAC,GAAG,IAAI,CAAC,sBAAsB;;;;QAK7F,IACE,kBAAkB,KAAK,OAAO;AAC9B,YAAA,CAAC,gBAAgB;AACjB,YAAA,gBAAgB,KAAK,gBAAgB;aACpC,gBAAgB,CAAC,QAAQ,KAAK,OAAO,IAAI,gBAAgB,CAAC,QAAQ,KAAK,UAAU,CAAC;YAClF,gBAA2D,CAAC,QAAQ,EACrE;AACA,YAAA,OAAO,KAAK;;AAGd,QAAA,MAAM,MAAM,GAAI,gBAA2D,CAAC,MAAM;QAElF,IAAI,MAAM,EAAE;AACV,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBACtC,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,gBAAgB,CAAC,EAAE;AACxC,oBAAA,OAAO,IAAI;;;;AAKjB,QAAA,OAAO,KAAK;;uGAtgBH,YAAY,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA;AAAZ,IAAA,OAAA,KAAA,GAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,YAAY,cADA,MAAM,EAAA,CAAA;;2FAClB,YAAY,EAAA,UAAA,EAAA,CAAA;kBADxB,UAAU;mBAAC,EAAC,UAAU,EAAE,MAAM,EAAC;;AA2gBhC;;;;;;;;AAQG;MAKU,eAAe,CAAA;AAClB,IAAA,WAAW,GAAG,MAAM,CAA0B,UAAU,CAAC;AACzD,IAAA,aAAa,GAAG,MAAM,CAAC,YAAY,CAAC;AAEpC,IAAA,oBAAoB;IACpB,YAAY,GAAgB,IAAI;AAErB,IAAA,cAAc,GAAG,IAAI,YAAY,EAAe;AAGnE,IAAA,WAAA,GAAA;AAEA,IAAA,IAAI,WAAW,GAAA;QACb,OAAO,IAAI,CAAC,YAAY;;IAG1B,eAAe,GAAA;AACb,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa;AAC9C,QAAA,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;AAC9B,aAAA,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC,QAAQ,KAAK,CAAC,IAAI,OAAO,CAAC,YAAY,CAAC,wBAAwB,CAAC;aACzF,SAAS,CAAC,MAAM,IAAG;AAClB,YAAA,IAAI,CAAC,YAAY,GAAG,MAAM;AAC1B,YAAA,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC;AAClC,SAAC,CAAC;;IAGN,WAAW,GAAA;QACT,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,IAAI,CAAC,WAAW,CAAC;AAEnD,QAAA,IAAI,IAAI,CAAC,oBAAoB,EAAE;AAC7B,YAAA,IAAI,CAAC,oBAAoB,CAAC,WAAW,EAAE;;;uGA9BhC,eAAe,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAf,eAAe,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,oDAAA,EAAA,OAAA,EAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,EAAA,QAAA,EAAA,CAAA,iBAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAAf,eAAe,EAAA,UAAA,EAAA,CAAA;kBAJ3B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,oDAAoD;AAC9D,oBAAA,QAAQ,EAAE,iBAAiB;AAC5B,iBAAA;wDAQoB,cAAc,EAAA,CAAA;sBAAhC;;;;;"}