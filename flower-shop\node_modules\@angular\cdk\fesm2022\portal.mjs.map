{"version": 3, "file": "portal.mjs", "sources": ["../../../../../k8-fastbuild-ST-46c76129e412/bin/src/cdk/portal/portal-injector.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {Injector} from '@angular/core';\n\n/**\n * Custom injector to be used when providing custom\n * injection tokens to components inside a portal.\n * @docs-private\n * @deprecated Use `Injector.create` instead.\n * @breaking-change 11.0.0\n */\nexport class PortalInjector implements Injector {\n  constructor(\n    private _parentInjector: Injector,\n    private _customTokens: WeakMap<any, any>,\n  ) {}\n\n  get(token: any, notFoundValue?: any): any {\n    const value = this._customTokens.get(token);\n\n    if (typeof value !== 'undefined') {\n      return value;\n    }\n\n    return this._parentInjector.get<any>(token, notFoundValue);\n  }\n}\n"], "names": [], "mappings": ";;;;AAUA;;;;;;AAMG;MACU,cAAc,CAAA;AAEf,IAAA,eAAA;AACA,IAAA,aAAA;IAFV,WACU,CAAA,eAAyB,EACzB,aAAgC,EAAA;QADhC,IAAe,CAAA,eAAA,GAAf,eAAe;QACf,IAAa,CAAA,aAAA,GAAb,aAAa;;IAGvB,GAAG,CAAC,KAAU,EAAE,aAAmB,EAAA;QACjC,MAAM,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,KAAK,CAAC;AAE3C,QAAA,IAAI,OAAO,KAAK,KAAK,WAAW,EAAE;AAChC,YAAA,OAAO,KAAK;;QAGd,OAAO,IAAI,CAAC,eAAe,CAAC,GAAG,CAAM,KAAK,EAAE,aAAa,CAAC;;AAE7D;;;;"}