/* Hero Section */
.hero-section {
    background: linear-gradient(135deg, var(--primary-pink) 0%, var(--soft-lavender) 100%);
    padding: 4rem 0;
    text-align: center;
}

.hero-content {
    max-width: 800px;
    margin: 0 auto;
}

.hero-title {
    font-size: 3rem;
    font-weight: 700;
    color: var(--text-dark);
    margin-bottom: 1rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.hero-subtitle {
    font-size: 1.25rem;
    color: var(--text-light);
    margin-bottom: 2rem;
    line-height: 1.6;
}

/* Search and Filters */
.search-filters {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    align-items: center;
    margin-top: 2rem;
}

.search-field {
    width: 100%;
    max-width: 400px;

    .mat-mdc-form-field {
        background: white;
        border-radius: var(--border-radius-md);
        box-shadow: var(--shadow-soft);
    }
}

.category-filters {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    justify-content: center;

    button {
        border-radius: var(--border-radius-sm);
        transition: var(--transition-smooth);

        &.active {
            background: var(--accent-pink);
            color: white;
        }

        &:hover {
            background: rgba(233, 30, 99, 0.1);
        }
    }
}

.clear-filters {
    color: var(--text-light);

    &:hover {
        color: var(--accent-pink);
    }
}

/* Flowers Section */
.flowers-section {
    padding: 4rem 0;
}

.flowers-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.flower-card {
    position: relative;
    overflow: hidden;
    border-radius: var(--border-radius-md);
    transition: var(--transition-smooth);

    &:hover {
        transform: translateY(-5px);
        box-shadow: var(--shadow-strong);

        .flower-overlay {
            opacity: 1;
        }
    }
}

.flower-image-container {
    position: relative;
    overflow: hidden;
    height: 250px;
}

.flower-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition-smooth);
}

.flower-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: var(--transition-smooth);
}

.add-to-cart-btn {
    background: var(--accent-pink);
    color: white;

    &:hover {
        background: #ad1457;
        transform: scale(1.1);
    }
}

.flower-info {
    padding: 1rem;
}

.flower-name {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 0.5rem;
}

.flower-category {
    font-size: 0.875rem;
    color: var(--accent-pink);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 0.5rem;
}

.flower-description {
    color: var(--text-light);
    line-height: 1.5;
    margin-bottom: 1rem;
}

.flower-price {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.price {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--accent-pink);
}

.stock-status {
    font-size: 0.875rem;
    padding: 0.25rem 0.5rem;
    border-radius: var(--border-radius-sm);
    font-weight: 500;

    &.in-stock {
        background: rgba(129, 199, 132, 0.1);
        color: var(--sage-green);
    }

    &.out-of-stock {
        background: rgba(244, 67, 54, 0.1);
        color: #f44336;
    }
}

.login-prompt {
    text-align: center;
    color: var(--text-light);
    margin: 1rem 0;

    a {
        color: var(--accent-pink);
        text-decoration: none;
        font-weight: 500;

        &:hover {
            text-decoration: underline;
        }
    }
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 4rem 2rem;
    color: var(--text-light);

    .empty-icon {
        font-size: 4rem;
        width: 4rem;
        height: 4rem;
        color: var(--accent-pink);
        margin-bottom: 1rem;
    }

    h3 {
        font-size: 1.5rem;
        color: var(--text-dark);
        margin-bottom: 0.5rem;
    }

    p {
        margin-bottom: 2rem;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-title {
        font-size: 2rem;
    }

    .hero-subtitle {
        font-size: 1rem;
    }

    .search-filters {
        padding: 0 1rem;
    }

    .flowers-grid {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 1rem;
    }

    .category-filters {
        button {
            font-size: 0.875rem;
            padding: 0.5rem 1rem;
        }
    }
}

@media (max-width: 480px) {
    .hero-section {
        padding: 2rem 0;
    }

    .flowers-grid {
        grid-template-columns: 1fr;
    }

    .search-filters {
        gap: 1rem;
    }

    .category-filters {
        flex-direction: column;
        width: 100%;

        button {
            width: 100%;
        }
    }
}
