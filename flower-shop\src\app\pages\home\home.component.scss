.home-container {
    padding: 20px;
    max-width: 1200px;
    margin: 0 auto;
}

.hero-section {
    text-align: center;
    margin-bottom: 30px;
    padding: 40px 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 8px;

    h1 {
        font-size: 2.5rem;
        margin-bottom: 10px;
        font-weight: 300;
    }

    p {
        font-size: 1.2rem;
        opacity: 0.9;
    }
}

.filters-section {
    margin-bottom: 30px;
}

.filters-card {
    .filters-row {
        display: flex;
        flex-wrap: wrap;
        gap: 16px;
        align-items: center;

        .search-field {
            flex: 2;
            min-width: 200px;
        }

        .category-field {
            flex: 1;
            min-width: 150px;
        }

        .price-filters {
            display: flex;
            gap: 8px;

            .price-field {
                width: 100px;
            }
        }

        .clear-filters-btn {
            margin-left: auto;
        }
    }
}

.flowers-section {
    .flowers-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: 20px;
        margin-bottom: 20px;
    }
}

.flower-card {
    height: 100%;
    display: flex;
    flex-direction: column;
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;

    &:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }

    .flower-image-container {
        position: relative;
        overflow: hidden;

        img {
            width: 100%;
            height: 250px;
            object-fit: cover;
            transition: transform 0.3s ease;
        }

        &:hover img {
            transform: scale(1.05);
        }

        .stock-badge {
            position: absolute;
            top: 10px;
            right: 10px;
            background-color: #4caf50;
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;

            &.out-of-stock {
                background-color: #f44336;
            }
        }
    }

    mat-card-content {
        flex-grow: 1;

        .flower-description {
            color: #666;
            font-size: 14px;
            line-height: 1.4;
            margin-bottom: 16px;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .price-section {
            .price {
                font-size: 1.5rem;
                font-weight: 600;
                color: #2196f3;
            }
        }
    }

    mat-card-actions {
        padding: 16px;
        display: flex;
        justify-content: space-between;
        align-items: center;

        button {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .quantity-display {
            mat-chip {
                background-color: #e8f5e8;
                color: #2e7d32;
            }
        }
    }
}

.no-results {
    display: flex;
    justify-content: center;
    margin-top: 40px;

    mat-card {
        max-width: 400px;
        text-align: center;

        .no-results-content {
            padding: 20px;

            mat-icon {
                font-size: 48px;
                height: 48px;
                width: 48px;
                color: #999;
                margin-bottom: 16px;
            }

            h3 {
                margin-bottom: 8px;
                color: #333;
            }

            p {
                color: #666;
                margin-bottom: 20px;
            }
        }
    }
}

// Responsive design
@media (max-width: 768px) {
    .home-container {
        padding: 10px;
    }

    .hero-section {
        padding: 20px 10px;

        h1 {
            font-size: 2rem;
        }

        p {
            font-size: 1rem;
        }
    }

    .filters-card .filters-row {
        flex-direction: column;
        align-items: stretch;

        .search-field,
        .category-field {
            flex: none;
            width: 100%;
        }

        .price-filters {
            width: 100%;

            .price-field {
                flex: 1;
            }
        }

        .clear-filters-btn {
            margin-left: 0;
            width: 100%;
        }
    }

    .flowers-grid {
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
        gap: 15px;
    }
}
