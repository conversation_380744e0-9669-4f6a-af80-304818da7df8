@use 'sass:map';

/// Adds the error colors to the given palette.
@function _patch-error-palette($palette) {
  @return map.merge(
    $palette,
    (
      error: (
        0: #000000,
        10: #410002,
        20: #690005,
        25: #7e0007,
        30: #93000a,
        35: #a80710,
        40: #ba1a1a,
        50: #de3730,
        60: #ff5449,
        70: #ff897d,
        80: #ffb4ab,
        90: #ffdad6,
        95: #ffedea,
        98: #fff8f7,
        99: #fffbff,
        100: #ffffff,
      ),
    )
  );
}

/// Red color palette to be used as primary or tertiary palette.
$red-palette: _patch-error-palette((
  0: #000000,
  10: #410000,
  20: #690100,
  25: #7e0100,
  30: #930100,
  35: #a90100,
  40: #c00100,
  50: #ef0000,
  60: #ff5540,
  70: #ff8a78,
  80: #ffb4a8,
  90: #ffdad4,
  95: #ffedea,
  98: #fff8f6,
  99: #fffbff,
  100: #ffffff,
  secondary: (
    0: #000000,
    10: #2c1512,
    20: #442925,
    25: #513430,
    30: #5d3f3b,
    35: #6a4b46,
    40: #775651,
    50: #926f69,
    60: #ae8882,
    70: #caa29c,
    80: #e7bdb6,
    90: #ffdad4,
    95: #ffedea,
    98: #fff8f6,
    99: #fffbff,
    100: #ffffff,
  ),
  neutral: (
    0: #000000,
    10: #201a19,
    20: #362f2e,
    25: #413a38,
    30: #4d4544,
    35: #59504f,
    40: #655c5b,
    50: #7f7573,
    60: #998e8d,
    70: #b4a9a7,
    80: #d0c4c2,
    90: #ede0dd,
    95: #fbeeec,
    98: #fff8f6,
    99: #fffbff,
    100: #ffffff,
    4: #130d0c,
    6: #181211,
    12: #251e1d,
    17: #302828,
    22: #3b3332,
    24: #3f3737,
    87: #e4d7d6,
    92: #f3e5e4,
    94: #f9ebe9,
    96: #fef1ef,
  ),
  neutral-variant: (
    0: #000000,
    10: #251917,
    20: #3b2d2b,
    25: #473836,
    30: #534341,
    35: #5f4f4c,
    40: #6c5a58,
    50: #857370,
    60: #a08c89,
    70: #bca7a3,
    80: #d8c2be,
    90: #f5ddda,
    95: #ffedea,
    98: #fff8f6,
    99: #fffbff,
    100: #ffffff,
  ),
));

/// Green color palette to be used as primary or tertiary palette.
$green-palette: _patch-error-palette((
  0: #000000,
  10: #002200,
  20: #013a00,
  25: #014600,
  30: #015300,
  35: #026100,
  40: #026e00,
  50: #038b00,
  60: #03a800,
  70: #03c700,
  80: #02e600,
  90: #77ff61,
  95: #cbffb8,
  98: #edffe1,
  99: #f7ffee,
  100: #ffffff,
  secondary: (
    0: #000000,
    10: #121f0e,
    20: #263422,
    25: #313f2c,
    30: #3c4b37,
    35: #485642,
    40: #54634d,
    50: #6c7b65,
    60: #86957e,
    70: #a0b097,
    80: #bbcbb2,
    90: #d7e8cd,
    95: #e5f6da,
    98: #eeffe3,
    99: #f7ffee,
    100: #ffffff,
  ),
  neutral: (
    0: #000000,
    10: #1a1c18,
    20: #2f312d,
    25: #3a3c38,
    30: #454743,
    35: #51534e,
    40: #5d5f5a,
    50: #767872,
    60: #90918c,
    70: #abaca6,
    80: #c6c7c1,
    90: #e2e3dc,
    95: #f1f1eb,
    98: #f9faf3,
    99: #fcfdf6,
    100: #ffffff,
    4: #0c0f0b,
    6: #121410,
    12: #1e201c,
    17: #282b26,
    22: #333531,
    24: #383a35,
    87: #dadbd3,
    92: #e8e9e1,
    94: #eeeee7,
    96: #f3f4ed,
  ),
  neutral-variant: (
    0: #000000,
    10: #181d15,
    20: #2c3229,
    25: #373d34,
    30: #43483f,
    35: #4e544a,
    40: #5a6056,
    50: #73796e,
    60: #8d9387,
    70: #a7ada1,
    80: #c3c8bc,
    90: #dfe4d7,
    95: #edf3e5,
    98: #f6fbee,
    99: #f9fef1,
    100: #ffffff,
  ),
));

/// Blue color palette to be used as primary or tertiary palette.
$blue-palette: _patch-error-palette((
  0: #000000,
  10: #00006e,
  20: #0001ac,
  25: #0001cd,
  30: #0000ef,
  35: #1a21ff,
  40: #343dff,
  50: #5a64ff,
  60: #7c84ff,
  70: #9da3ff,
  80: #bec2ff,
  90: #e0e0ff,
  95: #f1efff,
  98: #fbf8ff,
  99: #fffbff,
  100: #ffffff,
  secondary: (
    0: #000000,
    10: #191a2c,
    20: #2e2f42,
    25: #393a4d,
    30: #444559,
    35: #505165,
    40: #5c5d72,
    50: #75758b,
    60: #8f8fa6,
    70: #a9a9c1,
    80: #c5c4dd,
    90: #e1e0f9,
    95: #f1efff,
    98: #fbf8ff,
    99: #fffbff,
    100: #ffffff,
  ),
  neutral: (
    0: #000000,
    10: #1b1b1f,
    20: #303034,
    25: #3c3b3f,
    30: #47464a,
    35: #535256,
    40: #5f5e62,
    50: #78767a,
    60: #929094,
    70: #adaaaf,
    80: #c8c5ca,
    90: #e5e1e6,
    95: #f3eff4,
    98: #fcf8fd,
    99: #fffbff,
    100: #ffffff,
    4: #0e0e11,
    6: #131316,
    12: #201f22,
    17: #2a292d,
    22: #353438,
    24: #3a393c,
    87: #dcd9dd,
    92: #ebe7eb,
    94: #f0edf1,
    96: #f6f2f7,
  ),
  neutral-variant: (
    0: #000000,
    10: #1b1b23,
    20: #303038,
    25: #3b3b43,
    30: #46464f,
    35: #52515b,
    40: #5e5d67,
    50: #777680,
    60: #91909a,
    70: #acaab4,
    80: #c7c5d0,
    90: #e4e1ec,
    95: #f2effa,
    98: #fbf8ff,
    99: #fffbff,
    100: #ffffff,
  ),
));

/// Yellow color palette to be used as primary or tertiary palette.
$yellow-palette: _patch-error-palette((
  0: #000000,
  10: #1d1d00,
  20: #323200,
  25: #3e3e00,
  30: #494900,
  35: #555500,
  40: #626200,
  50: #7b7b00,
  60: #969600,
  70: #b1b100,
  80: #cdcd00,
  90: #eaea00,
  95: #f9f900,
  98: #fffeac,
  99: #fffbff,
  100: #ffffff,
  secondary: (
    0: #000000,
    10: #1d1d06,
    20: #323218,
    25: #3d3d22,
    30: #49482d,
    35: #545438,
    40: #606043,
    50: #7a795a,
    60: #949272,
    70: #aead8b,
    80: #cac8a5,
    90: #e7e4bf,
    95: #f5f3cd,
    98: #fefbd5,
    99: #fffbff,
    100: #ffffff,
  ),
  neutral: (
    0: #000000,
    10: #1c1c17,
    20: #31312b,
    25: #3c3c35,
    30: #484741,
    35: #54524c,
    40: #605e58,
    50: #797770,
    60: #939189,
    70: #aeaba3,
    80: #c9c6be,
    90: #e6e2d9,
    95: #f4f0e8,
    98: #fdf9f0,
    99: #fffbff,
    100: #ffffff,
    4: #0f0e0a,
    6: #14140f,
    12: #20201b,
    17: #2b2a25,
    22: #36352f,
    24: #3a3933,
    87: #dddad1,
    92: #ece8df,
    94: #f1ede5,
    96: #f7f3ea,
  ),
  neutral-variant: (
    0: #000000,
    10: #1c1c11,
    20: #313125,
    25: #3d3c2f,
    30: #48473a,
    35: #545345,
    40: #605f51,
    50: #797869,
    60: #939182,
    70: #aeac9b,
    80: #cac7b6,
    90: #e6e3d1,
    95: #f4f1df,
    98: #fdfae7,
    99: #fffbff,
    100: #ffffff,
  ),
));

/// Cyan color palette to be used as primary or tertiary palette.
$cyan-palette: _patch-error-palette((
  0: #000000,
  10: #002020,
  20: #003737,
  25: #004343,
  30: #004f4f,
  35: #005c5c,
  40: #006a6a,
  50: #008585,
  60: #00a1a1,
  70: #00bebe,
  80: #00dddd,
  90: #00fbfb,
  95: #adfffe,
  98: #e2fffe,
  99: #f1fffe,
  100: #ffffff,
  secondary: (
    0: #000000,
    10: #051f1f,
    20: #1b3534,
    25: #27403f,
    30: #324b4b,
    35: #3e5757,
    40: #4a6363,
    50: #627c7b,
    60: #7b9695,
    70: #95b0b0,
    80: #b0cccb,
    90: #cce8e7,
    95: #daf6f5,
    98: #e3fffe,
    99: #f1fffe,
    100: #ffffff,
  ),
  neutral: (
    0: #000000,
    10: #191c1c,
    20: #2d3131,
    25: #383c3c,
    30: #444747,
    35: #4f5353,
    40: #5b5f5f,
    50: #747877,
    60: #8e9191,
    70: #a9acab,
    80: #c4c7c6,
    90: #e0e3e2,
    95: #eff1f0,
    98: #f7faf9,
    99: #fafdfc,
    100: #ffffff,
    4: #0b0f0e,
    6: #101414,
    12: #1c2020,
    17: #272b2a,
    22: #313635,
    24: #363a39,
    87: #d7dbd9,
    92: #e6e9e7,
    94: #ebefed,
    96: #f1f4f3,
  ),
  neutral-variant: (
    0: #000000,
    10: #141d1d,
    20: #293232,
    25: #343d3d,
    30: #3f4948,
    35: #4a5454,
    40: #566060,
    50: #6f7979,
    60: #889392,
    70: #a3adad,
    80: #bec9c8,
    90: #dae5e4,
    95: #e9f3f2,
    98: #f1fbfa,
    99: #f4fefd,
    100: #ffffff,
  ),
));

/// Magenta color palette to be used as primary or tertiary palette.
$magenta-palette: _patch-error-palette((
  0: #000000,
  10: #380038,
  20: #5b005b,
  25: #6e006e,
  30: #810081,
  35: #950094,
  40: #a900a9,
  50: #d200d2,
  60: #fe00fe,
  70: #ff76f6,
  80: #ffabf3,
  90: #ffd7f5,
  95: #ffebf8,
  98: #fff7f9,
  99: #fffbff,
  100: #ffffff,
  secondary: (
    0: #000000,
    10: #271624,
    20: #3d2b3a,
    25: #493545,
    30: #554151,
    35: #614c5d,
    40: #6e5869,
    50: #877082,
    60: #a2899c,
    70: #bea4b7,
    80: #dabfd2,
    90: #f7daef,
    95: #ffebf8,
    98: #fff7f9,
    99: #fffbff,
    100: #ffffff,
  ),
  neutral: (
    0: #000000,
    10: #1e1a1d,
    20: #342f32,
    25: #3f3a3d,
    30: #4b4548,
    35: #575154,
    40: #635d60,
    50: #7c7579,
    60: #968f92,
    70: #b1a9ad,
    80: #cdc4c8,
    90: #e9e0e4,
    95: #f8eef2,
    98: #fff7f9,
    99: #fffbff,
    100: #ffffff,
    4: #110d10,
    6: #161215,
    12: #231e22,
    17: #2d292c,
    22: #383337,
    24: #3d383b,
    87: #e1d7dc,
    92: #efe6ea,
    94: #f5ebf0,
    96: #fbf1f5,
  ),
  neutral-variant: (
    0: #000000,
    10: #21191f,
    20: #372e34,
    25: #423940,
    30: #4e444b,
    35: #5a4f57,
    40: #665b63,
    50: #80747c,
    60: #9a8d95,
    70: #b5a7b0,
    80: #d1c2cb,
    90: #eedee7,
    95: #fcecf5,
    98: #fff7f9,
    99: #fffbff,
    100: #ffffff,
  ),
));

/// Orange color palette to be used as primary or tertiary palette.
$orange-palette: _patch-error-palette((
  0: #000000,
  10: #311300,
  20: #502400,
  25: #612d00,
  30: #723600,
  35: #843f00,
  40: #964900,
  50: #bc5d00,
  60: #e37100,
  70: #ff8e36,
  80: #ffb787,
  90: #ffdcc7,
  95: #ffede4,
  98: #fff8f5,
  99: #fffbff,
  100: #ffffff,
  secondary: (
    0: #000000,
    10: #2b1708,
    20: #422b1b,
    25: #4f3625,
    30: #5b4130,
    35: #684c3b,
    40: #755846,
    50: #90715d,
    60: #ab8a75,
    70: #c8a48e,
    80: #e5bfa8,
    90: #ffdcc7,
    95: #ffede4,
    98: #fff8f5,
    99: #fffbff,
    100: #ffffff,
  ),
  neutral: (
    0: #000000,
    10: #201a17,
    20: #362f2b,
    25: #413a36,
    30: #4d4541,
    35: #59514d,
    40: #655d58,
    50: #7e7571,
    60: #998f8a,
    70: #b4a9a4,
    80: #d0c4bf,
    90: #ece0da,
    95: #fbeee8,
    98: #fff8f5,
    99: #fffbff,
    100: #ffffff,
    4: #120d0b,
    6: #181210,
    12: #241e1b,
    17: #2f2926,
    22: #3a3330,
    24: #3f3834,
    87: #e3d8d3,
    92: #f2e6e1,
    94: #f8ebe6,
    96: #fef1ec,
  ),
  neutral-variant: (
    0: #000000,
    10: #241912,
    20: #3a2e26,
    25: #463931,
    30: #52443c,
    35: #5e4f47,
    40: #6b5b52,
    50: #84746a,
    60: #9f8d83,
    70: #baa79d,
    80: #d7c3b8,
    90: #f4ded3,
    95: #ffede4,
    98: #fff8f5,
    99: #fffbff,
    100: #ffffff,
  ),
));

/// Chartreuse color palette to be used as primary or tertiary palette.
$chartreuse-palette: _patch-error-palette((
  0: #000000,
  10: #0b2000,
  20: #173800,
  25: #1e4400,
  30: #245100,
  35: #2b5e00,
  40: #326b00,
  50: #418700,
  60: #50a400,
  70: #60c100,
  80: #70e000,
  90: #82ff10,
  95: #cfffa9,
  98: #eeffdc,
  99: #f8ffeb,
  100: #ffffff,
  secondary: (
    0: #000000,
    10: #141e0c,
    20: #293420,
    25: #333f2a,
    30: #3f4a35,
    35: #4a5640,
    40: #56624b,
    50: #6f7b62,
    60: #88957b,
    70: #a2b094,
    80: #becbaf,
    90: #dae7ca,
    95: #e8f5d7,
    98: #f0fee0,
    99: #f8ffeb,
    100: #ffffff,
  ),
  neutral: (
    0: #000000,
    10: #1a1c18,
    20: #2f312c,
    25: #3a3c37,
    30: #464742,
    35: #52534e,
    40: #5e5f5a,
    50: #767872,
    60: #90918b,
    70: #abaca5,
    80: #c7c7c0,
    90: #e3e3dc,
    95: #f1f1ea,
    98: #fafaf2,
    99: #fdfdf5,
    100: #ffffff,
    4: #0c0f0b,
    6: #121410,
    12: #1e201c,
    17: #282b26,
    22: #333531,
    24: #383a35,
    87: #dadbd3,
    92: #e8e9e1,
    94: #eeeee7,
    96: #f3f4ed,
  ),
  neutral-variant: (
    0: #000000,
    10: #181d14,
    20: #2d3228,
    25: #383d33,
    30: #44483e,
    35: #4f5449,
    40: #5b6055,
    50: #74796d,
    60: #8e9286,
    70: #a8ada0,
    80: #c4c8bb,
    90: #e0e4d6,
    95: #eef2e4,
    98: #f7fbec,
    99: #fafeef,
    100: #ffffff,
  ),
));

/// Spring Green color palette to be used as primary or tertiary palette.
$spring-green-palette: _patch-error-palette((
  0: #000000,
  10: #00210b,
  20: #003917,
  25: #00461e,
  30: #005225,
  35: #00602c,
  40: #006d33,
  50: #008942,
  60: #00a751,
  70: #00c561,
  80: #00e472,
  90: #63ff94,
  95: #c4ffcb,
  98: #eaffe9,
  99: #f5fff2,
  100: #ffffff,
  secondary: (
    0: #000000,
    10: #0e1f12,
    20: #233425,
    25: #2e4030,
    30: #394b3b,
    35: #445746,
    40: #506352,
    50: #697c6a,
    60: #829682,
    70: #9cb19c,
    80: #b7ccb7,
    90: #d3e8d2,
    95: #e1f6e0,
    98: #eaffe9,
    99: #f5fff2,
    100: #ffffff,
  ),
  neutral: (
    0: #000000,
    10: #191c19,
    20: #2e312e,
    25: #393c39,
    30: #454744,
    35: #51534f,
    40: #5d5f5b,
    50: #757873,
    60: #8f918d,
    70: #aaaca7,
    80: #c5c7c2,
    90: #e2e3de,
    95: #f0f1ec,
    98: #f9faf4,
    99: #fcfdf7,
    100: #ffffff,
    4: #0c0f0c,
    6: #111411,
    12: #1d201d,
    17: #282b27,
    22: #323632,
    24: #373a36,
    87: #d9dbd5,
    92: #e7e9e3,
    94: #edefe8,
    96: #f2f4ee,
  ),
  neutral-variant: (
    0: #000000,
    10: #161d17,
    20: #2b322b,
    25: #363d36,
    30: #414941,
    35: #4d544c,
    40: #596058,
    50: #717970,
    60: #8b9389,
    70: #a6ada4,
    80: #c1c9be,
    90: #dde5da,
    95: #ebf3e8,
    98: #f4fcf0,
    99: #f7fef3,
    100: #ffffff,
  ),
));

/// Azure color palette to be used as primary or tertiary palette.
$azure-palette: _patch-error-palette((
  0: #000000,
  10: #001b3f,
  20: #002f65,
  25: #003a7a,
  30: #00458f,
  35: #0050a5,
  40: #005cbb,
  50: #0074e9,
  60: #438fff,
  70: #7cabff,
  80: #abc7ff,
  90: #d7e3ff,
  95: #ecf0ff,
  98: #f9f9ff,
  99: #fdfbff,
  100: #ffffff,
  secondary: (
    0: #000000,
    10: #131c2b,
    20: #283041,
    25: #333c4d,
    30: #3e4759,
    35: #4a5365,
    40: #565e71,
    50: #6f778b,
    60: #8891a5,
    70: #a3abc0,
    80: #bec6dc,
    90: #dae2f9,
    95: #ecf0ff,
    98: #f9f9ff,
    99: #fdfbff,
    100: #ffffff,
  ),
  neutral: (
    0: #000000,
    10: #1a1b1f,
    20: #2f3033,
    25: #3b3b3f,
    30: #46464a,
    35: #525256,
    40: #5e5e62,
    50: #77777a,
    60: #919094,
    70: #ababaf,
    80: #c7c6ca,
    90: #e3e2e6,
    95: #f2f0f4,
    98: #faf9fd,
    99: #fdfbff,
    100: #ffffff,
    4: #0d0e11,
    6: #121316,
    12: #1f2022,
    17: #292a2c,
    22: #343537,
    24: #38393c,
    87: #dbd9dd,
    92: #e9e7eb,
    94: #efedf0,
    96: #f4f3f6,
  ),
  neutral-variant: (
    0: #000000,
    10: #181c22,
    20: #2d3038,
    25: #383b43,
    30: #44474e,
    35: #4f525a,
    40: #5b5e66,
    50: #74777f,
    60: #8e9099,
    70: #a9abb4,
    80: #c4c6d0,
    90: #e0e2ec,
    95: #eff0fa,
    98: #f9f9ff,
    99: #fdfbff,
    100: #ffffff,
  ),
));

/// Violet color palette to be used as primary or tertiary palette.
$violet-palette: _patch-error-palette((
  0: #000000,
  10: #270057,
  20: #42008a,
  25: #5000a4,
  30: #5f00c0,
  35: #6e00dc,
  40: #7d00fa,
  50: #944aff,
  60: #a974ff,
  70: #bf98ff,
  80: #d5baff,
  90: #ecdcff,
  95: #f7edff,
  98: #fef7ff,
  99: #fffbff,
  100: #ffffff,
  secondary: (
    0: #000000,
    10: #1f182a,
    20: #352d40,
    25: #40384c,
    30: #4b4357,
    35: #574f63,
    40: #645b70,
    50: #7d7389,
    60: #978ca4,
    70: #b2a7bf,
    80: #cec2db,
    90: #eadef7,
    95: #f7edff,
    98: #fef7ff,
    99: #fffbff,
    100: #ffffff,
  ),
  neutral: (
    0: #000000,
    10: #1d1b1e,
    20: #323033,
    25: #3d3a3e,
    30: #49464a,
    35: #545155,
    40: #605d61,
    50: #7a767a,
    60: #948f94,
    70: #aeaaae,
    80: #cac5ca,
    90: #e6e1e6,
    95: #f5eff4,
    98: #fef8fc,
    99: #fffbff,
    100: #ffffff,
    4: #0f0d11,
    6: #151316,
    12: #211f22,
    17: #2b292d,
    22: #363437,
    24: #3b383c,
    87: #ded8dd,
    92: #ede6eb,
    94: #f2ecf1,
    96: #f8f2f6,
  ),
  neutral-variant: (
    0: #000000,
    10: #1d1a22,
    20: #332f37,
    25: #3e3a42,
    30: #49454e,
    35: #55515a,
    40: #615c66,
    50: #7b757f,
    60: #958e99,
    70: #b0a9b3,
    80: #cbc4cf,
    90: #e8e0eb,
    95: #f6eef9,
    98: #fef7ff,
    99: #fffbff,
    100: #ffffff,
  ),
));

/// Rose color palette to be used as primary or tertiary palette.
$rose-palette: _patch-error-palette((
  0: #000000,
  10: #3f001b,
  20: #65002f,
  25: #7a003a,
  30: #8f0045,
  35: #a40050,
  40: #ba005c,
  50: #e80074,
  60: #ff4a8e,
  70: #ff84a9,
  80: #ffb1c5,
  90: #ffd9e1,
  95: #ffecef,
  98: #fff8f8,
  99: #fffbff,
  100: #ffffff,
  secondary: (
    0: #000000,
    10: #2b151b,
    20: #422930,
    25: #4f343b,
    30: #5b3f46,
    35: #684b52,
    40: #74565d,
    50: #8f6f76,
    60: #aa888f,
    70: #c6a2aa,
    80: #e3bdc5,
    90: #ffd9e1,
    95: #ffecef,
    98: #fff8f8,
    99: #fffbff,
    100: #ffffff,
  ),
  neutral: (
    0: #000000,
    10: #201a1b,
    20: #352f30,
    25: #413a3b,
    30: #4c4546,
    35: #585052,
    40: #655c5e,
    50: #7e7576,
    60: #988e90,
    70: #b3a9aa,
    80: #cfc4c5,
    90: #ece0e1,
    95: #faeeef,
    98: #fff8f8,
    99: #fffbff,
    100: #ffffff,
    4: #120d0e,
    6: #171213,
    12: #241e1f,
    17: #2f2829,
    22: #3a3334,
    24: #3e3738,
    87: #e3d7d8,
    92: #f1e5e6,
    94: #f7ebec,
    96: #fdf1f2,
  ),
  neutral-variant: (
    0: #000000,
    10: #24191b,
    20: #3a2d30,
    25: #45383b,
    30: #514346,
    35: #5d4f52,
    40: #6a5a5e,
    50: #847376,
    60: #9e8c90,
    70: #baa7aa,
    80: #d6c2c5,
    90: #f3dde1,
    95: #ffecef,
    98: #fff8f8,
    99: #fffbff,
    100: #ffffff,
  ),
));
