{"version": 3, "file": "private.mjs", "sources": ["../../../../../../k8-fastbuild-ST-46c76129e412/bin/src/cdk/coercion/private/observable.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\nimport {Observable, isObservable, of as observableOf} from 'rxjs';\n\n/**\n * Given either an Observable or non-Observable value, returns either the original\n * Observable, or wraps it in an Observable that emits the non-Observable value.\n */\nexport function coerceObservable<T>(data: T | Observable<T>): Observable<T> {\n  if (!isObservable(data)) {\n    return observableOf(data);\n  }\n  return data;\n}\n"], "names": ["observableOf"], "mappings": ";;AASA;;;AAGG;AACG,SAAU,gBAAgB,CAAI,IAAuB,EAAA;AACzD,IAAA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE;AACvB,QAAA,OAAOA,EAAY,CAAC,IAAI,CAAC;;AAE3B,IAAA,OAAO,IAAI;AACb;;;;"}