export { d as MAT_SELECT_CONFIG, a as MAT_SELECT_SCROLL_STRATEGY, e as MAT_SELECT_SCROLL_STRATEGY_PROVIDER, b as MAT_SELECT_SCROLL_STRATEGY_PROVIDER_FACTORY, f as MAT_SELECT_TRIGGER, h as Mat<PERSON>elect, g as MatSelectChange, c as MatSelectConfig, M as MatSelectModule, i as MatSelectTrigger } from '../module.d-CyLvt0Fz.js';
export { a as MatOptgroup, M as MatOption } from '../option.d-BVGX3edu.js';
export { M as MatLabel } from '../module.d-1ZCYe5BH.js';
export { b as MatError, M as MatFormField, a as MatHint, c as MatPrefix, d as MatSuffix } from '../form-field.d-CMA_QQ0R.js';
import '@angular/core';
import '@angular/cdk/overlay';
import '../index.d-CwEYxGJi.js';
import '../index.d-DG9eDM2-.js';
import '../common-module.d-C8xzHJDr.js';
import '@angular/cdk/bidi';
import '../ripple.d-BxTUZJt7.js';
import '@angular/cdk/platform';
import '../pseudo-checkbox-module.d-DL5oxSJM.js';
import '@angular/cdk/a11y';
import '@angular/cdk/collections';
import '@angular/cdk/scrolling';
import '@angular/forms';
import 'rxjs';
import '../error-options.d-CGdTZUYk.js';
import '../form-field-control.d-QxD-9xJ3.js';
import '@angular/cdk/observers';
import '@angular/cdk/coercion';
import '../palette.d-BSSFKjO6.js';

/**
 * The following are all the animations for the mat-select component, with each
 * const containing the metadata for one animation.
 *
 * The values below match the implementation of the AngularJS Material mat-select animation.
 * @docs-private
 * @deprecated No longer used, will be removed.
 * @breaking-change 21.0.0
 */
declare const matSelectAnimations: {
    /**
     * @deprecated No longer being used. To be removed.
     * @breaking-change 12.0.0
     */
    readonly transformPanelWrap: any;
    readonly transformPanel: any;
};

export { matSelectAnimations };
