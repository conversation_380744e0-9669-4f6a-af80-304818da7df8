@use 'sass:map';
@use '../../token-definition';

// The prefix used to generate the fully qualified name for tokens in this file.
$prefix: (mat, divider);

/// Generates custom tokens for the mat-divider.
/// @param {Map} $systems The MDC system tokens
/// @param {Boolean} $exclude-hardcoded Whether to exclude hardcoded token values
/// @param {Map} $token-slots Possible token slots
/// @return {Map} A set of custom tokens for the mat-divider
@function get-tokens($systems, $exclude-hardcoded, $token-slots) {
  $tokens: (
    width: token-definition.hardcode(1px, $exclude-hardcoded),
    color: map.get($systems, md-sys-color, outline),
  );

  @return token-definition.namespace-tokens($prefix, $tokens, $token-slots);
}
