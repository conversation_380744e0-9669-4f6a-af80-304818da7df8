import { coerceNumberProperty } from '@angular/cdk/coercion';
import { ComponentHarness, HarnessPredicate } from '@angular/cdk/testing';

/** <PERSON><PERSON>ss for interacting with a MDC based mat-progress-spinner in tests. */
class MatProgressSpinnerHarness extends ComponentHarness {
    /** The selector for the host element of a `MatProgressSpinner` instance. */
    static hostSelector = '.mat-mdc-progress-spinner';
    /**
     * Gets a `HarnessPredicate` that can be used to search for a progress spinnner with specific
     * attributes.
     * @param options Options for filtering which progress spinner instances are considered a match.
     * @return a `HarnessPredicate` configured with the given options.
     */
    static with(options = {}) {
        return new HarnessPredicate(this, options);
    }
    /** Gets the progress spinner's value. */
    async getValue() {
        const host = await this.host();
        const ariaValue = await host.getAttribute('aria-valuenow');
        return ariaValue ? coerceNumberProperty(ariaValue) : null;
    }
    /** Gets the progress spinner's mode. */
    async getMode() {
        const modeAttr = (await this.host()).getAttribute('mode');
        return (await modeAttr);
    }
}

export { MatProgressSpinnerHarness };
//# sourceMappingURL=testing.mjs.map
