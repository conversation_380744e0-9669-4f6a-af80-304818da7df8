{"version": 3, "file": "pseudo-checkbox-CJ7seqQH.mjs", "sources": ["../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/core/selection/pseudo-checkbox/pseudo-checkbox.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  Component,\n  ViewEncapsulation,\n  Input,\n  ChangeDetectionStrategy,\n  ANIMATION_MODULE_TYPE,\n  inject,\n} from '@angular/core';\n\n/**\n * Possible states for a pseudo checkbox.\n * @docs-private\n */\nexport type MatPseudoCheckboxState = 'unchecked' | 'checked' | 'indeterminate';\n\n/**\n * Component that shows a simplified checkbox without including any kind of \"real\" checkbox.\n * Meant to be used when the checkbox is purely decorative and a large number of them will be\n * included, such as for the options in a multi-select. Uses no SVGs or complex animations.\n * Note that theming is meant to be handled by the parent element, e.g.\n * `mat-primary .mat-pseudo-checkbox`.\n *\n * Note that this component will be completely invisible to screen-reader users. This is *not*\n * interchangeable with `<mat-checkbox>` and should *not* be used if the user would directly\n * interact with the checkbox. The pseudo-checkbox should only be used as an implementation detail\n * of more complex components that appropriately handle selected / checked state.\n * @docs-private\n */\n@Component({\n  encapsulation: ViewEncapsulation.None,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  selector: 'mat-pseudo-checkbox',\n  styleUrl: 'pseudo-checkbox.css',\n  template: '',\n  host: {\n    'class': 'mat-pseudo-checkbox',\n    '[class.mat-pseudo-checkbox-indeterminate]': 'state === \"indeterminate\"',\n    '[class.mat-pseudo-checkbox-checked]': 'state === \"checked\"',\n    '[class.mat-pseudo-checkbox-disabled]': 'disabled',\n    '[class.mat-pseudo-checkbox-minimal]': 'appearance === \"minimal\"',\n    '[class.mat-pseudo-checkbox-full]': 'appearance === \"full\"',\n    '[class._mat-animation-noopable]': '_animationMode === \"NoopAnimations\"',\n  },\n})\nexport class MatPseudoCheckbox {\n  _animationMode? = inject(ANIMATION_MODULE_TYPE, {optional: true});\n\n  /** Display state of the checkbox. */\n  @Input() state: MatPseudoCheckboxState = 'unchecked';\n\n  /** Whether the checkbox is disabled. */\n  @Input() disabled: boolean = false;\n\n  /**\n   * Appearance of the pseudo checkbox. Default appearance of 'full' renders a checkmark/mixedmark\n   * indicator inside a square box. 'minimal' appearance only renders the checkmark/mixedmark.\n   */\n  @Input() appearance: 'minimal' | 'full' = 'full';\n\n  constructor(...args: unknown[]);\n  constructor() {}\n}\n"], "names": [], "mappings": ";;;AAuBA;;;;;;;;;;;;AAYG;MAiBU,iBAAiB,CAAA;IAC5B,cAAc,GAAI,MAAM,CAAC,qBAAqB,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;;IAGxD,KAAK,GAA2B,WAAW;;IAG3C,QAAQ,GAAY,KAAK;AAElC;;;AAGG;IACM,UAAU,GAAuB,MAAM;AAGhD,IAAA,WAAA,GAAA;uGAhBW,iBAAiB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAjB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,iBAAiB,gmBAXlB,EAAE,EAAA,QAAA,EAAA,IAAA,EAAA,MAAA,EAAA,CAAA,iyGAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FAWD,iBAAiB,EAAA,UAAA,EAAA,CAAA;kBAhB7B,SAAS;oCACO,iBAAiB,CAAC,IAAI,EAAA,eAAA,EACpB,uBAAuB,CAAC,MAAM,EACrC,QAAA,EAAA,qBAAqB,EAErB,QAAA,EAAA,EAAE,EACN,IAAA,EAAA;AACJ,wBAAA,OAAO,EAAE,qBAAqB;AAC9B,wBAAA,2CAA2C,EAAE,2BAA2B;AACxE,wBAAA,qCAAqC,EAAE,qBAAqB;AAC5D,wBAAA,sCAAsC,EAAE,UAAU;AAClD,wBAAA,qCAAqC,EAAE,0BAA0B;AACjE,wBAAA,kCAAkC,EAAE,uBAAuB;AAC3D,wBAAA,iCAAiC,EAAE,qCAAqC;AACzE,qBAAA,EAAA,MAAA,EAAA,CAAA,iyGAAA,CAAA,EAAA;wDAMQ,KAAK,EAAA,CAAA;sBAAb;gBAGQ,QAAQ,EAAA,CAAA;sBAAhB;gBAMQ,UAAU,EAAA,CAAA;sBAAlB;;;;;"}