import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BehaviorSubject, Observable, of } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import { Flower } from '../models';

@Injectable({
  providedIn: 'root'
})
export class FlowerService {
  private flowersSubject = new BehaviorSubject<Flower[]>([]);
  public flowers$ = this.flowersSubject.asObservable();

  constructor(private http: HttpClient) {
    this.loadFlowers();
  }

  private loadFlowers(): void {
    this.http.get<Flower[]>('/assets/flowers.json')
      .pipe(
        catchError(error => {
          console.error('Error loading flowers:', error);
          return of([]);
        })
      )
      .subscribe(flowers => {
        this.flowersSubject.next(flowers);
      });
  }

  getFlowers(): Observable<Flower[]> {
    return this.flowers$;
  }

  getFlowerById(id: number): Observable<Flower | undefined> {
    return this.flowers$.pipe(
      map(flowers => flowers.find(flower => flower.id === id))
    );
  }

  getFlowersByCategory(category: string): Observable<Flower[]> {
    return this.flowers$.pipe(
      map(flowers => flowers.filter(flower => 
        flower.category.toLowerCase() === category.toLowerCase()
      ))
    );
  }

  searchFlowers(searchTerm: string): Observable<Flower[]> {
    return this.flowers$.pipe(
      map(flowers => flowers.filter(flower =>
        flower.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        flower.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        flower.category.toLowerCase().includes(searchTerm.toLowerCase())
      ))
    );
  }

  filterFlowersByPrice(minPrice: number, maxPrice: number): Observable<Flower[]> {
    return this.flowers$.pipe(
      map(flowers => flowers.filter(flower =>
        flower.price >= minPrice && flower.price <= maxPrice
      ))
    );
  }

  getCategories(): Observable<string[]> {
    return this.flowers$.pipe(
      map(flowers => {
        const categories = flowers.map(flower => flower.category);
        return [...new Set(categories)].sort();
      })
    );
  }

  // Admin functions
  addFlower(flower: Omit<Flower, 'id'>): Observable<Flower> {
    const currentFlowers = this.flowersSubject.value;
    const newId = Math.max(...currentFlowers.map(f => f.id), 0) + 1;
    const newFlower: Flower = {
      ...flower,
      id: newId,
      createdAt: new Date(),
      updatedAt: new Date()
    };
    
    const updatedFlowers = [...currentFlowers, newFlower];
    this.flowersSubject.next(updatedFlowers);
    this.saveFlowersToStorage(updatedFlowers);
    
    return of(newFlower);
  }

  updateFlower(id: number, updates: Partial<Flower>): Observable<Flower | null> {
    const currentFlowers = this.flowersSubject.value;
    const flowerIndex = currentFlowers.findIndex(f => f.id === id);
    
    if (flowerIndex === -1) {
      return of(null);
    }

    const updatedFlower: Flower = {
      ...currentFlowers[flowerIndex],
      ...updates,
      id, // Ensure ID doesn't change
      updatedAt: new Date()
    };

    const updatedFlowers = [...currentFlowers];
    updatedFlowers[flowerIndex] = updatedFlower;
    
    this.flowersSubject.next(updatedFlowers);
    this.saveFlowersToStorage(updatedFlowers);
    
    return of(updatedFlower);
  }

  deleteFlower(id: number): Observable<boolean> {
    const currentFlowers = this.flowersSubject.value;
    const updatedFlowers = currentFlowers.filter(f => f.id !== id);
    
    if (updatedFlowers.length === currentFlowers.length) {
      return of(false); // Flower not found
    }

    this.flowersSubject.next(updatedFlowers);
    this.saveFlowersToStorage(updatedFlowers);
    
    return of(true);
  }

  private saveFlowersToStorage(flowers: Flower[]): void {
    // In a real app, this would save to a backend
    // For demo purposes, we'll save to localStorage
    localStorage.setItem('flowers', JSON.stringify(flowers));
  }
}
