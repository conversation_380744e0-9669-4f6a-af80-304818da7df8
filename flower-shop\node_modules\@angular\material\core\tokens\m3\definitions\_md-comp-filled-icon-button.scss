//
// Design system display name: Material 3
// Design system version: v0.161
//

@use 'sass:map';

@use './md-sys-color';

@use './md-sys-shape';

@use './md-sys-state';

$_default: (
  'md-sys-color': md-sys-color.values-light(),
  'md-sys-shape': md-sys-shape.values(),
  'md-sys-state': md-sys-state.values(),
);

@function values($deps: $_default, $exclude-hardcoded-values: false) {
  @return (
    'container-color': map.get($deps, 'md-sys-color', 'primary'),
    'container-shape': map.get($deps, 'md-sys-shape', 'corner-full'),
    'container-size': if($exclude-hardcoded-values, null, 40px),
    'disabled-container-color': map.get($deps, 'md-sys-color', 'on-surface'),
    'disabled-container-opacity': if($exclude-hardcoded-values, null, 0.12),
    'disabled-icon-color': map.get($deps, 'md-sys-color', 'on-surface'),
    'disabled-icon-opacity': if($exclude-hardcoded-values, null, 0.38),
    'focus-icon-color': map.get($deps, 'md-sys-color', 'on-primary'),
    'focus-state-layer-color': map.get($deps, 'md-sys-color', 'on-primary'),
    'focus-state-layer-opacity':
      map.get($deps, 'md-sys-state', 'focus-state-layer-opacity'),
    'hover-icon-color': map.get($deps, 'md-sys-color', 'on-primary'),
    'hover-state-layer-color': map.get($deps, 'md-sys-color', 'on-primary'),
    'hover-state-layer-opacity':
      map.get($deps, 'md-sys-state', 'hover-state-layer-opacity'),
    'icon-color': map.get($deps, 'md-sys-color', 'on-primary'),
    'icon-size': if($exclude-hardcoded-values, null, 24px),
    'pressed-icon-color': map.get($deps, 'md-sys-color', 'on-primary'),
    'pressed-state-layer-color': map.get($deps, 'md-sys-color', 'on-primary'),
    'pressed-state-layer-opacity':
      map.get($deps, 'md-sys-state', 'pressed-state-layer-opacity'),
    'selected-container-color': map.get($deps, 'md-sys-color', 'primary'),
    'toggle-selected-focus-icon-color':
      map.get($deps, 'md-sys-color', 'on-primary'),
    'toggle-selected-focus-state-layer-color':
      map.get($deps, 'md-sys-color', 'on-primary'),
    'toggle-selected-hover-icon-color':
      map.get($deps, 'md-sys-color', 'on-primary'),
    'toggle-selected-hover-state-layer-color':
      map.get($deps, 'md-sys-color', 'on-primary'),
    'toggle-selected-icon-color': map.get($deps, 'md-sys-color', 'on-primary'),
    'toggle-selected-pressed-icon-color':
      map.get($deps, 'md-sys-color', 'on-primary'),
    'toggle-selected-pressed-state-layer-color':
      map.get($deps, 'md-sys-color', 'on-primary'),
    'toggle-unselected-focus-icon-color':
      map.get($deps, 'md-sys-color', 'primary'),
    'toggle-unselected-focus-state-layer-color':
      map.get($deps, 'md-sys-color', 'primary'),
    'toggle-unselected-hover-icon-color':
      map.get($deps, 'md-sys-color', 'primary'),
    'toggle-unselected-hover-state-layer-color':
      map.get($deps, 'md-sys-color', 'primary'),
    'toggle-unselected-icon-color': map.get($deps, 'md-sys-color', 'primary'),
    'toggle-unselected-pressed-icon-color':
      map.get($deps, 'md-sys-color', 'primary'),
    'toggle-unselected-pressed-state-layer-color':
      map.get($deps, 'md-sys-color', 'primary'),
    'unselected-container-color':
      map.get($deps, 'md-sys-color', 'surface-variant')
  );
}
