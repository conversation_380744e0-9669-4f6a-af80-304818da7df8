/* Petals & Blooms Navigation - Exact from Example1.html */
.petals-nav {
    background: white;
    box-shadow: var(--shadow-soft);
    position: sticky;
    top: 0;
    z-index: 40;
}

.nav-container {
    max-width: 1280px;
    margin: 0 auto;
    padding: 0 1rem;
}

.nav-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 4rem;
}

.nav-left {
    display: flex;
    align-items: center;
}

.mobile-menu-toggle {
    display: none;
    color: var(--text-light);

    &:hover {
        color: var(--accent-pink);
    }
}

.logo-button {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    margin-left: 0.75rem;

    .logo-icon {
        color: var(--accent-pink);
        font-size: 1.5rem;
    }

    .logo-text {
        font-size: 1.5rem;
        font-weight: 700;
        color: var(--accent-pink);
    }
}

.nav-center {
    display: flex;
    align-items: center;
    gap: 2rem;
}

.nav-item {
    color: var(--text-light);
    font-weight: 500;
    transition: var(--transition-smooth);

    &:hover {
        color: var(--accent-pink);
    }

    &.active {
        color: var(--accent-pink);
        font-weight: 600;
    }
}

.nav-right {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.cart-button {
    position: relative;
    color: var(--text-light);

    &:hover {
        color: var(--accent-pink);
    }
}

.cart-badge {
    position: absolute;
    top: -8px;
    right: -8px;
    background: var(--accent-pink);
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: bold;
}

.user-button {
    color: var(--text-light);

    &:hover {
        color: var(--accent-pink);
    }
}

.auth-button {
    color: var(--text-light);
    font-weight: 500;

    &:hover {
        color: var(--accent-pink);
    }
}

.register-button {
    margin-left: 0.5rem;
}

/* User Dropdown */
.user-info {
    padding: 1rem;
    border-bottom: 1px solid #e2e8f0;

    .user-name {
        display: block;
        font-weight: 600;
        color: var(--text-dark);
    }

    .user-email {
        display: block;
        font-size: 0.875rem;
        color: var(--text-light);
        margin-top: 0.25rem;
    }
}

/* Main Content */
.main-content {
    min-height: calc(100vh - 4rem);
}

/* Toast Notification */
.toast {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
    background: var(--sage-green);
    color: white;
    padding: 1rem 1.5rem;
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-medium);
    transform: translateX(100%);
    transition: var(--transition-smooth);

    &.show {
        transform: translateX(0);
    }
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    inset: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 50;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition-smooth);

    &.show {
        opacity: 1;
        visibility: visible;
    }
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .mobile-menu-toggle {
        display: flex;
    }

    .nav-center {
        display: none;
    }

    .logo-button {
        margin-left: 0.75rem;

        .logo-text {
            font-size: 1.25rem;
        }
    }
}

@media (max-width: 480px) {
    .logo-button .logo-text {
        display: none;
    }
}
