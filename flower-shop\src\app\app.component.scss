.app-toolbar {
    position: sticky;
    top: 0;
    z-index: 1000;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    .logo-button {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 1.2rem;
        font-weight: 500;

        mat-icon {
            font-size: 1.5rem;
            height: 1.5rem;
            width: 1.5rem;
        }
    }

    .spacer {
        flex: 1 1 auto;
    }

    .nav-buttons {
        display: flex;
        align-items: center;
        gap: 8px;

        button {
            display: flex;
            align-items: center;
            gap: 4px;

            .button-text {
                display: none;
            }

            mat-icon {
                font-size: 1.2rem;
                height: 1.2rem;
                width: 1.2rem;
            }
        }
    }
}

.main-content {
    min-height: calc(100vh - 64px);
    background-color: #fafafa;
}

// Responsive design
@media (min-width: 768px) {
    .app-toolbar .nav-buttons button .button-text {
        display: inline;
    }
}

// Dark theme support
@media (prefers-color-scheme: dark) {
    .main-content {
        background-color: #303030;
    }
}
