<div class="checkout-container">
    <div class="checkout-header">
        <h1>Checkout</h1>
        <button mat-icon-button (click)="goBack()" matTooltip="Back to Cart">
            <mat-icon>arrow_back</mat-icon>
        </button>
    </div>

    <div *ngIf="cart$ | async as cart" class="checkout-content">
        <div class="checkout-form">
            <mat-card>
                <mat-card-header>
                    <mat-card-title>Shipping Information</mat-card-title>
                </mat-card-header>

                <mat-card-content>
                    <form [formGroup]="checkoutForm" (ngSubmit)="onSubmit()">
                        <div class="form-row">
                            <mat-form-field appearance="outline" class="full-width">
                                <mat-label>Full Name</mat-label>
                                <input matInput formControlName="customerName" required>
                                <mat-error *ngIf="checkoutForm.get('customerName')?.hasError('required')">
                                    Name is required
                                </mat-error>
                                <mat-error *ngIf="checkoutForm.get('customerName')?.hasError('minlength')">
                                    Name must be at least 2 characters
                                </mat-error>
                            </mat-form-field>
                        </div>

                        <div class="form-row">
                            <mat-form-field appearance="outline" class="full-width">
                                <mat-label>Email</mat-label>
                                <input matInput type="email" formControlName="email" required>
                                <mat-error *ngIf="checkoutForm.get('email')?.hasError('required')">
                                    Email is required
                                </mat-error>
                                <mat-error *ngIf="checkoutForm.get('email')?.hasError('email')">
                                    Please enter a valid email
                                </mat-error>
                            </mat-form-field>
                        </div>

                        <div class="form-row">
                            <mat-form-field appearance="outline" class="full-width">
                                <mat-label>Address</mat-label>
                                <input matInput formControlName="address" required>
                                <mat-error *ngIf="checkoutForm.get('address')?.hasError('required')">
                                    Address is required
                                </mat-error>
                                <mat-error *ngIf="checkoutForm.get('address')?.hasError('minlength')">
                                    Address must be at least 5 characters
                                </mat-error>
                            </mat-form-field>
                        </div>

                        <div class="form-row">
                            <mat-form-field appearance="outline" class="half-width">
                                <mat-label>City</mat-label>
                                <input matInput formControlName="city" required>
                                <mat-error *ngIf="checkoutForm.get('city')?.hasError('required')">
                                    City is required
                                </mat-error>
                            </mat-form-field>

                            <mat-form-field appearance="outline" class="half-width">
                                <mat-label>Postal Code</mat-label>
                                <input matInput formControlName="postalCode" required>
                                <mat-error *ngIf="checkoutForm.get('postalCode')?.hasError('required')">
                                    Postal code is required
                                </mat-error>
                                <mat-error *ngIf="checkoutForm.get('postalCode')?.hasError('pattern')">
                                    Please enter a valid postal code
                                </mat-error>
                            </mat-form-field>
                        </div>

                        <div class="form-row">
                            <mat-form-field appearance="outline" class="full-width">
                                <mat-label>Phone (Optional)</mat-label>
                                <input matInput formControlName="phone">
                                <mat-error *ngIf="checkoutForm.get('phone')?.hasError('pattern')">
                                    Please enter a valid phone number
                                </mat-error>
                            </mat-form-field>
                        </div>

                        <div class="form-actions">
                            <button mat-stroked-button type="button" (click)="goBack()">
                                Back to Cart
                            </button>
                            <button mat-raised-button color="primary" type="submit"
                                [disabled]="isLoading || checkoutForm.invalid">
                                <span *ngIf="isLoading">Processing...</span>
                                <span *ngIf="!isLoading">Place Order</span>
                            </button>
                        </div>
                    </form>
                </mat-card-content>
            </mat-card>
        </div>

        <div class="order-summary">
            <mat-card>
                <mat-card-header>
                    <mat-card-title>Order Summary</mat-card-title>
                </mat-card-header>

                <mat-card-content>
                    <div class="summary-items">
                        <div *ngFor="let item of cart.items" class="summary-item">
                            <div class="item-info">
                                <span class="item-name">{{ item.flower.name }}</span>
                                <span class="item-quantity">x{{ item.quantity }}</span>
                            </div>
                            <span class="item-total">${{ (item.flower.price * item.quantity) | number:'1.2-2' }}</span>
                        </div>
                    </div>

                    <div class="summary-totals">
                        <div class="summary-row">
                            <span>Subtotal:</span>
                            <span>${{ cart.totalPrice | number:'1.2-2' }}</span>
                        </div>
                        <div class="summary-row">
                            <span>Shipping:</span>
                            <span>FREE</span>
                        </div>
                        <div class="summary-row total-row">
                            <span><strong>Total:</strong></span>
                            <span><strong>${{ cart.totalPrice | number:'1.2-2' }}</strong></span>
                        </div>
                    </div>
                </mat-card-content>
            </mat-card>
        </div>
    </div>
</div>
