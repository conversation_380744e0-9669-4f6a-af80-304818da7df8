@use '../../token-definition';
@use '../../../theming/inspection';
@use '../../../style/sass-utils';

// The prefix used to generate the fully qualified name for tokens in this file.
$prefix: (mat, card);

// Tokens that can't be configured through Angular Material's current theming API,
// but may be in a future version of the theming API.
@function get-unthemable-tokens() {
  @return ();
}

// Tokens that can be configured through Angular Material's color theming API.
@function get-color-tokens($theme) {
  @return (
    // Text color of the card's subtitle.
    subtitle-text-color: inspection.get-theme-color($theme, foreground, secondary-text),
  );
}

// Tokens that can be configured through Angular Material's typography theming API.
@function get-typography-tokens($theme) {
  @return (
    // Font family of the card's title.
    title-text-font: inspection.get-theme-typography($theme, headline-6, font-family),
    // Line height of the card's title.
    title-text-line-height: inspection.get-theme-typography($theme, headline-6, line-height),
    // Font size of the card's title.
    title-text-size: inspection.get-theme-typography($theme, headline-6, font-size),
    // Letter spacing of the card's title.
    title-text-tracking: inspection.get-theme-typography($theme, headline-6, letter-spacing),
    // Font weight of the card's title.
    title-text-weight: inspection.get-theme-typography($theme, headline-6, font-weight),
    // Font family of the card's subtitle.
    subtitle-text-font: inspection.get-theme-typography($theme, subtitle-2, font-family),
    // Line height of the card's subtitle.
    subtitle-text-line-height: inspection.get-theme-typography($theme, subtitle-2, line-height),
    // Font size of the card's subtitle.
    subtitle-text-size: inspection.get-theme-typography($theme, subtitle-2, font-size),
    // Letter spacing of the card's subtitle.
    subtitle-text-tracking: inspection.get-theme-typography($theme, subtitle-2, letter-spacing),
    // Font weight of the card's subtitle.
    subtitle-text-weight: inspection.get-theme-typography($theme, subtitle-2, font-weight),
  );
}

// Tokens that can be configured through Angular Material's density theming API.
@function get-density-tokens($theme) {
  @return ();
}

// Combines the tokens generated by the above functions into a single map with placeholder values.
// This is used to create token slots.
@function get-token-slots() {
  @return sass-utils.deep-merge-all(
      get-unthemable-tokens(),
      get-color-tokens(token-definition.$placeholder-color-config),
      get-typography-tokens(token-definition.$placeholder-typography-config),
      get-density-tokens(token-definition.$placeholder-density-config)
  );
}
