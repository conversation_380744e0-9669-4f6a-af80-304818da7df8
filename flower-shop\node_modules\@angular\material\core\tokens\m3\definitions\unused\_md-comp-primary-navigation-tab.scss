//
// Design system display name: Material 3
// Design system version: v0.161
//

@use 'sass:map';

@use './md-sys-color';

@use './md-sys-elevation';

@use './md-sys-shape';

@use './md-sys-state';

@use './md-sys-typescale';

$_default: (
  'md-sys-color': md-sys-color.values-light(),
  'md-sys-elevation': md-sys-elevation.values(),
  'md-sys-shape': md-sys-shape.values(),
  'md-sys-state': md-sys-state.values(),
  'md-sys-typescale': md-sys-typescale.values(),
);

@function values($deps: $_default, $exclude-hardcoded-values: false) {
  @return (
    'active-indicator-color': map.get($deps, 'md-sys-color', 'primary'),
    'active-indicator-height': if($exclude-hardcoded-values, null, 3px),
    'active-indicator-shape':
      if($exclude-hardcoded-values, null, (3px 3px 0 0)),
    'active-focus-state-layer-color': map.get($deps, 'md-sys-color', 'primary'),
    'active-focus-state-layer-opacity':
      map.get($deps, 'md-sys-state', 'focus-state-layer-opacity'),
    'active-hover-state-layer-color': map.get($deps, 'md-sys-color', 'primary'),
    'active-hover-state-layer-opacity':
      map.get($deps, 'md-sys-state', 'hover-state-layer-opacity'),
    'active-pressed-state-layer-color':
      map.get($deps, 'md-sys-color', 'primary'),
    'active-pressed-state-layer-opacity':
      map.get($deps, 'md-sys-state', 'pressed-state-layer-opacity'),
    'container-color': map.get($deps, 'md-sys-color', 'surface'),
    'container-elevation': map.get($deps, 'md-sys-elevation', 'level0'),
    'container-height': if($exclude-hardcoded-values, null, 48px),
    'container-shape': map.get($deps, 'md-sys-shape', 'corner-none'),
    'inactive-focus-state-layer-color':
      map.get($deps, 'md-sys-color', 'on-surface'),
    'inactive-focus-state-layer-opacity':
      map.get($deps, 'md-sys-state', 'focus-state-layer-opacity'),
    'inactive-hover-state-layer-color':
      map.get($deps, 'md-sys-color', 'on-surface'),
    'inactive-hover-state-layer-opacity':
      map.get($deps, 'md-sys-state', 'hover-state-layer-opacity'),
    'inactive-pressed-state-layer-color':
      map.get($deps, 'md-sys-color', 'primary'),
    'inactive-pressed-state-layer-opacity':
      map.get($deps, 'md-sys-state', 'pressed-state-layer-opacity'),
    'with-icon-and-label-text-container-height':
      if($exclude-hardcoded-values, null, 64px),
    'with-icon-active-focus-icon-color':
      map.get($deps, 'md-sys-color', 'primary'),
    'with-icon-active-hover-icon-color':
      map.get($deps, 'md-sys-color', 'primary'),
    'with-icon-active-icon-color': map.get($deps, 'md-sys-color', 'primary'),
    'with-icon-active-pressed-icon-color':
      map.get($deps, 'md-sys-color', 'primary'),
    'with-icon-icon-size': if($exclude-hardcoded-values, null, 24px),
    'with-icon-inactive-focus-icon-color':
      map.get($deps, 'md-sys-color', 'on-surface'),
    'with-icon-inactive-hover-icon-color':
      map.get($deps, 'md-sys-color', 'on-surface'),
    'with-icon-inactive-icon-color':
      map.get($deps, 'md-sys-color', 'on-surface-variant'),
    'with-icon-inactive-pressed-icon-color':
      map.get($deps, 'md-sys-color', 'on-surface'),
    'with-label-text-active-focus-label-text-color':
      map.get($deps, 'md-sys-color', 'primary'),
    'with-label-text-active-hover-label-text-color':
      map.get($deps, 'md-sys-color', 'primary'),
    'with-label-text-active-label-text-color':
      map.get($deps, 'md-sys-color', 'primary'),
    'with-label-text-active-pressed-label-text-color':
      map.get($deps, 'md-sys-color', 'primary'),
    'with-label-text-inactive-focus-label-text-color':
      map.get($deps, 'md-sys-color', 'on-surface'),
    'with-label-text-inactive-hover-label-text-color':
      map.get($deps, 'md-sys-color', 'on-surface'),
    'with-label-text-inactive-label-text-color':
      map.get($deps, 'md-sys-color', 'on-surface-variant'),
    'with-label-text-inactive-pressed-label-text-color':
      map.get($deps, 'md-sys-color', 'on-surface'),
    'with-label-text-label-text-font':
      map.get($deps, 'md-sys-typescale', 'title-small-font'),
    'with-label-text-label-text-line-height':
      map.get($deps, 'md-sys-typescale', 'title-small-line-height'),
    'with-label-text-label-text-size':
      map.get($deps, 'md-sys-typescale', 'title-small-size'),
    'with-label-text-label-text-tracking':
      map.get($deps, 'md-sys-typescale', 'title-small-tracking'),
    // Warning: risk of reduced fidelity from using this composite typography token.
    // Tokens md.comp.primary-navigation-tab.with-label-text.label-text.tracking cannot be
    // represented in the "font" property shorthand. Consider using the discrete properties instead.
    'with-label-text-label-text-type':
      if(
        $exclude-hardcoded-values,
        null,
          map.get($deps, 'md-sys-typescale', 'title-small-weight')
          map.get($deps, 'md-sys-typescale', 'title-small-size') #{'/'} map.get(
            $deps,
            'md-sys-typescale',
            'title-small-line-height'
          ) map.get($deps, 'md-sys-typescale', 'title-small-font')
      ),
    'with-label-text-label-text-weight':
      map.get($deps, 'md-sys-typescale', 'title-small-weight')
  );
}
