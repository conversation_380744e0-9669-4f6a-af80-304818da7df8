import { M as MatButtonToggleGroup, a as <PERSON><PERSON><PERSON><PERSON>Toggle } from '../button-toggle.d-DoMJU5F_.js';
export { d as MAT_BUTTON_TOGGLE_DEFAULT_OPTIONS, f as MAT_BUTTON_TOGGLE_GROUP, e as MAT_BUTTON_TOGGLE_GROUP_DEFAULT_OPTIONS_FACTORY, g as MAT_BUTTON_TOGGLE_GROUP_VALUE_ACCESSOR, b as MatButtonToggleAppearance, h as MatButtonToggleChange, c as MatButtonToggleDefaultOptions, T as ToggleType } from '../button-toggle.d-DoMJU5F_.js';
import * as i0 from '@angular/core';
import { M as MatCommonModule } from '../common-module.d-C8xzHJDr.js';
import { M as MatRippleModule } from '../index.d-DG9eDM2-.js';
import '@angular/cdk/bidi';
import '@angular/forms';
import '../ripple.d-BxTUZJt7.js';
import '@angular/cdk/platform';

declare class MatButtonToggleModule {
    static ɵfac: i0.ɵɵFactoryDeclaration<MatButtonToggleModule, never>;
    static ɵmod: i0.ɵɵNgModuleDeclaration<MatButtonToggleModule, never, [typeof MatCommonModule, typeof MatRippleModule, typeof MatButtonToggleGroup, typeof MatButtonToggle], [typeof MatCommonModule, typeof MatButtonToggleGroup, typeof MatButtonToggle]>;
    static ɵinj: i0.ɵɵInjectorDeclaration<MatButtonToggleModule>;
}

export { MatButtonToggle, MatButtonToggleGroup, MatButtonToggleModule };
