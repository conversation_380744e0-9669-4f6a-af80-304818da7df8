{"version": 3, "file": "select.mjs", "sources": ["../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/select/select-animations.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n/**\n * The following are all the animations for the mat-select component, with each\n * const containing the metadata for one animation.\n *\n * The values below match the implementation of the AngularJS Material mat-select animation.\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nexport const matSelectAnimations: {\n  /**\n   * @deprecated No longer being used. To be removed.\n   * @breaking-change 12.0.0\n   */\n  readonly transformPanelWrap: any;\n  readonly transformPanel: any;\n} = {\n  // Represents\n  // trigger('transformPanelWrap', [\n  //   transition('* => void', query('@transformPanel', [animateChild()], {optional: true})),\n  // ])\n\n  /**\n   * This animation ensures the select's overlay panel animation (transformPanel) is called when\n   * closing the select.\n   * This is needed due to https://github.com/angular/angular/issues/23302\n   */\n  transformPanelWrap: {\n    type: 7,\n    name: 'transformPanelWrap',\n    definitions: [\n      {\n        type: 1,\n        expr: '* => void',\n        animation: {\n          type: 11,\n          selector: '@transformPanel',\n          animation: [{type: 9, options: null}],\n          options: {optional: true},\n        },\n        options: null,\n      },\n    ],\n    options: {},\n  },\n\n  // Represents\n  // trigger('transformPanel', [\n  //   state(\n  //     'void',\n  //     style({\n  //       opacity: 0,\n  //       transform: 'scale(1, 0.8)',\n  //     }),\n  //   ),\n  //   transition(\n  //     'void => showing',\n  //     animate(\n  //       '120ms cubic-bezier(0, 0, 0.2, 1)',\n  //       style({\n  //         opacity: 1,\n  //         transform: 'scale(1, 1)',\n  //       }),\n  //     ),\n  //   ),\n  //   transition('* => void', animate('100ms linear', style({opacity: 0}))),\n  // ])\n\n  /** This animation transforms the select's overlay panel on and off the page. */\n  transformPanel: {\n    type: 7,\n    name: 'transformPanel',\n    definitions: [\n      {\n        type: 0,\n        name: 'void',\n        styles: {\n          type: 6,\n          styles: {opacity: 0, transform: 'scale(1, 0.8)'},\n          offset: null,\n        },\n      },\n      {\n        type: 1,\n        expr: 'void => showing',\n        animation: {\n          type: 4,\n          styles: {\n            type: 6,\n            styles: {opacity: 1, transform: 'scale(1, 1)'},\n            offset: null,\n          },\n          timings: '120ms cubic-bezier(0, 0, 0.2, 1)',\n        },\n        options: null,\n      },\n      {\n        type: 1,\n        expr: '* => void',\n        animation: {\n          type: 4,\n          styles: {type: 6, styles: {opacity: 0}, offset: null},\n          timings: '100ms linear',\n        },\n        options: null,\n      },\n    ],\n    options: {},\n  },\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQA;;;;;;;;AAQG;AACU,MAAA,mBAAmB,GAO5B;;;;;AAMF;;;;AAIG;AACH,IAAA,kBAAkB,EAAE;AAClB,QAAA,IAAI,EAAE,CAAC;AACP,QAAA,IAAI,EAAE,oBAAoB;AAC1B,QAAA,WAAW,EAAE;AACX,YAAA;AACE,gBAAA,IAAI,EAAE,CAAC;AACP,gBAAA,IAAI,EAAE,WAAW;AACjB,gBAAA,SAAS,EAAE;AACT,oBAAA,IAAI,EAAE,EAAE;AACR,oBAAA,QAAQ,EAAE,iBAAiB;oBAC3B,SAAS,EAAE,CAAC,EAAC,IAAI,EAAE,CAAC,EAAE,OAAO,EAAE,IAAI,EAAC,CAAC;AACrC,oBAAA,OAAO,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC;AAC1B,iBAAA;AACD,gBAAA,OAAO,EAAE,IAAI;AACd,aAAA;AACF,SAAA;AACD,QAAA,OAAO,EAAE,EAAE;AACZ,KAAA;;;;;;;;;;;;;;;;;;;;;;;AAyBD,IAAA,cAAc,EAAE;AACd,QAAA,IAAI,EAAE,CAAC;AACP,QAAA,IAAI,EAAE,gBAAgB;AACtB,QAAA,WAAW,EAAE;AACX,YAAA;AACE,gBAAA,IAAI,EAAE,CAAC;AACP,gBAAA,IAAI,EAAE,MAAM;AACZ,gBAAA,MAAM,EAAE;AACN,oBAAA,IAAI,EAAE,CAAC;oBACP,MAAM,EAAE,EAAC,OAAO,EAAE,CAAC,EAAE,SAAS,EAAE,eAAe,EAAC;AAChD,oBAAA,MAAM,EAAE,IAAI;AACb,iBAAA;AACF,aAAA;AACD,YAAA;AACE,gBAAA,IAAI,EAAE,CAAC;AACP,gBAAA,IAAI,EAAE,iBAAiB;AACvB,gBAAA,SAAS,EAAE;AACT,oBAAA,IAAI,EAAE,CAAC;AACP,oBAAA,MAAM,EAAE;AACN,wBAAA,IAAI,EAAE,CAAC;wBACP,MAAM,EAAE,EAAC,OAAO,EAAE,CAAC,EAAE,SAAS,EAAE,aAAa,EAAC;AAC9C,wBAAA,MAAM,EAAE,IAAI;AACb,qBAAA;AACD,oBAAA,OAAO,EAAE,kCAAkC;AAC5C,iBAAA;AACD,gBAAA,OAAO,EAAE,IAAI;AACd,aAAA;AACD,YAAA;AACE,gBAAA,IAAI,EAAE,CAAC;AACP,gBAAA,IAAI,EAAE,WAAW;AACjB,gBAAA,SAAS,EAAE;AACT,oBAAA,IAAI,EAAE,CAAC;AACP,oBAAA,MAAM,EAAE,EAAC,IAAI,EAAE,CAAC,EAAE,MAAM,EAAE,EAAC,OAAO,EAAE,CAAC,EAAC,EAAE,MAAM,EAAE,IAAI,EAAC;AACrD,oBAAA,OAAO,EAAE,cAAc;AACxB,iBAAA;AACD,gBAAA,OAAO,EAAE,IAAI;AACd,aAAA;AACF,SAAA;AACD,QAAA,OAAO,EAAE,EAAE;AACZ,KAAA;;;;;"}