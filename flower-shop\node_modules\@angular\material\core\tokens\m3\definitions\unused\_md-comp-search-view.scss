//
// Design system display name: Material 3
// Design system version: v0.161
//

@use 'sass:map';

@use './md-sys-color';

@use './md-sys-elevation';

@use './md-sys-shape';

@use './md-sys-typescale';

$_default: (
  'md-sys-color': md-sys-color.values-light(),
  'md-sys-elevation': md-sys-elevation.values(),
  'md-sys-shape': md-sys-shape.values(),
  'md-sys-typescale': md-sys-typescale.values(),
);

@function values($deps: $_default, $exclude-hardcoded-values: false) {
  @return (
    'container-color': map.get($deps, 'md-sys-color', 'surface'),
    'container-elevation': map.get($deps, 'md-sys-elevation', 'level3'),
    'container-surface-tint-layer-color':
      map.get($deps, 'md-sys-color', 'surface-tint'),
    'divider-color': map.get($deps, 'md-sys-color', 'outline'),
    'docked-container-shape':
      map.get($deps, 'md-sys-shape', 'corner-extra-large'),
    'docked-header-container-height': if($exclude-hardcoded-values, null, 56px),
    'full-screen-container-shape': map.get($deps, 'md-sys-shape', 'corner-none'),
    'full-screen-header-container-height':
      if($exclude-hardcoded-values, null, 72px),
    'header-input-text-color': map.get($deps, 'md-sys-color', 'on-surface'),
    'header-input-text-font':
      map.get($deps, 'md-sys-typescale', 'body-large-font'),
    'header-input-text-line-height':
      map.get($deps, 'md-sys-typescale', 'body-large-line-height'),
    'header-input-text-size':
      map.get($deps, 'md-sys-typescale', 'body-large-size'),
    'header-input-text-tracking':
      map.get($deps, 'md-sys-typescale', 'body-large-tracking'),
    // Warning: risk of reduced fidelity from using this composite typography token.
    // Tokens md.comp.search-view.header.input-text.tracking cannot be represented in the
    // "font" property shorthand. Consider using the discrete properties instead.
    'header-input-text-type':
      if(
        $exclude-hardcoded-values,
        null,
          map.get($deps, 'md-sys-typescale', 'body-large-weight')
          map.get($deps, 'md-sys-typescale', 'body-large-size') #{'/'} map.get(
            $deps,
            'md-sys-typescale',
            'body-large-line-height'
          ) map.get($deps, 'md-sys-typescale', 'body-large-font')
      ),
    'header-input-text-weight':
      map.get($deps, 'md-sys-typescale', 'body-large-weight'),
    'header-leading-icon-color': map.get($deps, 'md-sys-color', 'on-surface'),
    'header-supporting-text-color':
      map.get($deps, 'md-sys-color', 'on-surface-variant'),
    'header-supporting-text-font':
      map.get($deps, 'md-sys-typescale', 'body-large-font'),
    'header-supporting-text-line-height':
      map.get($deps, 'md-sys-typescale', 'body-large-line-height'),
    'header-supporting-text-size':
      map.get($deps, 'md-sys-typescale', 'body-large-size'),
    'header-supporting-text-tracking':
      map.get($deps, 'md-sys-typescale', 'body-large-tracking'),
    // Warning: risk of reduced fidelity from using this composite typography token.
    // Tokens md.comp.search-view.header.supporting-text.tracking cannot be represented in the
    // "font" property shorthand. Consider using the discrete properties instead.
    'header-supporting-text-type':
      if(
        $exclude-hardcoded-values,
        null,
          map.get($deps, 'md-sys-typescale', 'body-large-weight')
          map.get($deps, 'md-sys-typescale', 'body-large-size') #{'/'} map.get(
            $deps,
            'md-sys-typescale',
            'body-large-line-height'
          ) map.get($deps, 'md-sys-typescale', 'body-large-font')
      ),
    'header-supporting-text-weight':
      map.get($deps, 'md-sys-typescale', 'body-large-weight'),
    'header-trailing-icon-color':
      map.get($deps, 'md-sys-color', 'on-surface-variant')
  );
}
