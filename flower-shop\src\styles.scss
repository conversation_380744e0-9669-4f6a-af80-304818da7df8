/* Pet<PERSON> & Blooms - Modern Flower Shop Styles (Based on Example1.html) */
@import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;600;700&family=Inter:wght@300;400;500;600&display=swap');

/* Color Palette - Exact from Example1.html */
:root {
    --primary-pink: #f8e8f0;
    --accent-pink: #e91e63;
    --soft-lavender: #f3e5f5;
    --sage-green: #81c784;
    --warm-cream: #fefefe;
    --text-dark: #2d3748;
    --text-light: #718096;

    --shadow-soft: 0 2px 12px rgba(0, 0, 0, 0.1);
    --shadow-medium: 0 10px 20px rgba(233, 30, 99, 0.3);
    --shadow-strong: 0 20px 40px rgba(0, 0, 0, 0.1);

    --border-radius-sm: 8px;
    --border-radius-md: 12px;
    --border-radius-lg: 16px;

    --transition-smooth: all 0.3s ease;
}

/* Global Styles */
html,
body {
    height: 100%;
    margin: 0;
    padding: 0;
}

body {
    font-family: 'Inter', sans-serif;
    background: linear-gradient(135deg, var(--primary-pink) 0%, var(--soft-lavender) 100%);
    min-height: 100vh;
    color: var(--text-dark);
    line-height: 1.6;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Typography */
.font-display {
    font-family: 'Playfair Display', serif;
}

h1,
h2,
h3,
h4,
h5,
h6 {
    font-family: 'Playfair Display', serif;
    font-weight: 600;
    line-height: 1.3;
    color: var(--text-dark);
    margin-bottom: 1rem;
}

h1 {
    font-size: 2.5rem;
    font-weight: 700;
}

h2 {
    font-size: 2rem;
}

h3 {
    font-size: 1.5rem;
}

h4 {
    font-size: 1.25rem;
}

h5 {
    font-size: 1.125rem;
}

h6 {
    font-size: 1rem;
}

p {
    font-family: 'Inter', sans-serif;
    font-weight: 400;
    color: var(--text-light);
    margin-bottom: 1rem;
}

/* Glass Effect */
.glass-effect {
    background: rgba(255, 255, 255, 0.25);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.18);
}

/* Card Hover Effects */
.card-hover {
    transition: var(--transition-smooth);
}

.card-hover:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-strong);
}

/* Button Styles */
.btn-primary {
    background: linear-gradient(135deg, var(--accent-pink) 0%, #ad1457 100%);
    transition: var(--transition-smooth);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: var(--border-radius-sm);
    font-family: 'Inter', sans-serif;
    font-weight: 500;
    cursor: pointer;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

/* Animations */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.loading-spinner {
    border: 2px solid var(--primary-pink);
    border-top: 2px solid var(--accent-pink);
    border-radius: 50%;
    width: 20px;
    height: 20px;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

/* Product Image */
.product-image {
    aspect-ratio: 1;
    object-fit: cover;
}

/* Cart Badge */
.cart-badge {
    position: absolute;
    top: -8px;
    right: -8px;
    background: var(--accent-pink);
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: bold;
}

/* Utility Classes */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

.text-center {
    text-align: center;
}

.text-left {
    text-align: left;
}

.text-right {
    text-align: right;
}

.mb-1 {
    margin-bottom: 0.5rem;
}

.mb-2 {
    margin-bottom: 1rem;
}

.mb-3 {
    margin-bottom: 1.5rem;
}

.mb-4 {
    margin-bottom: 2rem;
}

.mt-1 {
    margin-top: 0.5rem;
}

.mt-2 {
    margin-top: 1rem;
}

.mt-3 {
    margin-top: 1.5rem;
}

.mt-4 {
    margin-top: 2rem;
}

/* Modern Card Styles */
.modern-card {
    background: white;
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-soft);
    transition: var(--transition-smooth);
    border: 1px solid rgba(255, 255, 255, 0.18);
}

.modern-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-strong);
}

/* Form Styles */
.form-field {
    margin-bottom: 1.5rem;
}

.form-field label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--text-dark);
}

.form-field input,
.form-field textarea,
.form-field select {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e2e8f0;
    border-radius: var(--border-radius-sm);
    font-family: 'Inter', sans-serif;
    transition: var(--transition-smooth);
    background: white;
}

.form-field input:focus,
.form-field textarea:focus,
.form-field select:focus {
    outline: none;
    border-color: var(--accent-pink);
    box-shadow: 0 0 0 3px rgba(233, 30, 99, 0.1);
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 0 0.75rem;
    }

    h1 {
        font-size: 2rem;
    }

    h2 {
        font-size: 1.75rem;
    }

    h3 {
        font-size: 1.25rem;
    }
}
