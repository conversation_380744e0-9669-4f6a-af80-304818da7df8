{"version": 3, "file": "typeahead-9ZW4Dtsf.mjs", "sources": ["../../../../../k8-fastbuild-ST-46c76129e412/bin/src/cdk/a11y/key-manager/typeahead.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {A, NINE, Z, ZERO} from '../../keycodes';\nimport {Subject, Observable} from 'rxjs';\nimport {debounceTime, filter, map, tap} from 'rxjs/operators';\n\nconst DEFAULT_TYPEAHEAD_DEBOUNCE_INTERVAL_MS = 200;\n\ninterface TypeaheadItem {\n  getLabel?(): string;\n}\n\ninterface TypeaheadConfig<T> {\n  debounceInterval?: number;\n  skipPredicate?: (item: T) => boolean | undefined;\n}\n\n/**\n * Selects items based on keyboard inputs. Implements the typeahead functionality of\n * `role=\"listbox\"` or `role=\"tree\"` and other related roles.\n */\nexport class Typeahead<T extends TypeaheadItem> {\n  private readonly _letterKeyStream = new Subject<string>();\n  private _items: readonly T[] = [];\n  private _selectedItemIndex = -1;\n\n  /** Buffer for the letters that the user has pressed */\n  private _pressedLetters: string[] = [];\n\n  private _skipPredicateFn?: (item: T) => boolean | undefined;\n\n  private readonly _selectedItem = new Subject<T>();\n  readonly selectedItem: Observable<T> = this._selectedItem;\n\n  constructor(initialItems: readonly T[], config?: TypeaheadConfig<T>) {\n    const typeAheadInterval =\n      typeof config?.debounceInterval === 'number'\n        ? config.debounceInterval\n        : DEFAULT_TYPEAHEAD_DEBOUNCE_INTERVAL_MS;\n\n    if (config?.skipPredicate) {\n      this._skipPredicateFn = config.skipPredicate;\n    }\n\n    if (\n      (typeof ngDevMode === 'undefined' || ngDevMode) &&\n      initialItems.length &&\n      initialItems.some(item => typeof item.getLabel !== 'function')\n    ) {\n      throw new Error('KeyManager items in typeahead mode must implement the `getLabel` method.');\n    }\n\n    this.setItems(initialItems);\n    this._setupKeyHandler(typeAheadInterval);\n  }\n\n  destroy() {\n    this._pressedLetters = [];\n    this._letterKeyStream.complete();\n    this._selectedItem.complete();\n  }\n\n  setCurrentSelectedItemIndex(index: number) {\n    this._selectedItemIndex = index;\n  }\n\n  setItems(items: readonly T[]) {\n    this._items = items;\n  }\n\n  handleKey(event: KeyboardEvent): void {\n    const keyCode = event.keyCode;\n\n    // Attempt to use the `event.key` which also maps it to the user's keyboard language,\n    // otherwise fall back to resolving alphanumeric characters via the keyCode.\n    if (event.key && event.key.length === 1) {\n      this._letterKeyStream.next(event.key.toLocaleUpperCase());\n    } else if ((keyCode >= A && keyCode <= Z) || (keyCode >= ZERO && keyCode <= NINE)) {\n      this._letterKeyStream.next(String.fromCharCode(keyCode));\n    }\n  }\n\n  /** Gets whether the user is currently typing into the manager using the typeahead feature. */\n  isTyping(): boolean {\n    return this._pressedLetters.length > 0;\n  }\n\n  /** Resets the currently stored sequence of typed letters. */\n  reset(): void {\n    this._pressedLetters = [];\n  }\n\n  private _setupKeyHandler(typeAheadInterval: number) {\n    // Debounce the presses of non-navigational keys, collect the ones that correspond to letters\n    // and convert those letters back into a string. Afterwards find the first item that starts\n    // with that string and select it.\n    this._letterKeyStream\n      .pipe(\n        tap(letter => this._pressedLetters.push(letter)),\n        debounceTime(typeAheadInterval),\n        filter(() => this._pressedLetters.length > 0),\n        map(() => this._pressedLetters.join('').toLocaleUpperCase()),\n      )\n      .subscribe(inputString => {\n        // Start at 1 because we want to start searching at the item immediately\n        // following the current active item.\n        for (let i = 1; i < this._items.length + 1; i++) {\n          const index = (this._selectedItemIndex + i) % this._items.length;\n          const item = this._items[index];\n\n          if (\n            !this._skipPredicateFn?.(item) &&\n            item.getLabel?.().toLocaleUpperCase().trim().indexOf(inputString) === 0\n          ) {\n            this._selectedItem.next(item);\n            break;\n          }\n        }\n\n        this._pressedLetters = [];\n      });\n  }\n}\n"], "names": [], "mappings": ";;;;AAYA,MAAM,sCAAsC,GAAG,GAAG;AAWlD;;;AAGG;MACU,SAAS,CAAA;AACH,IAAA,gBAAgB,GAAG,IAAI,OAAO,EAAU;IACjD,MAAM,GAAiB,EAAE;IACzB,kBAAkB,GAAG,CAAC,CAAC;;IAGvB,eAAe,GAAa,EAAE;AAE9B,IAAA,gBAAgB;AAEP,IAAA,aAAa,GAAG,IAAI,OAAO,EAAK;AACxC,IAAA,YAAY,GAAkB,IAAI,CAAC,aAAa;IAEzD,WAAY,CAAA,YAA0B,EAAE,MAA2B,EAAA;AACjE,QAAA,MAAM,iBAAiB,GACrB,OAAO,MAAM,EAAE,gBAAgB,KAAK;cAChC,MAAM,CAAC;cACP,sCAAsC;AAE5C,QAAA,IAAI,MAAM,EAAE,aAAa,EAAE;AACzB,YAAA,IAAI,CAAC,gBAAgB,GAAG,MAAM,CAAC,aAAa;;AAG9C,QAAA,IACE,CAAC,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS;AAC9C,YAAA,YAAY,CAAC,MAAM;AACnB,YAAA,YAAY,CAAC,IAAI,CAAC,IAAI,IAAI,OAAO,IAAI,CAAC,QAAQ,KAAK,UAAU,CAAC,EAC9D;AACA,YAAA,MAAM,IAAI,KAAK,CAAC,0EAA0E,CAAC;;AAG7F,QAAA,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC;AAC3B,QAAA,IAAI,CAAC,gBAAgB,CAAC,iBAAiB,CAAC;;IAG1C,OAAO,GAAA;AACL,QAAA,IAAI,CAAC,eAAe,GAAG,EAAE;AACzB,QAAA,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE;AAChC,QAAA,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE;;AAG/B,IAAA,2BAA2B,CAAC,KAAa,EAAA;AACvC,QAAA,IAAI,CAAC,kBAAkB,GAAG,KAAK;;AAGjC,IAAA,QAAQ,CAAC,KAAmB,EAAA;AAC1B,QAAA,IAAI,CAAC,MAAM,GAAG,KAAK;;AAGrB,IAAA,SAAS,CAAC,KAAoB,EAAA;AAC5B,QAAA,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO;;;AAI7B,QAAA,IAAI,KAAK,CAAC,GAAG,IAAI,KAAK,CAAC,GAAG,CAAC,MAAM,KAAK,CAAC,EAAE;AACvC,YAAA,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,iBAAiB,EAAE,CAAC;;aACpD,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,OAAO,IAAI,CAAC,MAAM,OAAO,IAAI,IAAI,IAAI,OAAO,IAAI,IAAI,CAAC,EAAE;AACjF,YAAA,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;;;;IAK5D,QAAQ,GAAA;AACN,QAAA,OAAO,IAAI,CAAC,eAAe,CAAC,MAAM,GAAG,CAAC;;;IAIxC,KAAK,GAAA;AACH,QAAA,IAAI,CAAC,eAAe,GAAG,EAAE;;AAGnB,IAAA,gBAAgB,CAAC,iBAAyB,EAAA;;;;AAIhD,QAAA,IAAI,CAAC;aACF,IAAI,CACH,GAAG,CAAC,MAAM,IAAI,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,EAChD,YAAY,CAAC,iBAAiB,CAAC,EAC/B,MAAM,CAAC,MAAM,IAAI,CAAC,eAAe,CAAC,MAAM,GAAG,CAAC,CAAC,EAC7C,GAAG,CAAC,MAAM,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,iBAAiB,EAAE,CAAC;aAE7D,SAAS,CAAC,WAAW,IAAG;;;AAGvB,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;AAC/C,gBAAA,MAAM,KAAK,GAAG,CAAC,IAAI,CAAC,kBAAkB,GAAG,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM;gBAChE,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC;AAE/B,gBAAA,IACE,CAAC,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;AAC9B,oBAAA,IAAI,CAAC,QAAQ,IAAI,CAAC,iBAAiB,EAAE,CAAC,IAAI,EAAE,CAAC,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,EACvE;AACA,oBAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC;oBAC7B;;;AAIJ,YAAA,IAAI,CAAC,eAAe,GAAG,EAAE;AAC3B,SAAC,CAAC;;AAEP;;;;"}