//
// Design system display name: Material 3
// Design system version: v0.161
//

@use 'sass:map';

@use './md-sys-color';

@use './md-sys-elevation';

@use './md-sys-shape';

@use './md-sys-state';

@use './md-sys-typescale';

$_default: (
  'md-sys-color': md-sys-color.values-light(),
  'md-sys-elevation': md-sys-elevation.values(),
  'md-sys-shape': md-sys-shape.values(),
  'md-sys-state': md-sys-state.values(),
  'md-sys-typescale': md-sys-typescale.values(),
);

@function values($deps: $_default, $exclude-hardcoded-values: false) {
  @return (
    'container-color': map.get($deps, 'md-sys-color', 'surface'),
    'container-elevation': map.get($deps, 'md-sys-elevation', 'level3'),
    'container-height': if($exclude-hardcoded-values, null, 56px),
    'container-shadow-color': map.get($deps, 'md-sys-color', 'shadow'),
    'container-shape': map.get($deps, 'md-sys-shape', 'corner-large'),
    'container-surface-tint-layer-color':
      map.get($deps, 'md-sys-color', 'surface-tint'),
    'focus-container-elevation': map.get($deps, 'md-sys-elevation', 'level3'),
    'focus-icon-color': map.get($deps, 'md-sys-color', 'primary'),
    'focus-label-text-color': map.get($deps, 'md-sys-color', 'primary'),
    'focus-state-layer-color': map.get($deps, 'md-sys-color', 'primary'),
    'focus-state-layer-opacity':
      map.get($deps, 'md-sys-state', 'focus-state-layer-opacity'),
    'hover-container-elevation': map.get($deps, 'md-sys-elevation', 'level4'),
    'hover-icon-color': map.get($deps, 'md-sys-color', 'primary'),
    'hover-label-text-color': map.get($deps, 'md-sys-color', 'primary'),
    'hover-state-layer-color': map.get($deps, 'md-sys-color', 'primary'),
    'hover-state-layer-opacity':
      map.get($deps, 'md-sys-state', 'hover-state-layer-opacity'),
    'icon-color': map.get($deps, 'md-sys-color', 'primary'),
    'icon-size': if($exclude-hardcoded-values, null, 24px),
    'label-text-color': map.get($deps, 'md-sys-color', 'primary'),
    'label-text-font': map.get($deps, 'md-sys-typescale', 'label-large-font'),
    'label-text-line-height':
      map.get($deps, 'md-sys-typescale', 'label-large-line-height'),
    'label-text-size': map.get($deps, 'md-sys-typescale', 'label-large-size'),
    'label-text-tracking':
      map.get($deps, 'md-sys-typescale', 'label-large-tracking'),
    // Warning: risk of reduced fidelity from using this composite typography token.
    // Tokens md.comp.extended-fab.surface.label-text.tracking cannot be represented in
    // the "font" property shorthand. Consider using the discrete properties instead.
    'label-text-type':
      if(
        $exclude-hardcoded-values,
        null,
          map.get($deps, 'md-sys-typescale', 'label-large-weight')
          map.get($deps, 'md-sys-typescale', 'label-large-size') #{'/'} map.get(
            $deps,
            'md-sys-typescale',
            'label-large-line-height'
          ) map.get($deps, 'md-sys-typescale', 'label-large-font')
      ),
    'label-text-weight':
      map.get($deps, 'md-sys-typescale', 'label-large-weight'),
    'lowered-container-elevation': map.get($deps, 'md-sys-elevation', 'level1'),
    'lowered-focus-container-elevation':
      map.get($deps, 'md-sys-elevation', 'level1'),
    'lowered-hover-container-elevation':
      map.get($deps, 'md-sys-elevation', 'level2'),
    'lowered-pressed-container-elevation':
      map.get($deps, 'md-sys-elevation', 'level1'),
    'pressed-container-elevation': map.get($deps, 'md-sys-elevation', 'level3'),
    'pressed-icon-color': map.get($deps, 'md-sys-color', 'primary'),
    'pressed-label-text-color': map.get($deps, 'md-sys-color', 'primary'),
    'pressed-state-layer-color': map.get($deps, 'md-sys-color', 'primary'),
    'pressed-state-layer-opacity':
      map.get($deps, 'md-sys-state', 'pressed-state-layer-opacity')
  );
}
