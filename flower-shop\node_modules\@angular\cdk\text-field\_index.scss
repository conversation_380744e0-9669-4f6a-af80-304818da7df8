// Structural styles for the autosize text fields.
@mixin text-field-autosize() {
  // Remove the resize handle on autosizing textareas, because whatever height
  // the user resized to will be overwritten once they start typing again.
  textarea.cdk-textarea-autosize {
    resize: none;
  }

  // This class is temporarily applied to the textarea when it is being measured. It is immediately
  // removed when measuring is complete. We use `!important` rules here to make sure user-specified
  // rules do not interfere with the measurement.
  textarea.cdk-textarea-autosize-measuring {
    @include _autosize-measuring-base;
    height: auto !important;
    overflow: hidden !important;
  }

  // Similar to the `cdk-textarea-autosize-measuring` class, but only applied on Firefox. We need
  // to use this class, because Firefox has a bug where changing the `overflow` breaks the user's
  // ability to undo/redo what they were typing (see #16629). This class is only scoped to Firefox,
  // because the measurements there don't seem to be affected by the `height: 0`, whereas on other
  // browsers they are, e.g. Ch<PERSON> detects longer text and IE does't resize back to normal.
  // Identical issue report: https://bugzilla.mozilla.org/show_bug.cgi?id=448784
  textarea.cdk-textarea-autosize-measuring-firefox {
    @include _autosize-measuring-base;
    height: 0 !important;
  }
}

// Core styles that enable monitoring autofill state of text fields.
@mixin text-field-autofill() {
  // Keyframes that apply no styles, but allow us to monitor when a text field becomes autofilled
  // by watching for the animation events that are fired when they start. Note: the /*!*/ comment is
  // needed to prevent LibSass from stripping the keyframes out.
  // Based on: https://medium.com/@brunn/detecting-autofilled-fields-in-javascript-aed598d25da7
  @keyframes cdk-text-field-autofill-start {/*!*/}
  @keyframes cdk-text-field-autofill-end {/*!*/}

  .cdk-text-field-autofill-monitored:-webkit-autofill {
    // Since Chrome 80 we need a 1ms delay, or the animationstart event won't fire.
    animation: cdk-text-field-autofill-start 0s 1ms;
  }

  .cdk-text-field-autofill-monitored:not(:-webkit-autofill) {
    // Since Chrome 80 we need a 1ms delay, or the animationstart event won't fire.
    animation: cdk-text-field-autofill-end 0s 1ms;
  }
}

@mixin _autosize-measuring-base {
  // Having 2px top and bottom padding seems to fix a bug where Chrome gets an incorrect
  // measurement. We just have to account for it later and subtract it off the final result.
  padding: 2px 0 !important;
  box-sizing: content-box !important;
}

// Used to generate UIDs for keyframes used to change the text field autofill styles.
$autofill-color-frame-count: 0;

// Mixin used to apply custom background and foreground colors to an autofilled text field.
// Based on: https://stackoverflow.com/questions/2781549/
// removing-input-background-colour-for-chrome-autocomplete#answer-********
@mixin text-field-autofill-color($background, $foreground:'') {
  @keyframes cdk-text-field-autofill-color-#{$autofill-color-frame-count} {
    to {
      background: $background;
      @if $foreground != '' { color: $foreground; }
    }
  }

  &:-webkit-autofill {
    animation: cdk-text-field-autofill-color-#{$autofill-color-frame-count} both;
  }

  &.cdk-text-field-autofill-monitored:-webkit-autofill {
    // Since Chrome 80 we need a 1ms delay for cdk-text-field-autofill-start, or the animationstart
    // event won't fire.
    animation: cdk-text-field-autofill-start 0s 1ms,
               cdk-text-field-autofill-color-#{$autofill-color-frame-count} both;
  }

  $autofill-color-frame-count: $autofill-color-frame-count + 1 !global;
}

// @deprecated Use `autosize` and `autofill` instead.
@mixin text-field {
  @include text-field-autosize();
  @include text-field-autofill();
}
