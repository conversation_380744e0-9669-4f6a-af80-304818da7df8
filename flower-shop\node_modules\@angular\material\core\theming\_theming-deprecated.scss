@use '../m2/theming' as m2-theming;
@use '../m2/palette' as m2-palette;

// @deprecated Use `get-color-from-palette`.
@function color($palette, $hue: default, $opacity: null) {
  @return m2-theming.get-color-from-palette($palette, $hue, $opacity);
}

// @deprecated Use `get-contrast-color-from-palette`.
@function contrast($palette, $hue) {
  @return m2-theming.get-contrast-color-from-palette($palette, $hue);
}

// @deprecated Use `define-palette`.
@function palette($base-palette, $default: 500, $lighter: 100, $darker: 700, $text: $default) {
  @return m2-theming.define-palette($base-palette, $default, $lighter, $darker, $text);
}

// @deprecated Use `define-light-theme`.
@function dark-theme($primary, $accent: null, $warn: palette(m2-palette.$red-palette)) {
  @return m2-theming.define-dark-theme($primary, $accent, $warn);
}

// @deprecated Use `define-light-theme`.
@function light-theme($primary, $accent: null, $warn: palette(m2-palette.$red-palette)) {
  @return m2-theming.define-light-theme($primary, $accent, $warn);
}
