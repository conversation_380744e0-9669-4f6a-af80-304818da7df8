import { Component } from '@angular/core';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { CommonModule } from '@angular/common';
import { AuthService } from '../../services/auth.service';

@Component({
    selector: 'app-login',
    imports: [
        CommonModule,
        ReactiveFormsModule,
        MatCardModule,
        MatFormFieldModule,
        MatInputModule,
        MatButtonModule,
        MatSnackBarModule
    ],
    templateUrl: './login.component.html',
    styleUrl: './login.component.scss'
})
export class LoginComponent {
    loginForm: FormGroup;
    isLoading = false;

    constructor(
        private fb: FormBuilder,
        private authService: AuthService,
        private router: Router,
        private snackBar: MatSnackBar
    ) {
        this.loginForm = this.fb.group({
            email: ['', [Validators.required, Validators.email]],
            password: ['', [Validators.required, Validators.minLength(3)]]
        });
    }

    onSubmit(): void {
        if (this.loginForm.valid) {
            this.isLoading = true;
            const credentials = this.loginForm.value;

            this.authService.login(credentials).subscribe({
                next: (user) => {
                    this.isLoading = false;
                    if (user) {
                        this.snackBar.open('Login successful!', 'Close', { duration: 3000 });
                        if (user.role === 'admin') {
                            this.router.navigate(['/admin']);
                        } else {
                            this.router.navigate(['/']);
                        }
                    } else {
                        this.snackBar.open('Invalid credentials', 'Close', { duration: 3000 });
                    }
                },
                error: () => {
                    this.isLoading = false;
                    this.snackBar.open('Login failed', 'Close', { duration: 3000 });
                }
            });
        }
    }

    goToRegister(): void {
        this.router.navigate(['/register']);
    }
}
