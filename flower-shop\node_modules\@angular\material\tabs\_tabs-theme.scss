@use '../core/style/sass-utils';
@use '../core/tokens/m2/mdc/secondary-navigation-tab' as tokens-mdc-secondary-navigation-tab;
@use '../core/tokens/m2/mdc/tab-indicator' as tokens-mdc-tab-indicator;
@use '../core/tokens/m2/mat/tab-header' as tokens-mat-tab-header;
@use '../core/tokens/m2/mat/tab-header-with-background' as tokens-mat-tab-header-with-background;
@use '../core/theming/theming';
@use '../core/theming/inspection';
@use '../core/theming/validation';
@use '../core/typography/typography';
@use '../core/tokens/token-utils';

/// Outputs base theme styles (styles not dependent on the color, typography, or density settings)
/// for the mat-tab.
/// @param {Map} $theme The theme to generate base styles for.
@mixin base($theme) {
  @if inspection.get-theme-version($theme) == 1 {
    @include _theme-from-tokens(inspection.get-theme-tokens($theme, base));
  } @else {
    @include sass-utils.current-selector-or-root() {
      @include token-utils.create-token-values(
        tokens-mdc-secondary-navigation-tab.$prefix,
        tokens-mdc-secondary-navigation-tab.get-unthemable-tokens()
      );
      @include token-utils.create-token-values(
        tokens-mdc-tab-indicator.$prefix,
        tokens-mdc-tab-indicator.get-unthemable-tokens()
      );
      @include token-utils.create-token-values(
        tokens-mat-tab-header.$prefix,
        tokens-mat-tab-header.get-unthemable-tokens()
      );
      @include token-utils.create-token-values(
        tokens-mat-tab-header-with-background.$prefix,
        tokens-mat-tab-header-with-background.get-unthemable-tokens()
      );
    }
  }
}

/// Outputs color theme styles for the mat-tab.
/// @param {Map} $theme The theme to generate color styles for.
/// @param {ArgList} Additional optional arguments (only supported for M3 themes):
///   $color-variant: The color variant to use for the tab indicator: primary, secondary,
///      tertiary, or error (If not specified, default primary color will be used).
@mixin color($theme, $options...) {
  @if inspection.get-theme-version($theme) == 1 {
    @include _theme-from-tokens(inspection.get-theme-tokens($theme, color), $options...);
  } @else {
    .mat-mdc-tab-group,
    .mat-mdc-tab-nav-bar {
      @include _palette-styles($theme, primary);

      &.mat-accent {
        @include _palette-styles($theme, accent);
      }

      &.mat-warn {
        @include _palette-styles($theme, warn);
      }

      &.mat-background-primary {
        @include _background-styles($theme, primary);
      }

      &.mat-background-accent {
        @include _background-styles($theme, accent);
      }

      &.mat-background-warn {
        @include _background-styles($theme, warn);
      }
    }
  }
}

@mixin _background-styles($theme, $palette-name) {
  @include token-utils.create-token-values(
    tokens-mat-tab-header-with-background.$prefix,
    tokens-mat-tab-header-with-background.get-color-tokens($theme, $palette-name)
  );
}

@mixin _palette-styles($theme, $palette-name) {
  @include token-utils.create-token-values(
    tokens-mdc-secondary-navigation-tab.$prefix,
    tokens-mdc-secondary-navigation-tab.get-color-tokens($theme, $palette-name)
  );
  @include token-utils.create-token-values(
    tokens-mdc-tab-indicator.$prefix,
    tokens-mdc-tab-indicator.get-color-tokens($theme, $palette-name)
  );
  @include token-utils.create-token-values(
    tokens-mat-tab-header.$prefix,
    tokens-mat-tab-header.get-color-tokens($theme, $palette-name)
  );
}

/// Outputs typography theme styles for the mat-tab.
/// @param {Map} $theme The theme to generate typography styles for.
@mixin typography($theme) {
  @if inspection.get-theme-version($theme) == 1 {
    @include _theme-from-tokens(inspection.get-theme-tokens($theme, typography));
  } @else {
    .mat-mdc-tab-header {
      @include token-utils.create-token-values(
        tokens-mdc-secondary-navigation-tab.$prefix,
        tokens-mdc-secondary-navigation-tab.get-typography-tokens($theme)
      );
      @include token-utils.create-token-values(
        tokens-mdc-tab-indicator.$prefix,
        tokens-mdc-tab-indicator.get-typography-tokens($theme)
      );
      @include token-utils.create-token-values(
        tokens-mat-tab-header.$prefix,
        tokens-mat-tab-header.get-typography-tokens($theme)
      );
      @include token-utils.create-token-values(
        tokens-mat-tab-header-with-background.$prefix,
        tokens-mat-tab-header-with-background.get-typography-tokens($theme)
      );
    }
  }
}

/// Outputs density theme styles for the mat-tab.
/// @param {Map} $theme The theme to generate density styles for.
@mixin density($theme) {
  @if inspection.get-theme-version($theme) == 1 {
    @include _theme-from-tokens(inspection.get-theme-tokens($theme, density));
  } @else {
    .mat-mdc-tab-header {
      @include token-utils.create-token-values(
        tokens-mdc-secondary-navigation-tab.$prefix,
        tokens-mdc-secondary-navigation-tab.get-density-tokens($theme)
      );
      @include token-utils.create-token-values(
        tokens-mdc-tab-indicator.$prefix,
        tokens-mdc-tab-indicator.get-density-tokens($theme)
      );
      @include token-utils.create-token-values(
        tokens-mat-tab-header.$prefix,
        tokens-mat-tab-header.get-density-tokens($theme)
      );
      @include token-utils.create-token-values(
        tokens-mat-tab-header-with-background.$prefix,
        tokens-mat-tab-header-with-background.get-density-tokens($theme)
      );
    }
  }
}

/// Defines the tokens that will be available in the `overrides` mixin and for docs extraction.
@function _define-overrides() {
  $tab-tokens: tokens-mdc-secondary-navigation-tab.get-token-slots();
  $tab-indicator-tokens: tokens-mdc-tab-indicator.get-token-slots();
  $tab-header-tokens: tokens-mat-tab-header.get-token-slots();
  $tab-header-with-background-tokens: tokens-mat-tab-header-with-background.get-token-slots();

  @return (
    (
      namespace: tokens-mdc-secondary-navigation-tab.$prefix,
      tokens: $tab-tokens,
    ),
    (
      namespace: tokens-mdc-tab-indicator.$prefix,
      tokens: $tab-indicator-tokens,
    ),
    (
      namespace: tokens-mat-tab-header.$prefix,
      tokens: $tab-header-tokens,
    ),
    (
      namespace: tokens-mat-tab-header-with-background.$prefix,
      tokens: $tab-header-with-background-tokens,
    ),
  );
}

/// Outputs the CSS variable values for the given tokens.
/// @param {Map} $tokens The token values to emit.
@mixin overrides($tokens: ()) {
  @include token-utils.batch-create-token-values($tokens, _define-overrides()...);
}

/// Outputs all (base, color, typography, and density) theme styles for the mat-tab.
/// @param {Map} $theme The theme to generate styles for.
/// @param {ArgList} Additional optional arguments (only supported for M3 themes):
///   $color-variant: The color variant to use for the tab indicator: primary, secondary,
///      tertiary, or error (If not specified, default primary color will be used).
@mixin theme($theme, $options...) {
  @include theming.private-check-duplicate-theme-styles($theme, 'mat-tabs') {
    @if inspection.get-theme-version($theme) == 1 {
      @include _theme-from-tokens(inspection.get-theme-tokens($theme), $options...);
    } @else {
      @include base($theme);
      @if inspection.theme-has($theme, color) {
        @include color($theme);
      }
      @if inspection.theme-has($theme, density) {
        @include density($theme);
      }
      @if inspection.theme-has($theme, typography) {
        @include typography($theme);
      }
    }
  }
}

@mixin _theme-from-tokens($tokens, $options...) {
  @include validation.selector-defined(
    'Calls to Angular Material theme mixins with an M3 theme must be wrapped in a selector'
  );
  $mdc-tab-indicator-tokens: token-utils.get-tokens-for(
    $tokens,
    tokens-mdc-tab-indicator.$prefix,
    $options...
  );
  $mat-tab-header-tokens: token-utils.get-tokens-for(
    $tokens,
    tokens-mat-tab-header.$prefix,
    $options...
  );
  // Don't pass $options here, because the mdc-tab doesn't have color variants,
  // only the mdc-tab-indicator and mat-tab-header do.
  $mdc-secondary-navigation-tab-tokens: token-utils.get-tokens-for(
    $tokens,
    tokens-mdc-secondary-navigation-tab.$prefix
  );
  @include token-utils.create-token-values(
    tokens-mdc-secondary-navigation-tab.$prefix,
    $mdc-secondary-navigation-tab-tokens
  );
  @include token-utils.create-token-values(
    tokens-mdc-tab-indicator.$prefix,
    $mdc-tab-indicator-tokens
  );
  @include token-utils.create-token-values(tokens-mat-tab-header.$prefix, $mat-tab-header-tokens);
}
