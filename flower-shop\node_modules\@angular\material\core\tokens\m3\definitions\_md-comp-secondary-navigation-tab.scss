//
// Design system display name: Material 3
// Design system version: v0.161
//

@use 'sass:map';

@use './md-sys-color';

@use './md-sys-elevation';

@use './md-sys-shape';

@use './md-sys-state';

@use './md-sys-typescale';

$_default: (
  'md-sys-color': md-sys-color.values-light(),
  'md-sys-elevation': md-sys-elevation.values(),
  'md-sys-shape': md-sys-shape.values(),
  'md-sys-state': md-sys-state.values(),
  'md-sys-typescale': md-sys-typescale.values(),
);

@function values($deps: $_default, $exclude-hardcoded-values: false) {
  @return (
    'active-label-text-color': map.get($deps, 'md-sys-color', 'on-surface'),
    'container-color': map.get($deps, 'md-sys-color', 'surface'),
    'container-elevation': map.get($deps, 'md-sys-elevation', 'level0'),
    'container-height': if($exclude-hardcoded-values, null, 48px),
    'container-shadow-color': map.get($deps, 'md-sys-color', 'shadow'),
    'container-shape': map.get($deps, 'md-sys-shape', 'corner-none'),
    'divider-color': map.get($deps, 'md-sys-color', 'surface-variant'),
    'divider-height': if($exclude-hardcoded-values, null, 1px),
    'focus-label-text-color': map.get($deps, 'md-sys-color', 'on-surface'),
    'focus-state-layer-color': map.get($deps, 'md-sys-color', 'on-surface'),
    'focus-state-layer-opacity':
      map.get($deps, 'md-sys-state', 'focus-state-layer-opacity'),
    'hover-label-text-color': map.get($deps, 'md-sys-color', 'on-surface'),
    'hover-state-layer-color': map.get($deps, 'md-sys-color', 'on-surface'),
    'hover-state-layer-opacity':
      map.get($deps, 'md-sys-state', 'hover-state-layer-opacity'),
    'inactive-label-text-color':
      map.get($deps, 'md-sys-color', 'on-surface-variant'),
    'label-text-font': map.get($deps, 'md-sys-typescale', 'title-small-font'),
    'label-text-line-height':
      map.get($deps, 'md-sys-typescale', 'title-small-line-height'),
    'label-text-size': map.get($deps, 'md-sys-typescale', 'title-small-size'),
    'label-text-tracking':
      map.get($deps, 'md-sys-typescale', 'title-small-tracking'),
    // Warning: risk of reduced fidelity from using this composite typography token.
    // Tokens md.comp.secondary-navigation-tab.label-text.tracking cannot be represented
    // in the "font" property shorthand. Consider using the discrete properties instead.
    'label-text-type':
      if(
        $exclude-hardcoded-values,
        null,
          map.get($deps, 'md-sys-typescale', 'title-small-weight')
          map.get($deps, 'md-sys-typescale', 'title-small-size') #{'/'} map.get(
            $deps,
            'md-sys-typescale',
            'title-small-line-height'
          ) map.get($deps, 'md-sys-typescale', 'title-small-font')
      ),
    'label-text-weight':
      map.get($deps, 'md-sys-typescale', 'title-small-weight'),
    'pressed-label-text-color': map.get($deps, 'md-sys-color', 'on-surface'),
    'pressed-state-layer-color': map.get($deps, 'md-sys-color', 'on-surface'),
    'pressed-state-layer-opacity':
      map.get($deps, 'md-sys-state', 'pressed-state-layer-opacity'),
    'with-icon-active-icon-color': map.get($deps, 'md-sys-color', 'on-surface'),
    'with-icon-focus-icon-color': map.get($deps, 'md-sys-color', 'on-surface'),
    'with-icon-hover-icon-color': map.get($deps, 'md-sys-color', 'on-surface'),
    'with-icon-icon-size': if($exclude-hardcoded-values, null, 24px),
    'with-icon-inactive-icon-color':
      map.get($deps, 'md-sys-color', 'on-surface-variant'),
    'with-icon-pressed-icon-color': map.get($deps, 'md-sys-color', 'on-surface')
  );
}
