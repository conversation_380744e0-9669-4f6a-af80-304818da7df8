<div class="home-container">
    <!-- Hero Section -->
    <div class="hero-section">
        <h1>Welcome to Our Flower Shop</h1>
        <p>Discover beautiful flowers for every occasion</p>
    </div>

    <!-- Filters Section -->
    <div class="filters-section">
        <mat-card class="filters-card">
            <mat-card-content>
                <div class="filters-row">
                    <mat-form-field appearance="outline" class="search-field">
                        <mat-label>Search flowers</mat-label>
                        <input matInput [(ngModel)]="searchTerm" (ngModelChange)="onSearchChange()"
                            placeholder="Search by name or description">
                        <mat-icon matSuffix>search</mat-icon>
                    </mat-form-field>

                    <mat-form-field appearance="outline" class="category-field">
                        <mat-label>Category</mat-label>
                        <mat-select [(value)]="selectedCategory" (selectionChange)="onCategoryChange()">
                            <mat-option value="">All Categories</mat-option>
                            <mat-option *ngFor="let category of categories$ | async" [value]="category">
                                {{ category }}
                            </mat-option>
                        </mat-select>
                    </mat-form-field>

                    <div class="price-filters">
                        <mat-form-field appearance="outline" class="price-field">
                            <mat-label>Min Price</mat-label>
                            <input matInput type="number" [(ngModel)]="minPrice" (ngModelChange)="onPriceChange()"
                                min="0" max="200">
                        </mat-form-field>

                        <mat-form-field appearance="outline" class="price-field">
                            <mat-label>Max Price</mat-label>
                            <input matInput type="number" [(ngModel)]="maxPrice" (ngModelChange)="onPriceChange()"
                                min="0" max="200">
                        </mat-form-field>
                    </div>

                    <button mat-stroked-button (click)="clearFilters()" class="clear-filters-btn">
                        Clear Filters
                    </button>
                </div>
            </mat-card-content>
        </mat-card>
    </div>

    <!-- Flowers Grid -->
    <div class="flowers-section">
        <div class="flowers-grid">
            <mat-card *ngFor="let flower of flowers$ | async" class="flower-card">
                <div class="flower-image-container">
                    <img mat-card-image [src]="flower.imageUrl" [alt]="flower.name"
                        (error)="$any($event.target).src='https://via.placeholder.com/400x400?text=No+Image'">
                    <div class="stock-badge" [class.out-of-stock]="!flower.inStock">
                        {{ flower.inStock ? 'In Stock' : 'Out of Stock' }}
                    </div>
                </div>

                <mat-card-header>
                    <mat-card-title>{{ flower.name }}</mat-card-title>
                    <mat-card-subtitle>{{ flower.category }}</mat-card-subtitle>
                </mat-card-header>

                <mat-card-content>
                    <p class="flower-description">{{ flower.description }}</p>
                    <div class="price-section">
                        <span class="price">${{ flower.price | number:'1.2-2' }}</span>
                    </div>
                </mat-card-content>

                <mat-card-actions>
                    <button mat-raised-button color="primary" [disabled]="!flower.inStock" (click)="addToCart(flower)">
                        <mat-icon>add_shopping_cart</mat-icon>
                        Add to Cart
                    </button>

                    <div *ngIf="(getItemQuantity(flower) | async) as quantity" class="quantity-display">
                        <mat-chip-set>
                            <mat-chip *ngIf="quantity > 0">
                                In cart: {{ quantity }}
                            </mat-chip>
                        </mat-chip-set>
                    </div>
                </mat-card-actions>
            </mat-card>
        </div>

        <!-- No results message -->
        <div *ngIf="(flowers$ | async)?.length === 0" class="no-results">
            <mat-card>
                <mat-card-content>
                    <div class="no-results-content">
                        <mat-icon>search_off</mat-icon>
                        <h3>No flowers found</h3>
                        <p>Try adjusting your search criteria or clear the filters.</p>
                        <button mat-raised-button color="primary" (click)="clearFilters()">
                            Clear Filters
                        </button>
                    </div>
                </mat-card-content>
            </mat-card>
        </div>
    </div>
</div>
