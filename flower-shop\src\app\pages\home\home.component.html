<!-- Hero Section -->
<section class="hero-section">
    <div class="container">
        <div class="hero-content">
            <h1 class="hero-title font-display">Welcome to Petals & Blooms</h1>
            <p class="hero-subtitle">Discover our beautiful collection of fresh flowers for every occasion</p>

            <!-- Search and Filter -->
            <div class="search-filters">
                <mat-form-field class="search-field">
                    <mat-label>Search flowers...</mat-label>
                    <input matInput [(ngModel)]="searchQuery" (keyup.enter)="onSearch()"
                        placeholder="<PERSON>, Tulip, Lily...">
                    <mat-icon matSuffix (click)="onSearch()">search</mat-icon>
                </mat-form-field>

                <div class="category-filters">
                    <button mat-button [class.active]="selectedCategory === ''" (click)="onCategoryFilter('')">
                        All
                    </button>
                    <button mat-button *ngFor="let category of categories"
                        [class.active]="selectedCategory === category" (click)="onCategoryFilter(category)">
                        {{ category }}
                    </button>
                </div>

                <button mat-button (click)="clearFilters()" class="clear-filters">
                    <mat-icon>clear</mat-icon>
                    Clear Filters
                </button>
            </div>
        </div>
    </div>
</section>

<!-- Flowers Grid -->
<section class="flowers-section">
    <div class="container">
        <div class="flowers-grid">
            <mat-card *ngFor="let flower of flowers$ | async" class="flower-card modern-card card-hover">
                <div class="flower-image-container">
                    <img mat-card-image [src]="flower.imageUrl" [alt]="flower.name" class="flower-image">
                    <div class="flower-overlay" *ngIf="currentUser$ | async">
                        <button mat-fab color="accent" (click)="addToCart(flower)" class="add-to-cart-btn">
                            <mat-icon>add_shopping_cart</mat-icon>
                        </button>
                    </div>
                </div>

                <mat-card-content>
                    <div class="flower-info">
                        <h3 class="flower-name font-display">{{ flower.name }}</h3>
                        <p class="flower-category">{{ flower.category }}</p>
                        <p class="flower-description">{{ flower.description }}</p>
                        <div class="flower-price">
                            <span class="price">${{ flower.price | number:'1.2-2' }}</span>
                            <span class="stock-status" [class.in-stock]="flower.inStock"
                                [class.out-of-stock]="!flower.inStock">
                                {{ flower.inStock ? 'In Stock' : 'Out of Stock' }}
                            </span>
                        </div>
                    </div>
                </mat-card-content>

                <mat-card-actions *ngIf="currentUser$ | async">
                    <button mat-button color="primary" (click)="addToCart(flower)" [disabled]="!flower.inStock">
                        <mat-icon>add_shopping_cart</mat-icon>
                        Add to Cart
                    </button>
                    <button mat-button>
                        <mat-icon>visibility</mat-icon>
                        View Details
                    </button>
                </mat-card-actions>

                <mat-card-actions *ngIf="!(currentUser$ | async)">
                    <p class="login-prompt">
                        <a href="/login">Login</a> to purchase flowers
                    </p>
                </mat-card-actions>
            </mat-card>
        </div>

        <!-- Empty State -->
        <div *ngIf="(flowers$ | async)?.length === 0" class="empty-state">
            <mat-icon class="empty-icon">local_florist</mat-icon>
            <h3>No flowers found</h3>
            <p>Try adjusting your search or filter criteria</p>
            <button mat-raised-button color="primary" (click)="clearFilters()">
                Show All Flowers
            </button>
        </div>
    </div>
</section>
