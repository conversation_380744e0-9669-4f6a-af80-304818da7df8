{"version": 3, "file": "ripple-BT3tzh6F.mjs", "sources": ["../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/core/ripple/ripple-ref.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/core/ripple/ripple-event-manager.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/core/ripple/ripple-renderer.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/core/ripple/ripple.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n/** Possible states for a ripple element. */\nexport enum RippleState {\n  FADING_IN,\n  VISIBLE,\n  FADING_OUT,\n  HIDDEN,\n}\n\nexport type RippleConfig = {\n  color?: string;\n  centered?: boolean;\n  radius?: number;\n  persistent?: boolean;\n  animation?: RippleAnimationConfig;\n  terminateOnPointerUp?: boolean;\n};\n\n/**\n * Interface that describes the configuration for the animation of a ripple.\n * There are two animation phases with different durations for the ripples.\n */\nexport interface RippleAnimationConfig {\n  /** Duration in milliseconds for the enter animation (expansion from point of contact). */\n  enterDuration?: number;\n  /** Duration in milliseconds for the exit animation (fade-out). */\n  exitDuration?: number;\n}\n\n/**\n * Reference to a previously launched ripple element.\n */\nexport class RippleRef {\n  /** Current state of the ripple. */\n  state: RippleState = RippleState.HIDDEN;\n\n  constructor(\n    private _renderer: {fadeOutRipple(ref: RippleRef): void},\n    /** Reference to the ripple HTML element. */\n    public element: HTMLElement,\n    /** Ripple configuration used for the ripple. */\n    public config: RippleConfig,\n    /* Whether animations are forcibly disabled for ripples through CSS. */\n    public _animationForciblyDisabledThroughCss = false,\n  ) {}\n\n  /** Fades out the ripple element. */\n  fadeOut() {\n    this._renderer.fadeOutRipple(this);\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {normalizePassiveListenerOptions, _getEventTarget} from '@angular/cdk/platform';\nimport {NgZone} from '@angular/core';\n\n/** Options used to bind a passive capturing event. */\nconst passiveCapturingEventOptions = normalizePassiveListenerOptions({\n  passive: true,\n  capture: true,\n});\n\n/** Manages events through delegation so that as few event handlers as possible are bound. */\nexport class RippleEventManager {\n  private _events = new Map<string, Map<HTMLElement, Set<EventListenerObject>>>();\n\n  /** Adds an event handler. */\n  addHandler(ngZone: NgZone, name: string, element: HTMLElement, handler: EventListenerObject) {\n    const handlersForEvent = this._events.get(name);\n\n    if (handlersForEvent) {\n      const handlersForElement = handlersForEvent.get(element);\n\n      if (handlersForElement) {\n        handlersForElement.add(handler);\n      } else {\n        handlersForEvent.set(element, new Set([handler]));\n      }\n    } else {\n      this._events.set(name, new Map([[element, new Set([handler])]]));\n\n      ngZone.runOutsideAngular(() => {\n        document.addEventListener(name, this._delegateEventHandler, passiveCapturingEventOptions);\n      });\n    }\n  }\n\n  /** Removes an event handler. */\n  removeHandler(name: string, element: HTMLElement, handler: EventListenerObject) {\n    const handlersForEvent = this._events.get(name);\n\n    if (!handlersForEvent) {\n      return;\n    }\n\n    const handlersForElement = handlersForEvent.get(element);\n\n    if (!handlersForElement) {\n      return;\n    }\n\n    handlersForElement.delete(handler);\n\n    if (handlersForElement.size === 0) {\n      handlersForEvent.delete(element);\n    }\n\n    if (handlersForEvent.size === 0) {\n      this._events.delete(name);\n      document.removeEventListener(name, this._delegateEventHandler, passiveCapturingEventOptions);\n    }\n  }\n\n  /** Event handler that is bound and which dispatches the events to the different targets. */\n  private _delegateEventHandler = (event: Event) => {\n    const target = _getEventTarget(event);\n\n    if (target) {\n      this._events.get(event.type)?.forEach((handlers, element) => {\n        if (element === target || element.contains(target as Node)) {\n          handlers.forEach(handler => handler.handleEvent(event));\n        }\n      });\n    }\n  };\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\nimport {\n  ElementRef,\n  NgZone,\n  Component,\n  ChangeDetectionStrategy,\n  ViewEncapsulation,\n  Injector,\n} from '@angular/core';\nimport {Platform, normalizePassiveListenerOptions, _getEventTarget} from '@angular/cdk/platform';\nimport {isFakeMousedownFromScreenReader, isFakeTouchstartFromScreenReader} from '@angular/cdk/a11y';\nimport {coerceElement} from '@angular/cdk/coercion';\nimport {_CdkPrivateStyleLoader} from '@angular/cdk/private';\nimport {RippleRef, RippleState, RippleConfig} from './ripple-ref';\nimport {RippleEventManager} from './ripple-event-manager';\n\n/**\n * Interface that describes the target for launching ripples.\n * It defines the ripple configuration and disabled state for interaction ripples.\n * @docs-private\n */\nexport interface RippleTarget {\n  /** Configuration for ripples that are launched on pointer down. */\n  rippleConfig: RippleConfig;\n  /** Whether ripples on pointer down should be disabled. */\n  rippleDisabled: boolean;\n}\n\n/** Interfaces the defines ripple element transition event listeners. */\ninterface RippleEventListeners {\n  onTransitionEnd: EventListener;\n  onTransitionCancel: EventListener;\n  fallbackTimer: ReturnType<typeof setTimeout> | null;\n}\n\n/**\n * Default ripple animation configuration for ripples without an explicit\n * animation config specified.\n */\nexport const defaultRippleAnimationConfig = {\n  enterDuration: 225,\n  exitDuration: 150,\n};\n\n/**\n * Timeout for ignoring mouse events. Mouse events will be temporary ignored after touch\n * events to avoid synthetic mouse events.\n */\nconst ignoreMouseEventsTimeout = 800;\n\n/** Options used to bind a passive capturing event. */\nconst passiveCapturingEventOptions = normalizePassiveListenerOptions({\n  passive: true,\n  capture: true,\n});\n\n/** Events that signal that the pointer is down. */\nconst pointerDownEvents = ['mousedown', 'touchstart'];\n\n/** Events that signal that the pointer is up. */\nconst pointerUpEvents = ['mouseup', 'mouseleave', 'touchend', 'touchcancel'];\n\n@Component({\n  template: '',\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  encapsulation: ViewEncapsulation.None,\n  styleUrl: 'ripple-structure.css',\n  host: {'mat-ripple-style-loader': ''},\n})\nexport class _MatRippleStylesLoader {}\n\n/**\n * Helper service that performs DOM manipulations. Not intended to be used outside this module.\n * The constructor takes a reference to the ripple directive's host element and a map of DOM\n * event handlers to be installed on the element that triggers ripple animations.\n * This will eventually become a custom renderer once Angular support exists.\n * @docs-private\n */\nexport class RippleRenderer implements EventListenerObject {\n  /** Element where the ripples are being added to. */\n  private _containerElement: HTMLElement;\n\n  /** Element which triggers the ripple elements on mouse events. */\n  private _triggerElement: HTMLElement | null;\n\n  /** Whether the pointer is currently down or not. */\n  private _isPointerDown = false;\n\n  /**\n   * Map of currently active ripple references.\n   * The ripple reference is mapped to its element event listeners.\n   * The reason why `| null` is used is that event listeners are added only\n   * when the condition is truthy (see the `_startFadeOutTransition` method).\n   */\n  private _activeRipples = new Map<RippleRef, RippleEventListeners | null>();\n\n  /** Latest non-persistent ripple that was triggered. */\n  private _mostRecentTransientRipple: RippleRef | null;\n\n  /** Time in milliseconds when the last touchstart event happened. */\n  private _lastTouchStartEvent: number;\n\n  /** Whether pointer-up event listeners have been registered. */\n  private _pointerUpEventsRegistered = false;\n\n  /**\n   * Cached dimensions of the ripple container. Set when the first\n   * ripple is shown and cleared once no more ripples are visible.\n   */\n  private _containerRect: DOMRect | null;\n\n  private static _eventManager = new RippleEventManager();\n\n  constructor(\n    private _target: RippleTarget,\n    private _ngZone: NgZone,\n    elementOrElementRef: HTMLElement | ElementRef<HTMLElement>,\n    private _platform: Platform,\n    injector?: Injector,\n  ) {\n    // Only do anything if we're on the browser.\n    if (_platform.isBrowser) {\n      this._containerElement = coerceElement(elementOrElementRef);\n    }\n\n    if (injector) {\n      injector.get(_CdkPrivateStyleLoader).load(_MatRippleStylesLoader);\n    }\n  }\n\n  /**\n   * Fades in a ripple at the given coordinates.\n   * @param x Coordinate within the element, along the X axis at which to start the ripple.\n   * @param y Coordinate within the element, along the Y axis at which to start the ripple.\n   * @param config Extra ripple options.\n   */\n  fadeInRipple(x: number, y: number, config: RippleConfig = {}): RippleRef {\n    const containerRect = (this._containerRect =\n      this._containerRect || this._containerElement.getBoundingClientRect());\n    const animationConfig = {...defaultRippleAnimationConfig, ...config.animation};\n\n    if (config.centered) {\n      x = containerRect.left + containerRect.width / 2;\n      y = containerRect.top + containerRect.height / 2;\n    }\n\n    const radius = config.radius || distanceToFurthestCorner(x, y, containerRect);\n    const offsetX = x - containerRect.left;\n    const offsetY = y - containerRect.top;\n    const enterDuration = animationConfig.enterDuration;\n\n    const ripple = document.createElement('div');\n    ripple.classList.add('mat-ripple-element');\n\n    ripple.style.left = `${offsetX - radius}px`;\n    ripple.style.top = `${offsetY - radius}px`;\n    ripple.style.height = `${radius * 2}px`;\n    ripple.style.width = `${radius * 2}px`;\n\n    // If a custom color has been specified, set it as inline style. If no color is\n    // set, the default color will be applied through the ripple theme styles.\n    if (config.color != null) {\n      ripple.style.backgroundColor = config.color;\n    }\n\n    ripple.style.transitionDuration = `${enterDuration}ms`;\n\n    this._containerElement.appendChild(ripple);\n\n    // By default the browser does not recalculate the styles of dynamically created\n    // ripple elements. This is critical to ensure that the `scale` animates properly.\n    // We enforce a style recalculation by calling `getComputedStyle` and *accessing* a property.\n    // See: https://gist.github.com/paulirish/5d52fb081b3570c81e3a\n    const computedStyles = window.getComputedStyle(ripple);\n    const userTransitionProperty = computedStyles.transitionProperty;\n    const userTransitionDuration = computedStyles.transitionDuration;\n\n    // Note: We detect whether animation is forcibly disabled through CSS (e.g. through\n    // `transition: none` or `display: none`). This is technically unexpected since animations are\n    // controlled through the animation config, but this exists for backwards compatibility. This\n    // logic does not need to be super accurate since it covers some edge cases which can be easily\n    // avoided by users.\n    const animationForciblyDisabledThroughCss =\n      userTransitionProperty === 'none' ||\n      // Note: The canonical unit for serialized CSS `<time>` properties is seconds. Additionally\n      // some browsers expand the duration for every property (in our case `opacity` and `transform`).\n      userTransitionDuration === '0s' ||\n      userTransitionDuration === '0s, 0s' ||\n      // If the container is 0x0, it's likely `display: none`.\n      (containerRect.width === 0 && containerRect.height === 0);\n\n    // Exposed reference to the ripple that will be returned.\n    const rippleRef = new RippleRef(this, ripple, config, animationForciblyDisabledThroughCss);\n\n    // Start the enter animation by setting the transform/scale to 100%. The animation will\n    // execute as part of this statement because we forced a style recalculation before.\n    // Note: We use a 3d transform here in order to avoid an issue in Safari where\n    // the ripples aren't clipped when inside the shadow DOM (see #24028).\n    ripple.style.transform = 'scale3d(1, 1, 1)';\n\n    rippleRef.state = RippleState.FADING_IN;\n\n    if (!config.persistent) {\n      this._mostRecentTransientRipple = rippleRef;\n    }\n\n    let eventListeners: RippleEventListeners | null = null;\n\n    // Do not register the `transition` event listener if fade-in and fade-out duration\n    // are set to zero. The events won't fire anyway and we can save resources here.\n    if (!animationForciblyDisabledThroughCss && (enterDuration || animationConfig.exitDuration)) {\n      this._ngZone.runOutsideAngular(() => {\n        const onTransitionEnd = () => {\n          // Clear the fallback timer since the transition fired correctly.\n          if (eventListeners) {\n            eventListeners.fallbackTimer = null;\n          }\n          clearTimeout(fallbackTimer);\n          this._finishRippleTransition(rippleRef);\n        };\n        const onTransitionCancel = () => this._destroyRipple(rippleRef);\n\n        // In some cases where there's a higher load on the browser, it can choose not to dispatch\n        // neither `transitionend` nor `transitioncancel` (see b/227356674). This timer serves as a\n        // fallback for such cases so that the ripple doesn't become stuck. We add a 100ms buffer\n        // because timers aren't precise. Note that another approach can be to transition the ripple\n        // to the `VISIBLE` state immediately above and to `FADING_IN` afterwards inside\n        // `transitionstart`. We go with the timer because it's one less event listener and\n        // it's less likely to break existing tests.\n        const fallbackTimer = setTimeout(onTransitionCancel, enterDuration + 100);\n\n        ripple.addEventListener('transitionend', onTransitionEnd);\n        // If the transition is cancelled (e.g. due to DOM removal), we destroy the ripple\n        // directly as otherwise we would keep it part of the ripple container forever.\n        // https://www.w3.org/TR/css-transitions-1/#:~:text=no%20longer%20in%20the%20document.\n        ripple.addEventListener('transitioncancel', onTransitionCancel);\n        eventListeners = {onTransitionEnd, onTransitionCancel, fallbackTimer};\n      });\n    }\n\n    // Add the ripple reference to the list of all active ripples.\n    this._activeRipples.set(rippleRef, eventListeners);\n\n    // In case there is no fade-in transition duration, we need to manually call the transition\n    // end listener because `transitionend` doesn't fire if there is no transition.\n    if (animationForciblyDisabledThroughCss || !enterDuration) {\n      this._finishRippleTransition(rippleRef);\n    }\n\n    return rippleRef;\n  }\n\n  /** Fades out a ripple reference. */\n  fadeOutRipple(rippleRef: RippleRef) {\n    // For ripples already fading out or hidden, this should be a noop.\n    if (rippleRef.state === RippleState.FADING_OUT || rippleRef.state === RippleState.HIDDEN) {\n      return;\n    }\n\n    const rippleEl = rippleRef.element;\n    const animationConfig = {...defaultRippleAnimationConfig, ...rippleRef.config.animation};\n\n    // This starts the fade-out transition and will fire the transition end listener that\n    // removes the ripple element from the DOM.\n    rippleEl.style.transitionDuration = `${animationConfig.exitDuration}ms`;\n    rippleEl.style.opacity = '0';\n    rippleRef.state = RippleState.FADING_OUT;\n\n    // In case there is no fade-out transition duration, we need to manually call the\n    // transition end listener because `transitionend` doesn't fire if there is no transition.\n    if (rippleRef._animationForciblyDisabledThroughCss || !animationConfig.exitDuration) {\n      this._finishRippleTransition(rippleRef);\n    }\n  }\n\n  /** Fades out all currently active ripples. */\n  fadeOutAll() {\n    this._getActiveRipples().forEach(ripple => ripple.fadeOut());\n  }\n\n  /** Fades out all currently active non-persistent ripples. */\n  fadeOutAllNonPersistent() {\n    this._getActiveRipples().forEach(ripple => {\n      if (!ripple.config.persistent) {\n        ripple.fadeOut();\n      }\n    });\n  }\n\n  /** Sets up the trigger event listeners */\n  setupTriggerEvents(elementOrElementRef: HTMLElement | ElementRef<HTMLElement>) {\n    const element = coerceElement(elementOrElementRef);\n\n    if (!this._platform.isBrowser || !element || element === this._triggerElement) {\n      return;\n    }\n\n    // Remove all previously registered event listeners from the trigger element.\n    this._removeTriggerEvents();\n    this._triggerElement = element;\n\n    // Use event delegation for the trigger events since they're\n    // set up during creation and are performance-sensitive.\n    pointerDownEvents.forEach(type => {\n      RippleRenderer._eventManager.addHandler(this._ngZone, type, element, this);\n    });\n  }\n\n  /**\n   * Handles all registered events.\n   * @docs-private\n   */\n  handleEvent(event: Event) {\n    if (event.type === 'mousedown') {\n      this._onMousedown(event as MouseEvent);\n    } else if (event.type === 'touchstart') {\n      this._onTouchStart(event as TouchEvent);\n    } else {\n      this._onPointerUp();\n    }\n\n    // If pointer-up events haven't been registered yet, do so now.\n    // We do this on-demand in order to reduce the total number of event listeners\n    // registered by the ripples, which speeds up the rendering time for large UIs.\n    if (!this._pointerUpEventsRegistered) {\n      // The events for hiding the ripple are bound directly on the trigger, because:\n      // 1. Some of them occur frequently (e.g. `mouseleave`) and any advantage we get from\n      // delegation will be diminished by having to look through all the data structures often.\n      // 2. They aren't as performance-sensitive, because they're bound only after the user\n      // has interacted with an element.\n      this._ngZone.runOutsideAngular(() => {\n        pointerUpEvents.forEach(type => {\n          this._triggerElement!.addEventListener(type, this, passiveCapturingEventOptions);\n        });\n      });\n\n      this._pointerUpEventsRegistered = true;\n    }\n  }\n\n  /** Method that will be called if the fade-in or fade-in transition completed. */\n  private _finishRippleTransition(rippleRef: RippleRef) {\n    if (rippleRef.state === RippleState.FADING_IN) {\n      this._startFadeOutTransition(rippleRef);\n    } else if (rippleRef.state === RippleState.FADING_OUT) {\n      this._destroyRipple(rippleRef);\n    }\n  }\n\n  /**\n   * Starts the fade-out transition of the given ripple if it's not persistent and the pointer\n   * is not held down anymore.\n   */\n  private _startFadeOutTransition(rippleRef: RippleRef) {\n    const isMostRecentTransientRipple = rippleRef === this._mostRecentTransientRipple;\n    const {persistent} = rippleRef.config;\n\n    rippleRef.state = RippleState.VISIBLE;\n\n    // When the timer runs out while the user has kept their pointer down, we want to\n    // keep only the persistent ripples and the latest transient ripple. We do this,\n    // because we don't want stacked transient ripples to appear after their enter\n    // animation has finished.\n    if (!persistent && (!isMostRecentTransientRipple || !this._isPointerDown)) {\n      rippleRef.fadeOut();\n    }\n  }\n\n  /** Destroys the given ripple by removing it from the DOM and updating its state. */\n  private _destroyRipple(rippleRef: RippleRef) {\n    const eventListeners = this._activeRipples.get(rippleRef) ?? null;\n    this._activeRipples.delete(rippleRef);\n\n    // Clear out the cached bounding rect if we have no more ripples.\n    if (!this._activeRipples.size) {\n      this._containerRect = null;\n    }\n\n    // If the current ref is the most recent transient ripple, unset it\n    // avoid memory leaks.\n    if (rippleRef === this._mostRecentTransientRipple) {\n      this._mostRecentTransientRipple = null;\n    }\n\n    rippleRef.state = RippleState.HIDDEN;\n    if (eventListeners !== null) {\n      rippleRef.element.removeEventListener('transitionend', eventListeners.onTransitionEnd);\n      rippleRef.element.removeEventListener('transitioncancel', eventListeners.onTransitionCancel);\n      if (eventListeners.fallbackTimer !== null) {\n        clearTimeout(eventListeners.fallbackTimer);\n      }\n    }\n    rippleRef.element.remove();\n  }\n\n  /** Function being called whenever the trigger is being pressed using mouse. */\n  private _onMousedown(event: MouseEvent) {\n    // Screen readers will fire fake mouse events for space/enter. Skip launching a\n    // ripple in this case for consistency with the non-screen-reader experience.\n    const isFakeMousedown = isFakeMousedownFromScreenReader(event);\n    const isSyntheticEvent =\n      this._lastTouchStartEvent &&\n      Date.now() < this._lastTouchStartEvent + ignoreMouseEventsTimeout;\n\n    if (!this._target.rippleDisabled && !isFakeMousedown && !isSyntheticEvent) {\n      this._isPointerDown = true;\n      this.fadeInRipple(event.clientX, event.clientY, this._target.rippleConfig);\n    }\n  }\n\n  /** Function being called whenever the trigger is being pressed using touch. */\n  private _onTouchStart(event: TouchEvent) {\n    if (!this._target.rippleDisabled && !isFakeTouchstartFromScreenReader(event)) {\n      // Some browsers fire mouse events after a `touchstart` event. Those synthetic mouse\n      // events will launch a second ripple if we don't ignore mouse events for a specific\n      // time after a touchstart event.\n      this._lastTouchStartEvent = Date.now();\n      this._isPointerDown = true;\n\n      // Use `changedTouches` so we skip any touches where the user put\n      // their finger down, but used another finger to tap the element again.\n      const touches = event.changedTouches as TouchList | undefined;\n\n      // According to the typings the touches should always be defined, but in some cases\n      // the browser appears to not assign them in tests which leads to flakes.\n      if (touches) {\n        for (let i = 0; i < touches.length; i++) {\n          this.fadeInRipple(touches[i].clientX, touches[i].clientY, this._target.rippleConfig);\n        }\n      }\n    }\n  }\n\n  /** Function being called whenever the trigger is being released. */\n  private _onPointerUp() {\n    if (!this._isPointerDown) {\n      return;\n    }\n\n    this._isPointerDown = false;\n\n    // Fade-out all ripples that are visible and not persistent.\n    this._getActiveRipples().forEach(ripple => {\n      // By default, only ripples that are completely visible will fade out on pointer release.\n      // If the `terminateOnPointerUp` option is set, ripples that still fade in will also fade out.\n      const isVisible =\n        ripple.state === RippleState.VISIBLE ||\n        (ripple.config.terminateOnPointerUp && ripple.state === RippleState.FADING_IN);\n\n      if (!ripple.config.persistent && isVisible) {\n        ripple.fadeOut();\n      }\n    });\n  }\n\n  private _getActiveRipples(): RippleRef[] {\n    return Array.from(this._activeRipples.keys());\n  }\n\n  /** Removes previously registered event listeners from the trigger element. */\n  _removeTriggerEvents() {\n    const trigger = this._triggerElement;\n\n    if (trigger) {\n      pointerDownEvents.forEach(type =>\n        RippleRenderer._eventManager.removeHandler(type, trigger, this),\n      );\n\n      if (this._pointerUpEventsRegistered) {\n        pointerUpEvents.forEach(type =>\n          trigger.removeEventListener(type, this, passiveCapturingEventOptions),\n        );\n\n        this._pointerUpEventsRegistered = false;\n      }\n    }\n  }\n}\n\n/**\n * Returns the distance from the point (x, y) to the furthest corner of a rectangle.\n */\nfunction distanceToFurthestCorner(x: number, y: number, rect: DOMRect) {\n  const distX = Math.max(Math.abs(x - rect.left), Math.abs(x - rect.right));\n  const distY = Math.max(Math.abs(y - rect.top), Math.abs(y - rect.bottom));\n  return Math.sqrt(distX * distX + distY * distY);\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {Platform} from '@angular/cdk/platform';\nimport {\n  Directive,\n  ElementRef,\n  InjectionToken,\n  Input,\n  NgZone,\n  OnDestroy,\n  OnInit,\n  ANIMATION_MODULE_TYPE,\n  Injector,\n  inject,\n} from '@angular/core';\nimport {_CdkPrivateStyleLoader} from '@angular/cdk/private';\nimport {RippleAnimationConfig, RippleConfig, RippleRef} from './ripple-ref';\nimport {RippleRenderer, RippleTarget} from './ripple-renderer';\n\n/** Configurable options for `matRipple`. */\nexport interface RippleGlobalOptions {\n  /**\n   * Whether ripples should be disabled. Ripples can be still launched manually by using\n   * the `launch()` method. Therefore focus indicators will still show up.\n   */\n  disabled?: boolean;\n\n  /**\n   * Default configuration for the animation duration of the ripples. There are two phases with\n   * different durations for the ripples: `enter` and `leave`. The durations will be overwritten\n   * by the value of `matRippleAnimation` or if the `NoopAnimationsModule` is included.\n   */\n  animation?: RippleAnimationConfig;\n\n  /**\n   * Whether ripples should start fading out immediately after the mouse or touch is released. By\n   * default, ripples will wait for the enter animation to complete and for mouse or touch release.\n   */\n  terminateOnPointerUp?: boolean;\n\n  /**\n   * A namespace to use for ripple loader to allow multiple instances to exist on the same page.\n   */\n  namespace?: string;\n}\n\n/** Injection token that can be used to specify the global ripple options. */\nexport const MAT_RIPPLE_GLOBAL_OPTIONS = new InjectionToken<RippleGlobalOptions>(\n  'mat-ripple-global-options',\n);\n\n@Directive({\n  selector: '[mat-ripple], [matRipple]',\n  exportAs: 'matRipple',\n  host: {\n    'class': 'mat-ripple',\n    '[class.mat-ripple-unbounded]': 'unbounded',\n  },\n})\nexport class MatRipple implements OnInit, OnDestroy, RippleTarget {\n  private _elementRef = inject<ElementRef<HTMLElement>>(ElementRef);\n  private _animationMode = inject(ANIMATION_MODULE_TYPE, {optional: true});\n\n  /** Custom color for all ripples. */\n  @Input('matRippleColor') color: string;\n\n  /** Whether the ripples should be visible outside the component's bounds. */\n  @Input('matRippleUnbounded') unbounded: boolean;\n\n  /**\n   * Whether the ripple always originates from the center of the host element's bounds, rather\n   * than originating from the location of the click event.\n   */\n  @Input('matRippleCentered') centered: boolean;\n\n  /**\n   * If set, the radius in pixels of foreground ripples when fully expanded. If unset, the radius\n   * will be the distance from the center of the ripple to the furthest corner of the host element's\n   * bounding rectangle.\n   */\n  @Input('matRippleRadius') radius: number = 0;\n\n  /**\n   * Configuration for the ripple animation. Allows modifying the enter and exit animation\n   * duration of the ripples. The animation durations will be overwritten if the\n   * `NoopAnimationsModule` is being used.\n   */\n  @Input('matRippleAnimation') animation: RippleAnimationConfig;\n\n  /**\n   * Whether click events will not trigger the ripple. Ripples can be still launched manually\n   * by using the `launch()` method.\n   */\n  @Input('matRippleDisabled')\n  get disabled() {\n    return this._disabled;\n  }\n  set disabled(value: boolean) {\n    if (value) {\n      this.fadeOutAllNonPersistent();\n    }\n    this._disabled = value;\n    this._setupTriggerEventsIfEnabled();\n  }\n  private _disabled: boolean = false;\n\n  /**\n   * The element that triggers the ripple when click events are received.\n   * Defaults to the directive's host element.\n   */\n  @Input('matRippleTrigger')\n  get trigger() {\n    return this._trigger || this._elementRef.nativeElement;\n  }\n  set trigger(trigger: HTMLElement) {\n    this._trigger = trigger;\n    this._setupTriggerEventsIfEnabled();\n  }\n  private _trigger: HTMLElement;\n\n  /** Renderer for the ripple DOM manipulations. */\n  private _rippleRenderer: RippleRenderer;\n\n  /** Options that are set globally for all ripples. */\n  private _globalOptions: RippleGlobalOptions;\n\n  /** @docs-private Whether ripple directive is initialized and the input bindings are set. */\n  _isInitialized: boolean = false;\n\n  constructor(...args: unknown[]);\n\n  constructor() {\n    const ngZone = inject(NgZone);\n    const platform = inject(Platform);\n    const globalOptions = inject<RippleGlobalOptions>(MAT_RIPPLE_GLOBAL_OPTIONS, {optional: true});\n    const injector = inject(Injector);\n\n    // Note: cannot use `inject()` here, because this class\n    // gets instantiated manually in the ripple loader.\n    this._globalOptions = globalOptions || {};\n    this._rippleRenderer = new RippleRenderer(this, ngZone, this._elementRef, platform, injector);\n  }\n\n  ngOnInit() {\n    this._isInitialized = true;\n    this._setupTriggerEventsIfEnabled();\n  }\n\n  ngOnDestroy() {\n    this._rippleRenderer._removeTriggerEvents();\n  }\n\n  /** Fades out all currently showing ripple elements. */\n  fadeOutAll() {\n    this._rippleRenderer.fadeOutAll();\n  }\n\n  /** Fades out all currently showing non-persistent ripple elements. */\n  fadeOutAllNonPersistent() {\n    this._rippleRenderer.fadeOutAllNonPersistent();\n  }\n\n  /**\n   * Ripple configuration from the directive's input values.\n   * @docs-private Implemented as part of RippleTarget\n   */\n  get rippleConfig(): RippleConfig {\n    return {\n      centered: this.centered,\n      radius: this.radius,\n      color: this.color,\n      animation: {\n        ...this._globalOptions.animation,\n        ...(this._animationMode === 'NoopAnimations' ? {enterDuration: 0, exitDuration: 0} : {}),\n        ...this.animation,\n      },\n      terminateOnPointerUp: this._globalOptions.terminateOnPointerUp,\n    };\n  }\n\n  /**\n   * Whether ripples on pointer-down are disabled or not.\n   * @docs-private Implemented as part of RippleTarget\n   */\n  get rippleDisabled(): boolean {\n    return this.disabled || !!this._globalOptions.disabled;\n  }\n\n  /** Sets up the trigger event listeners if ripples are enabled. */\n  private _setupTriggerEventsIfEnabled() {\n    if (!this.disabled && this._isInitialized) {\n      this._rippleRenderer.setupTriggerEvents(this.trigger);\n    }\n  }\n\n  /**\n   * Launches a manual ripple using the specified ripple configuration.\n   * @param config Configuration for the manual ripple.\n   */\n  launch(config: RippleConfig): RippleRef;\n\n  /**\n   * Launches a manual ripple at the specified coordinates relative to the viewport.\n   * @param x Coordinate along the X axis at which to fade-in the ripple. Coordinate\n   *   should be relative to the viewport.\n   * @param y Coordinate along the Y axis at which to fade-in the ripple. Coordinate\n   *   should be relative to the viewport.\n   * @param config Optional ripple configuration for the manual ripple.\n   */\n  launch(x: number, y: number, config?: RippleConfig): RippleRef;\n\n  /** Launches a manual ripple at the specified coordinated or just by the ripple config. */\n  launch(configOrX: number | RippleConfig, y: number = 0, config?: RippleConfig): RippleRef {\n    if (typeof configOrX === 'number') {\n      return this._rippleRenderer.fadeInRipple(configOrX, y, {...this.rippleConfig, ...config});\n    } else {\n      return this._rippleRenderer.fadeInRipple(0, 0, {...this.rippleConfig, ...configOrX});\n    }\n  }\n}\n"], "names": ["passiveCapturingEventOptions"], "mappings": ";;;;;;;AAQA;IACY;AAAZ,CAAA,UAAY,WAAW,EAAA;AACrB,IAAA,WAAA,CAAA,WAAA,CAAA,WAAA,CAAA,GAAA,CAAA,CAAA,GAAA,WAAS;AACT,IAAA,WAAA,CAAA,WAAA,CAAA,SAAA,CAAA,GAAA,CAAA,CAAA,GAAA,SAAO;AACP,IAAA,WAAA,CAAA,WAAA,CAAA,YAAA,CAAA,GAAA,CAAA,CAAA,GAAA,YAAU;AACV,IAAA,WAAA,CAAA,WAAA,CAAA,QAAA,CAAA,GAAA,CAAA,CAAA,GAAA,QAAM;AACR,CAAC,EALW,WAAW,KAAX,WAAW,GAKtB,EAAA,CAAA,CAAA;AAsBD;;AAEG;MACU,SAAS,CAAA;AAKV,IAAA,SAAA;AAED,IAAA,OAAA;AAEA,IAAA,MAAA;AAEA,IAAA,oCAAA;;AATT,IAAA,KAAK,GAAgB,WAAW,CAAC,MAAM;AAEvC,IAAA,WAAA,CACU,SAAgD;;IAEjD,OAAoB;;IAEpB,MAAoB;;AAEpB,IAAA,oCAAA,GAAuC,KAAK,EAAA;QAN3C,IAAS,CAAA,SAAA,GAAT,SAAS;QAEV,IAAO,CAAA,OAAA,GAAP,OAAO;QAEP,IAAM,CAAA,MAAA,GAAN,MAAM;QAEN,IAAoC,CAAA,oCAAA,GAApC,oCAAoC;;;IAI7C,OAAO,GAAA;AACL,QAAA,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,IAAI,CAAC;;AAErC;;AC9CD;AACA,MAAMA,8BAA4B,GAAG,+BAA+B,CAAC;AACnE,IAAA,OAAO,EAAE,IAAI;AACb,IAAA,OAAO,EAAE,IAAI;AACd,CAAA,CAAC;AAEF;MACa,kBAAkB,CAAA;AACrB,IAAA,OAAO,GAAG,IAAI,GAAG,EAAsD;;AAG/E,IAAA,UAAU,CAAC,MAAc,EAAE,IAAY,EAAE,OAAoB,EAAE,OAA4B,EAAA;QACzF,MAAM,gBAAgB,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC;QAE/C,IAAI,gBAAgB,EAAE;YACpB,MAAM,kBAAkB,GAAG,gBAAgB,CAAC,GAAG,CAAC,OAAO,CAAC;YAExD,IAAI,kBAAkB,EAAE;AACtB,gBAAA,kBAAkB,CAAC,GAAG,CAAC,OAAO,CAAC;;iBAC1B;AACL,gBAAA,gBAAgB,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;;;aAE9C;YACL,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,GAAG,CAAC,CAAC,CAAC,OAAO,EAAE,IAAI,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAEhE,YAAA,MAAM,CAAC,iBAAiB,CAAC,MAAK;gBAC5B,QAAQ,CAAC,gBAAgB,CAAC,IAAI,EAAE,IAAI,CAAC,qBAAqB,EAAEA,8BAA4B,CAAC;AAC3F,aAAC,CAAC;;;;AAKN,IAAA,aAAa,CAAC,IAAY,EAAE,OAAoB,EAAE,OAA4B,EAAA;QAC5E,MAAM,gBAAgB,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC;QAE/C,IAAI,CAAC,gBAAgB,EAAE;YACrB;;QAGF,MAAM,kBAAkB,GAAG,gBAAgB,CAAC,GAAG,CAAC,OAAO,CAAC;QAExD,IAAI,CAAC,kBAAkB,EAAE;YACvB;;AAGF,QAAA,kBAAkB,CAAC,MAAM,CAAC,OAAO,CAAC;AAElC,QAAA,IAAI,kBAAkB,CAAC,IAAI,KAAK,CAAC,EAAE;AACjC,YAAA,gBAAgB,CAAC,MAAM,CAAC,OAAO,CAAC;;AAGlC,QAAA,IAAI,gBAAgB,CAAC,IAAI,KAAK,CAAC,EAAE;AAC/B,YAAA,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC;YACzB,QAAQ,CAAC,mBAAmB,CAAC,IAAI,EAAE,IAAI,CAAC,qBAAqB,EAAEA,8BAA4B,CAAC;;;;AAKxF,IAAA,qBAAqB,GAAG,CAAC,KAAY,KAAI;AAC/C,QAAA,MAAM,MAAM,GAAG,eAAe,CAAC,KAAK,CAAC;QAErC,IAAI,MAAM,EAAE;AACV,YAAA,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,OAAO,CAAC,CAAC,QAAQ,EAAE,OAAO,KAAI;gBAC1D,IAAI,OAAO,KAAK,MAAM,IAAI,OAAO,CAAC,QAAQ,CAAC,MAAc,CAAC,EAAE;AAC1D,oBAAA,QAAQ,CAAC,OAAO,CAAC,OAAO,IAAI,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;;AAE3D,aAAC,CAAC;;AAEN,KAAC;AACF;;ACvCD;;;AAGG;AACU,MAAA,4BAA4B,GAAG;AAC1C,IAAA,aAAa,EAAE,GAAG;AAClB,IAAA,YAAY,EAAE,GAAG;;AAGnB;;;AAGG;AACH,MAAM,wBAAwB,GAAG,GAAG;AAEpC;AACA,MAAM,4BAA4B,GAAG,+BAA+B,CAAC;AACnE,IAAA,OAAO,EAAE,IAAI;AACb,IAAA,OAAO,EAAE,IAAI;AACd,CAAA,CAAC;AAEF;AACA,MAAM,iBAAiB,GAAG,CAAC,WAAW,EAAE,YAAY,CAAC;AAErD;AACA,MAAM,eAAe,GAAG,CAAC,SAAS,EAAE,YAAY,EAAE,UAAU,EAAE,aAAa,CAAC;MAS/D,sBAAsB,CAAA;uGAAtB,sBAAsB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAtB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,sBAAsB,iIANvB,EAAE,EAAA,QAAA,EAAA,IAAA,EAAA,MAAA,EAAA,CAAA,6jBAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FAMD,sBAAsB,EAAA,UAAA,EAAA,CAAA;kBAPlC,SAAS;AACE,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,EAAE,EACK,eAAA,EAAA,uBAAuB,CAAC,MAAM,EAChC,aAAA,EAAA,iBAAiB,CAAC,IAAI,EAE/B,IAAA,EAAA,EAAC,yBAAyB,EAAE,EAAE,EAAC,EAAA,MAAA,EAAA,CAAA,6jBAAA,CAAA,EAAA;;AAIvC;;;;;;AAMG;MACU,cAAc,CAAA;AAoCf,IAAA,OAAA;AACA,IAAA,OAAA;AAEA,IAAA,SAAA;;AArCF,IAAA,iBAAiB;;AAGjB,IAAA,eAAe;;IAGf,cAAc,GAAG,KAAK;AAE9B;;;;;AAKG;AACK,IAAA,cAAc,GAAG,IAAI,GAAG,EAA0C;;AAGlE,IAAA,0BAA0B;;AAG1B,IAAA,oBAAoB;;IAGpB,0BAA0B,GAAG,KAAK;AAE1C;;;AAGG;AACK,IAAA,cAAc;AAEd,IAAA,OAAO,aAAa,GAAG,IAAI,kBAAkB,EAAE;IAEvD,WACU,CAAA,OAAqB,EACrB,OAAe,EACvB,mBAA0D,EAClD,SAAmB,EAC3B,QAAmB,EAAA;QAJX,IAAO,CAAA,OAAA,GAAP,OAAO;QACP,IAAO,CAAA,OAAA,GAAP,OAAO;QAEP,IAAS,CAAA,SAAA,GAAT,SAAS;;AAIjB,QAAA,IAAI,SAAS,CAAC,SAAS,EAAE;AACvB,YAAA,IAAI,CAAC,iBAAiB,GAAG,aAAa,CAAC,mBAAmB,CAAC;;QAG7D,IAAI,QAAQ,EAAE;YACZ,QAAQ,CAAC,GAAG,CAAC,sBAAsB,CAAC,CAAC,IAAI,CAAC,sBAAsB,CAAC;;;AAIrE;;;;;AAKG;AACH,IAAA,YAAY,CAAC,CAAS,EAAE,CAAS,EAAE,SAAuB,EAAE,EAAA;AAC1D,QAAA,MAAM,aAAa,IAAI,IAAI,CAAC,cAAc;YACxC,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,iBAAiB,CAAC,qBAAqB,EAAE,CAAC;QACxE,MAAM,eAAe,GAAG,EAAC,GAAG,4BAA4B,EAAE,GAAG,MAAM,CAAC,SAAS,EAAC;AAE9E,QAAA,IAAI,MAAM,CAAC,QAAQ,EAAE;YACnB,CAAC,GAAG,aAAa,CAAC,IAAI,GAAG,aAAa,CAAC,KAAK,GAAG,CAAC;YAChD,CAAC,GAAG,aAAa,CAAC,GAAG,GAAG,aAAa,CAAC,MAAM,GAAG,CAAC;;AAGlD,QAAA,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,IAAI,wBAAwB,CAAC,CAAC,EAAE,CAAC,EAAE,aAAa,CAAC;AAC7E,QAAA,MAAM,OAAO,GAAG,CAAC,GAAG,aAAa,CAAC,IAAI;AACtC,QAAA,MAAM,OAAO,GAAG,CAAC,GAAG,aAAa,CAAC,GAAG;AACrC,QAAA,MAAM,aAAa,GAAG,eAAe,CAAC,aAAa;QAEnD,MAAM,MAAM,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC;AAC5C,QAAA,MAAM,CAAC,SAAS,CAAC,GAAG,CAAC,oBAAoB,CAAC;QAE1C,MAAM,CAAC,KAAK,CAAC,IAAI,GAAG,GAAG,OAAO,GAAG,MAAM,CAAA,EAAA,CAAI;QAC3C,MAAM,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,OAAO,GAAG,MAAM,CAAA,EAAA,CAAI;QAC1C,MAAM,CAAC,KAAK,CAAC,MAAM,GAAG,GAAG,MAAM,GAAG,CAAC,CAAA,EAAA,CAAI;QACvC,MAAM,CAAC,KAAK,CAAC,KAAK,GAAG,GAAG,MAAM,GAAG,CAAC,CAAA,EAAA,CAAI;;;AAItC,QAAA,IAAI,MAAM,CAAC,KAAK,IAAI,IAAI,EAAE;YACxB,MAAM,CAAC,KAAK,CAAC,eAAe,GAAG,MAAM,CAAC,KAAK;;QAG7C,MAAM,CAAC,KAAK,CAAC,kBAAkB,GAAG,CAAG,EAAA,aAAa,IAAI;AAEtD,QAAA,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,MAAM,CAAC;;;;;QAM1C,MAAM,cAAc,GAAG,MAAM,CAAC,gBAAgB,CAAC,MAAM,CAAC;AACtD,QAAA,MAAM,sBAAsB,GAAG,cAAc,CAAC,kBAAkB;AAChE,QAAA,MAAM,sBAAsB,GAAG,cAAc,CAAC,kBAAkB;;;;;;AAOhE,QAAA,MAAM,mCAAmC,GACvC,sBAAsB,KAAK,MAAM;;;AAGjC,YAAA,sBAAsB,KAAK,IAAI;AAC/B,YAAA,sBAAsB,KAAK,QAAQ;;AAEnC,aAAC,aAAa,CAAC,KAAK,KAAK,CAAC,IAAI,aAAa,CAAC,MAAM,KAAK,CAAC,CAAC;;AAG3D,QAAA,MAAM,SAAS,GAAG,IAAI,SAAS,CAAC,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,mCAAmC,CAAC;;;;;AAM1F,QAAA,MAAM,CAAC,KAAK,CAAC,SAAS,GAAG,kBAAkB;AAE3C,QAAA,SAAS,CAAC,KAAK,GAAG,WAAW,CAAC,SAAS;AAEvC,QAAA,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE;AACtB,YAAA,IAAI,CAAC,0BAA0B,GAAG,SAAS;;QAG7C,IAAI,cAAc,GAAgC,IAAI;;;QAItD,IAAI,CAAC,mCAAmC,KAAK,aAAa,IAAI,eAAe,CAAC,YAAY,CAAC,EAAE;AAC3F,YAAA,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,MAAK;gBAClC,MAAM,eAAe,GAAG,MAAK;;oBAE3B,IAAI,cAAc,EAAE;AAClB,wBAAA,cAAc,CAAC,aAAa,GAAG,IAAI;;oBAErC,YAAY,CAAC,aAAa,CAAC;AAC3B,oBAAA,IAAI,CAAC,uBAAuB,CAAC,SAAS,CAAC;AACzC,iBAAC;gBACD,MAAM,kBAAkB,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC;;;;;;;;gBAS/D,MAAM,aAAa,GAAG,UAAU,CAAC,kBAAkB,EAAE,aAAa,GAAG,GAAG,CAAC;AAEzE,gBAAA,MAAM,CAAC,gBAAgB,CAAC,eAAe,EAAE,eAAe,CAAC;;;;AAIzD,gBAAA,MAAM,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,kBAAkB,CAAC;gBAC/D,cAAc,GAAG,EAAC,eAAe,EAAE,kBAAkB,EAAE,aAAa,EAAC;AACvE,aAAC,CAAC;;;QAIJ,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,SAAS,EAAE,cAAc,CAAC;;;AAIlD,QAAA,IAAI,mCAAmC,IAAI,CAAC,aAAa,EAAE;AACzD,YAAA,IAAI,CAAC,uBAAuB,CAAC,SAAS,CAAC;;AAGzC,QAAA,OAAO,SAAS;;;AAIlB,IAAA,aAAa,CAAC,SAAoB,EAAA;;AAEhC,QAAA,IAAI,SAAS,CAAC,KAAK,KAAK,WAAW,CAAC,UAAU,IAAI,SAAS,CAAC,KAAK,KAAK,WAAW,CAAC,MAAM,EAAE;YACxF;;AAGF,QAAA,MAAM,QAAQ,GAAG,SAAS,CAAC,OAAO;AAClC,QAAA,MAAM,eAAe,GAAG,EAAC,GAAG,4BAA4B,EAAE,GAAG,SAAS,CAAC,MAAM,CAAC,SAAS,EAAC;;;QAIxF,QAAQ,CAAC,KAAK,CAAC,kBAAkB,GAAG,GAAG,eAAe,CAAC,YAAY,CAAA,EAAA,CAAI;AACvE,QAAA,QAAQ,CAAC,KAAK,CAAC,OAAO,GAAG,GAAG;AAC5B,QAAA,SAAS,CAAC,KAAK,GAAG,WAAW,CAAC,UAAU;;;QAIxC,IAAI,SAAS,CAAC,oCAAoC,IAAI,CAAC,eAAe,CAAC,YAAY,EAAE;AACnF,YAAA,IAAI,CAAC,uBAAuB,CAAC,SAAS,CAAC;;;;IAK3C,UAAU,GAAA;AACR,QAAA,IAAI,CAAC,iBAAiB,EAAE,CAAC,OAAO,CAAC,MAAM,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;;;IAI9D,uBAAuB,GAAA;QACrB,IAAI,CAAC,iBAAiB,EAAE,CAAC,OAAO,CAAC,MAAM,IAAG;AACxC,YAAA,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,UAAU,EAAE;gBAC7B,MAAM,CAAC,OAAO,EAAE;;AAEpB,SAAC,CAAC;;;AAIJ,IAAA,kBAAkB,CAAC,mBAA0D,EAAA;AAC3E,QAAA,MAAM,OAAO,GAAG,aAAa,CAAC,mBAAmB,CAAC;AAElD,QAAA,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,IAAI,CAAC,OAAO,IAAI,OAAO,KAAK,IAAI,CAAC,eAAe,EAAE;YAC7E;;;QAIF,IAAI,CAAC,oBAAoB,EAAE;AAC3B,QAAA,IAAI,CAAC,eAAe,GAAG,OAAO;;;AAI9B,QAAA,iBAAiB,CAAC,OAAO,CAAC,IAAI,IAAG;AAC/B,YAAA,cAAc,CAAC,aAAa,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,CAAC;AAC5E,SAAC,CAAC;;AAGJ;;;AAGG;AACH,IAAA,WAAW,CAAC,KAAY,EAAA;AACtB,QAAA,IAAI,KAAK,CAAC,IAAI,KAAK,WAAW,EAAE;AAC9B,YAAA,IAAI,CAAC,YAAY,CAAC,KAAmB,CAAC;;AACjC,aAAA,IAAI,KAAK,CAAC,IAAI,KAAK,YAAY,EAAE;AACtC,YAAA,IAAI,CAAC,aAAa,CAAC,KAAmB,CAAC;;aAClC;YACL,IAAI,CAAC,YAAY,EAAE;;;;;AAMrB,QAAA,IAAI,CAAC,IAAI,CAAC,0BAA0B,EAAE;;;;;;AAMpC,YAAA,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,MAAK;AAClC,gBAAA,eAAe,CAAC,OAAO,CAAC,IAAI,IAAG;oBAC7B,IAAI,CAAC,eAAgB,CAAC,gBAAgB,CAAC,IAAI,EAAE,IAAI,EAAE,4BAA4B,CAAC;AAClF,iBAAC,CAAC;AACJ,aAAC,CAAC;AAEF,YAAA,IAAI,CAAC,0BAA0B,GAAG,IAAI;;;;AAKlC,IAAA,uBAAuB,CAAC,SAAoB,EAAA;QAClD,IAAI,SAAS,CAAC,KAAK,KAAK,WAAW,CAAC,SAAS,EAAE;AAC7C,YAAA,IAAI,CAAC,uBAAuB,CAAC,SAAS,CAAC;;aAClC,IAAI,SAAS,CAAC,KAAK,KAAK,WAAW,CAAC,UAAU,EAAE;AACrD,YAAA,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC;;;AAIlC;;;AAGG;AACK,IAAA,uBAAuB,CAAC,SAAoB,EAAA;AAClD,QAAA,MAAM,2BAA2B,GAAG,SAAS,KAAK,IAAI,CAAC,0BAA0B;AACjF,QAAA,MAAM,EAAC,UAAU,EAAC,GAAG,SAAS,CAAC,MAAM;AAErC,QAAA,SAAS,CAAC,KAAK,GAAG,WAAW,CAAC,OAAO;;;;;AAMrC,QAAA,IAAI,CAAC,UAAU,KAAK,CAAC,2BAA2B,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,EAAE;YACzE,SAAS,CAAC,OAAO,EAAE;;;;AAKf,IAAA,cAAc,CAAC,SAAoB,EAAA;AACzC,QAAA,MAAM,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,SAAS,CAAC,IAAI,IAAI;AACjE,QAAA,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,SAAS,CAAC;;AAGrC,QAAA,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE;AAC7B,YAAA,IAAI,CAAC,cAAc,GAAG,IAAI;;;;AAK5B,QAAA,IAAI,SAAS,KAAK,IAAI,CAAC,0BAA0B,EAAE;AACjD,YAAA,IAAI,CAAC,0BAA0B,GAAG,IAAI;;AAGxC,QAAA,SAAS,CAAC,KAAK,GAAG,WAAW,CAAC,MAAM;AACpC,QAAA,IAAI,cAAc,KAAK,IAAI,EAAE;YAC3B,SAAS,CAAC,OAAO,CAAC,mBAAmB,CAAC,eAAe,EAAE,cAAc,CAAC,eAAe,CAAC;YACtF,SAAS,CAAC,OAAO,CAAC,mBAAmB,CAAC,kBAAkB,EAAE,cAAc,CAAC,kBAAkB,CAAC;AAC5F,YAAA,IAAI,cAAc,CAAC,aAAa,KAAK,IAAI,EAAE;AACzC,gBAAA,YAAY,CAAC,cAAc,CAAC,aAAa,CAAC;;;AAG9C,QAAA,SAAS,CAAC,OAAO,CAAC,MAAM,EAAE;;;AAIpB,IAAA,YAAY,CAAC,KAAiB,EAAA;;;AAGpC,QAAA,MAAM,eAAe,GAAG,+BAA+B,CAAC,KAAK,CAAC;AAC9D,QAAA,MAAM,gBAAgB,GACpB,IAAI,CAAC,oBAAoB;YACzB,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,oBAAoB,GAAG,wBAAwB;AAEnE,QAAA,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,cAAc,IAAI,CAAC,eAAe,IAAI,CAAC,gBAAgB,EAAE;AACzE,YAAA,IAAI,CAAC,cAAc,GAAG,IAAI;AAC1B,YAAA,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC;;;;AAKtE,IAAA,aAAa,CAAC,KAAiB,EAAA;AACrC,QAAA,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,cAAc,IAAI,CAAC,gCAAgC,CAAC,KAAK,CAAC,EAAE;;;;AAI5E,YAAA,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,GAAG,EAAE;AACtC,YAAA,IAAI,CAAC,cAAc,GAAG,IAAI;;;AAI1B,YAAA,MAAM,OAAO,GAAG,KAAK,CAAC,cAAuC;;;YAI7D,IAAI,OAAO,EAAE;AACX,gBAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;oBACvC,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC;;;;;;IAOpF,YAAY,GAAA;AAClB,QAAA,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YACxB;;AAGF,QAAA,IAAI,CAAC,cAAc,GAAG,KAAK;;QAG3B,IAAI,CAAC,iBAAiB,EAAE,CAAC,OAAO,CAAC,MAAM,IAAG;;;YAGxC,MAAM,SAAS,GACb,MAAM,CAAC,KAAK,KAAK,WAAW,CAAC,OAAO;AACpC,iBAAC,MAAM,CAAC,MAAM,CAAC,oBAAoB,IAAI,MAAM,CAAC,KAAK,KAAK,WAAW,CAAC,SAAS,CAAC;YAEhF,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,UAAU,IAAI,SAAS,EAAE;gBAC1C,MAAM,CAAC,OAAO,EAAE;;AAEpB,SAAC,CAAC;;IAGI,iBAAiB,GAAA;QACvB,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,CAAC;;;IAI/C,oBAAoB,GAAA;AAClB,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,eAAe;QAEpC,IAAI,OAAO,EAAE;YACX,iBAAiB,CAAC,OAAO,CAAC,IAAI,IAC5B,cAAc,CAAC,aAAa,CAAC,aAAa,CAAC,IAAI,EAAE,OAAO,EAAE,IAAI,CAAC,CAChE;AAED,YAAA,IAAI,IAAI,CAAC,0BAA0B,EAAE;AACnC,gBAAA,eAAe,CAAC,OAAO,CAAC,IAAI,IAC1B,OAAO,CAAC,mBAAmB,CAAC,IAAI,EAAE,IAAI,EAAE,4BAA4B,CAAC,CACtE;AAED,gBAAA,IAAI,CAAC,0BAA0B,GAAG,KAAK;;;;;AAM/C;;AAEG;AACH,SAAS,wBAAwB,CAAC,CAAS,EAAE,CAAS,EAAE,IAAa,EAAA;AACnE,IAAA,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC;AACzE,IAAA,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC;AACzE,IAAA,OAAO,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,CAAC;AACjD;;ACxbA;MACa,yBAAyB,GAAG,IAAI,cAAc,CACzD,2BAA2B;MAWhB,SAAS,CAAA;AACZ,IAAA,WAAW,GAAG,MAAM,CAA0B,UAAU,CAAC;IACzD,cAAc,GAAG,MAAM,CAAC,qBAAqB,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;;AAG/C,IAAA,KAAK;;AAGD,IAAA,SAAS;AAEtC;;;AAGG;AACyB,IAAA,QAAQ;AAEpC;;;;AAIG;IACuB,MAAM,GAAW,CAAC;AAE5C;;;;AAIG;AAC0B,IAAA,SAAS;AAEtC;;;AAGG;AACH,IAAA,IACI,QAAQ,GAAA;QACV,OAAO,IAAI,CAAC,SAAS;;IAEvB,IAAI,QAAQ,CAAC,KAAc,EAAA;QACzB,IAAI,KAAK,EAAE;YACT,IAAI,CAAC,uBAAuB,EAAE;;AAEhC,QAAA,IAAI,CAAC,SAAS,GAAG,KAAK;QACtB,IAAI,CAAC,4BAA4B,EAAE;;IAE7B,SAAS,GAAY,KAAK;AAElC;;;AAGG;AACH,IAAA,IACI,OAAO,GAAA;QACT,OAAO,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,WAAW,CAAC,aAAa;;IAExD,IAAI,OAAO,CAAC,OAAoB,EAAA;AAC9B,QAAA,IAAI,CAAC,QAAQ,GAAG,OAAO;QACvB,IAAI,CAAC,4BAA4B,EAAE;;AAE7B,IAAA,QAAQ;;AAGR,IAAA,eAAe;;AAGf,IAAA,cAAc;;IAGtB,cAAc,GAAY,KAAK;AAI/B,IAAA,WAAA,GAAA;AACE,QAAA,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;AAC7B,QAAA,MAAM,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC;AACjC,QAAA,MAAM,aAAa,GAAG,MAAM,CAAsB,yBAAyB,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;AAC9F,QAAA,MAAM,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC;;;AAIjC,QAAA,IAAI,CAAC,cAAc,GAAG,aAAa,IAAI,EAAE;AACzC,QAAA,IAAI,CAAC,eAAe,GAAG,IAAI,cAAc,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,WAAW,EAAE,QAAQ,EAAE,QAAQ,CAAC;;IAG/F,QAAQ,GAAA;AACN,QAAA,IAAI,CAAC,cAAc,GAAG,IAAI;QAC1B,IAAI,CAAC,4BAA4B,EAAE;;IAGrC,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,eAAe,CAAC,oBAAoB,EAAE;;;IAI7C,UAAU,GAAA;AACR,QAAA,IAAI,CAAC,eAAe,CAAC,UAAU,EAAE;;;IAInC,uBAAuB,GAAA;AACrB,QAAA,IAAI,CAAC,eAAe,CAAC,uBAAuB,EAAE;;AAGhD;;;AAGG;AACH,IAAA,IAAI,YAAY,GAAA;QACd,OAAO;YACL,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,KAAK,EAAE,IAAI,CAAC,KAAK;AACjB,YAAA,SAAS,EAAE;AACT,gBAAA,GAAG,IAAI,CAAC,cAAc,CAAC,SAAS;gBAChC,IAAI,IAAI,CAAC,cAAc,KAAK,gBAAgB,GAAG,EAAC,aAAa,EAAE,CAAC,EAAE,YAAY,EAAE,CAAC,EAAC,GAAG,EAAE,CAAC;gBACxF,GAAG,IAAI,CAAC,SAAS;AAClB,aAAA;AACD,YAAA,oBAAoB,EAAE,IAAI,CAAC,cAAc,CAAC,oBAAoB;SAC/D;;AAGH;;;AAGG;AACH,IAAA,IAAI,cAAc,GAAA;QAChB,OAAO,IAAI,CAAC,QAAQ,IAAI,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,QAAQ;;;IAIhD,4BAA4B,GAAA;QAClC,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,cAAc,EAAE;YACzC,IAAI,CAAC,eAAe,CAAC,kBAAkB,CAAC,IAAI,CAAC,OAAO,CAAC;;;;AAqBzD,IAAA,MAAM,CAAC,SAAgC,EAAE,CAAY,GAAA,CAAC,EAAE,MAAqB,EAAA;AAC3E,QAAA,IAAI,OAAO,SAAS,KAAK,QAAQ,EAAE;YACjC,OAAO,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,SAAS,EAAE,CAAC,EAAE,EAAC,GAAG,IAAI,CAAC,YAAY,EAAE,GAAG,MAAM,EAAC,CAAC;;aACpF;YACL,OAAO,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,EAAC,GAAG,IAAI,CAAC,YAAY,EAAE,GAAG,SAAS,EAAC,CAAC;;;uGA7J7E,SAAS,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAT,SAAS,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,2BAAA,EAAA,MAAA,EAAA,EAAA,KAAA,EAAA,CAAA,gBAAA,EAAA,OAAA,CAAA,EAAA,SAAA,EAAA,CAAA,oBAAA,EAAA,WAAA,CAAA,EAAA,QAAA,EAAA,CAAA,mBAAA,EAAA,UAAA,CAAA,EAAA,MAAA,EAAA,CAAA,iBAAA,EAAA,QAAA,CAAA,EAAA,SAAA,EAAA,CAAA,oBAAA,EAAA,WAAA,CAAA,EAAA,QAAA,EAAA,CAAA,mBAAA,EAAA,UAAA,CAAA,EAAA,OAAA,EAAA,CAAA,kBAAA,EAAA,SAAA,CAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,4BAAA,EAAA,WAAA,EAAA,EAAA,cAAA,EAAA,YAAA,EAAA,EAAA,QAAA,EAAA,CAAA,WAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAAT,SAAS,EAAA,UAAA,EAAA,CAAA;kBARrB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,2BAA2B;AACrC,oBAAA,QAAQ,EAAE,WAAW;AACrB,oBAAA,IAAI,EAAE;AACJ,wBAAA,OAAO,EAAE,YAAY;AACrB,wBAAA,8BAA8B,EAAE,WAAW;AAC5C,qBAAA;AACF,iBAAA;wDAM0B,KAAK,EAAA,CAAA;sBAA7B,KAAK;uBAAC,gBAAgB;gBAGM,SAAS,EAAA,CAAA;sBAArC,KAAK;uBAAC,oBAAoB;gBAMC,QAAQ,EAAA,CAAA;sBAAnC,KAAK;uBAAC,mBAAmB;gBAOA,MAAM,EAAA,CAAA;sBAA/B,KAAK;uBAAC,iBAAiB;gBAOK,SAAS,EAAA,CAAA;sBAArC,KAAK;uBAAC,oBAAoB;gBAOvB,QAAQ,EAAA,CAAA;sBADX,KAAK;uBAAC,mBAAmB;gBAkBtB,OAAO,EAAA,CAAA;sBADV,KAAK;uBAAC,kBAAkB;;;;;"}