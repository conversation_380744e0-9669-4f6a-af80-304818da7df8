{"version": 3, "file": "keycodes-CpHkExLC.mjs", "sources": ["../../../../../k8-fastbuild-ST-46c76129e412/bin/src/cdk/keycodes/keycodes.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nexport const MAC_ENTER = 3;\nexport const BACKSPACE = 8;\nexport const TAB = 9;\nexport const NUM_CENTER = 12;\nexport const ENTER = 13;\nexport const SHIFT = 16;\nexport const CONTROL = 17;\nexport const ALT = 18;\nexport const PAUSE = 19;\nexport const CAPS_LOCK = 20;\nexport const ESCAPE = 27;\nexport const SPACE = 32;\nexport const PAGE_UP = 33;\nexport const PAGE_DOWN = 34;\nexport const END = 35;\nexport const HOME = 36;\nexport const LEFT_ARROW = 37;\nexport const UP_ARROW = 38;\nexport const RIGHT_ARROW = 39;\nexport const DOWN_ARROW = 40;\nexport const PLUS_SIGN = 43;\nexport const PRINT_SCREEN = 44;\nexport const INSERT = 45;\nexport const DELETE = 46;\nexport const ZERO = 48;\nexport const ONE = 49;\nexport const TWO = 50;\nexport const THREE = 51;\nexport const FOUR = 52;\nexport const FIVE = 53;\nexport const SIX = 54;\nexport const SEVEN = 55;\nexport const EIGHT = 56;\nexport const NINE = 57;\nexport const FF_SEMICOLON = 59; // Firefox (Gecko) fires this for semicolon instead of 186\nexport const FF_EQUALS = 61; // Firefox (Gecko) fires this for equals instead of 187\nexport const QUESTION_MARK = 63;\nexport const AT_SIGN = 64;\nexport const A = 65;\nexport const B = 66;\nexport const C = 67;\nexport const D = 68;\nexport const E = 69;\nexport const F = 70;\nexport const G = 71;\nexport const H = 72;\nexport const I = 73;\nexport const J = 74;\nexport const K = 75;\nexport const L = 76;\nexport const M = 77;\nexport const N = 78;\nexport const O = 79;\nexport const P = 80;\nexport const Q = 81;\nexport const R = 82;\nexport const S = 83;\nexport const T = 84;\nexport const U = 85;\nexport const V = 86;\nexport const W = 87;\nexport const X = 88;\nexport const Y = 89;\nexport const Z = 90;\nexport const META = 91; // WIN_KEY_LEFT\nexport const MAC_WK_CMD_LEFT = 91;\nexport const MAC_WK_CMD_RIGHT = 93;\nexport const CONTEXT_MENU = 93;\nexport const NUMPAD_ZERO = 96;\nexport const NUMPAD_ONE = 97;\nexport const NUMPAD_TWO = 98;\nexport const NUMPAD_THREE = 99;\nexport const NUMPAD_FOUR = 100;\nexport const NUMPAD_FIVE = 101;\nexport const NUMPAD_SIX = 102;\nexport const NUMPAD_SEVEN = 103;\nexport const NUMPAD_EIGHT = 104;\nexport const NUMPAD_NINE = 105;\nexport const NUMPAD_MULTIPLY = 106;\nexport const NUMPAD_PLUS = 107;\nexport const NUMPAD_MINUS = 109;\nexport const NUMPAD_PERIOD = 110;\nexport const NUMPAD_DIVIDE = 111;\nexport const F1 = 112;\nexport const F2 = 113;\nexport const F3 = 114;\nexport const F4 = 115;\nexport const F5 = 116;\nexport const F6 = 117;\nexport const F7 = 118;\nexport const F8 = 119;\nexport const F9 = 120;\nexport const F10 = 121;\nexport const F11 = 122;\nexport const F12 = 123;\nexport const NUM_LOCK = 144;\nexport const SCROLL_LOCK = 145;\nexport const FIRST_MEDIA = 166;\nexport const FF_MINUS = 173;\nexport const MUTE = 173; // Firefox (Gecko) fires 181 for MUTE\nexport const VOLUME_DOWN = 174; // Firefox (Gecko) fires 182 for VOLUME_DOWN\nexport const VOLUME_UP = 175; // Firefox (Gecko) fires 183 for VOLUME_UP\nexport const FF_MUTE = 181;\nexport const FF_VOLUME_DOWN = 182;\nexport const LAST_MEDIA = 183;\nexport const FF_VOLUME_UP = 183;\nexport const SEMICOLON = 186; // Firefox (Gecko) fires 59 for SEMICOLON\nexport const EQUALS = 187; // Firefox (Gecko) fires 61 for EQUALS\nexport const COMMA = 188;\nexport const DASH = 189; // Firefox (Gecko) fires 173 for DASH/MINUS\nexport const PERIOD = 190;\nexport const SLASH = 191;\nexport const APOSTROPHE = 192;\nexport const TILDE = 192;\nexport const OPEN_SQUARE_BRACKET = 219;\nexport const BACKSLASH = 220;\nexport const CLOSE_SQUARE_BRACKET = 221;\nexport const SINGLE_QUOTE = 222;\nexport const MAC_META = 224;\n"], "names": [], "mappings": "AAQO,MAAM,SAAS,GAAG;AAClB,MAAM,SAAS,GAAG;AAClB,MAAM,GAAG,GAAG;AACZ,MAAM,UAAU,GAAG;AACnB,MAAM,KAAK,GAAG;AACd,MAAM,KAAK,GAAG;AACd,MAAM,OAAO,GAAG;AAChB,MAAM,GAAG,GAAG;AACZ,MAAM,KAAK,GAAG;AACd,MAAM,SAAS,GAAG;AAClB,MAAM,MAAM,GAAG;AACf,MAAM,KAAK,GAAG;AACd,MAAM,OAAO,GAAG;AAChB,MAAM,SAAS,GAAG;AAClB,MAAM,GAAG,GAAG;AACZ,MAAM,IAAI,GAAG;AACb,MAAM,UAAU,GAAG;AACnB,MAAM,QAAQ,GAAG;AACjB,MAAM,WAAW,GAAG;AACpB,MAAM,UAAU,GAAG;AACnB,MAAM,SAAS,GAAG;AAClB,MAAM,YAAY,GAAG;AACrB,MAAM,MAAM,GAAG;AACf,MAAM,MAAM,GAAG;AACf,MAAM,IAAI,GAAG;AACb,MAAM,GAAG,GAAG;AACZ,MAAM,GAAG,GAAG;AACZ,MAAM,KAAK,GAAG;AACd,MAAM,IAAI,GAAG;AACb,MAAM,IAAI,GAAG;AACb,MAAM,GAAG,GAAG;AACZ,MAAM,KAAK,GAAG;AACd,MAAM,KAAK,GAAG;AACd,MAAM,IAAI,GAAG;AACP,MAAA,YAAY,GAAG,GAAG;AAClB,MAAA,SAAS,GAAG,GAAG;AACrB,MAAM,aAAa,GAAG;AACtB,MAAM,OAAO,GAAG;AAChB,MAAM,CAAC,GAAG;AACV,MAAM,CAAC,GAAG;AACV,MAAM,CAAC,GAAG;AACV,MAAM,CAAC,GAAG;AACV,MAAM,CAAC,GAAG;AACV,MAAM,CAAC,GAAG;AACV,MAAM,CAAC,GAAG;AACV,MAAM,CAAC,GAAG;AACV,MAAM,CAAC,GAAG;AACV,MAAM,CAAC,GAAG;AACV,MAAM,CAAC,GAAG;AACV,MAAM,CAAC,GAAG;AACV,MAAM,CAAC,GAAG;AACV,MAAM,CAAC,GAAG;AACV,MAAM,CAAC,GAAG;AACV,MAAM,CAAC,GAAG;AACV,MAAM,CAAC,GAAG;AACV,MAAM,CAAC,GAAG;AACV,MAAM,CAAC,GAAG;AACV,MAAM,CAAC,GAAG;AACV,MAAM,CAAC,GAAG;AACV,MAAM,CAAC,GAAG;AACV,MAAM,CAAC,GAAG;AACV,MAAM,CAAC,GAAG;AACV,MAAM,CAAC,GAAG;AACV,MAAM,CAAC,GAAG;AACJ,MAAA,IAAI,GAAG,GAAG;AAChB,MAAM,eAAe,GAAG;AACxB,MAAM,gBAAgB,GAAG;AACzB,MAAM,YAAY,GAAG;AACrB,MAAM,WAAW,GAAG;AACpB,MAAM,UAAU,GAAG;AACnB,MAAM,UAAU,GAAG;AACnB,MAAM,YAAY,GAAG;AACrB,MAAM,WAAW,GAAG;AACpB,MAAM,WAAW,GAAG;AACpB,MAAM,UAAU,GAAG;AACnB,MAAM,YAAY,GAAG;AACrB,MAAM,YAAY,GAAG;AACrB,MAAM,WAAW,GAAG;AACpB,MAAM,eAAe,GAAG;AACxB,MAAM,WAAW,GAAG;AACpB,MAAM,YAAY,GAAG;AACrB,MAAM,aAAa,GAAG;AACtB,MAAM,aAAa,GAAG;AACtB,MAAM,EAAE,GAAG;AACX,MAAM,EAAE,GAAG;AACX,MAAM,EAAE,GAAG;AACX,MAAM,EAAE,GAAG;AACX,MAAM,EAAE,GAAG;AACX,MAAM,EAAE,GAAG;AACX,MAAM,EAAE,GAAG;AACX,MAAM,EAAE,GAAG;AACX,MAAM,EAAE,GAAG;AACX,MAAM,GAAG,GAAG;AACZ,MAAM,GAAG,GAAG;AACZ,MAAM,GAAG,GAAG;AACZ,MAAM,QAAQ,GAAG;AACjB,MAAM,WAAW,GAAG;AACpB,MAAM,WAAW,GAAG;AACpB,MAAM,QAAQ,GAAG;AACX,MAAA,IAAI,GAAG,IAAI;AACX,MAAA,WAAW,GAAG,IAAI;AAClB,MAAA,SAAS,GAAG,IAAI;AACtB,MAAM,OAAO,GAAG;AAChB,MAAM,cAAc,GAAG;AACvB,MAAM,UAAU,GAAG;AACnB,MAAM,YAAY,GAAG;AACf,MAAA,SAAS,GAAG,IAAI;AAChB,MAAA,MAAM,GAAG,IAAI;AACnB,MAAM,KAAK,GAAG;AACR,MAAA,IAAI,GAAG,IAAI;AACjB,MAAM,MAAM,GAAG;AACf,MAAM,KAAK,GAAG;AACd,MAAM,UAAU,GAAG;AACnB,MAAM,KAAK,GAAG;AACd,MAAM,mBAAmB,GAAG;AAC5B,MAAM,SAAS,GAAG;AAClB,MAAM,oBAAoB,GAAG;AAC7B,MAAM,YAAY,GAAG;AACrB,MAAM,QAAQ,GAAG;;;;"}