//
// Design system display name: Material 3
// Design system version: v0.161
//

@use 'sass:map';

@use './md-ref-palette';

// Indicates whether alternative tokens should be used
$_alternate-tokens: false;

$_default-dark: (
  'md-ref-palette': md-ref-palette.values(),
);

@function values-dark($deps: $_default-dark) {
  $values: (
    'background': map.get($deps, 'md-ref-palette', 'neutral6'),
    'error': map.get($deps, 'md-ref-palette', 'error80'),
    'error-container': map.get($deps, 'md-ref-palette', 'error30'),
    'inverse-on-surface': map.get($deps, 'md-ref-palette', 'neutral20'),
    'inverse-primary': map.get($deps, 'md-ref-palette', 'primary40'),
    'inverse-surface': map.get($deps, 'md-ref-palette', 'neutral90'),
    'on-background': map.get($deps, 'md-ref-palette', 'neutral90'),
    'on-error': map.get($deps, 'md-ref-palette', 'error20'),
    'on-error-container': map.get($deps, 'md-ref-palette', 'error90'),
    'on-primary': map.get($deps, 'md-ref-palette', 'primary20'),
    'on-primary-container': map.get($deps, 'md-ref-palette', 'primary90'),
    'on-primary-fixed': map.get($deps, 'md-ref-palette', 'primary10'),
    'on-primary-fixed-variant': map.get($deps, 'md-ref-palette', 'primary30'),
    'on-secondary': map.get($deps, 'md-ref-palette', 'secondary20'),
    'on-secondary-container': map.get($deps, 'md-ref-palette', 'secondary90'),
    'on-secondary-fixed': map.get($deps, 'md-ref-palette', 'secondary10'),
    'on-secondary-fixed-variant':
      map.get($deps, 'md-ref-palette', 'secondary30'),
    'on-surface': map.get($deps, 'md-ref-palette', 'neutral90'),
    'on-surface-variant': map.get($deps, 'md-ref-palette', 'neutral-variant90'),
    'on-tertiary': map.get($deps, 'md-ref-palette', 'tertiary20'),
    'on-tertiary-container': map.get($deps, 'md-ref-palette', 'tertiary90'),
    'on-tertiary-fixed': map.get($deps, 'md-ref-palette', 'tertiary10'),
    'on-tertiary-fixed-variant': map.get($deps, 'md-ref-palette', 'tertiary30'),
    'outline': map.get($deps, 'md-ref-palette', 'neutral-variant60'),
    'outline-variant': map.get($deps, 'md-ref-palette', 'neutral-variant30'),
    'primary': map.get($deps, 'md-ref-palette', 'primary80'),
    'primary-container': map.get($deps, 'md-ref-palette', 'primary30'),
    'primary-fixed': map.get($deps, 'md-ref-palette', 'primary90'),
    'primary-fixed-dim': map.get($deps, 'md-ref-palette', 'primary80'),
    'scrim': map.get($deps, 'md-ref-palette', 'neutral0'),
    'secondary': map.get($deps, 'md-ref-palette', 'secondary80'),
    'secondary-container': map.get($deps, 'md-ref-palette', 'secondary30'),
    'secondary-fixed': map.get($deps, 'md-ref-palette', 'secondary90'),
    'secondary-fixed-dim': map.get($deps, 'md-ref-palette', 'secondary80'),
    'shadow': map.get($deps, 'md-ref-palette', 'neutral0'),
    'surface': map.get($deps, 'md-ref-palette', 'neutral6'),
    'surface-bright': map.get($deps, 'md-ref-palette', 'neutral24'),
    'surface-container': map.get($deps, 'md-ref-palette', 'neutral12'),
    'surface-container-high': map.get($deps, 'md-ref-palette', 'neutral17'),
    'surface-container-highest': map.get($deps, 'md-ref-palette', 'neutral22'),
    'surface-container-low': map.get($deps, 'md-ref-palette', 'neutral10'),
    'surface-container-lowest': map.get($deps, 'md-ref-palette', 'neutral4'),
    'surface-dim': map.get($deps, 'md-ref-palette', 'neutral6'),
    'surface-tint': map.get($deps, 'md-ref-palette', 'primary80'),
    'surface-variant': map.get($deps, 'md-ref-palette', 'neutral-variant30'),
    'tertiary': map.get($deps, 'md-ref-palette', 'tertiary80'),
    'tertiary-container': map.get($deps, 'md-ref-palette', 'tertiary30'),
    'tertiary-fixed': map.get($deps, 'md-ref-palette', 'tertiary90'),
    'tertiary-fixed-dim': map.get($deps, 'md-ref-palette', 'tertiary80')
  );

  @if ($_alternate-tokens) {
    $values: map.merge($values, (
      'background': map.get($deps, 'md-ref-palette', 'neutral10'),
      'on-surface-variant': map.get($deps, 'md-ref-palette', 'neutral-variant80'),
      'surface': map.get($deps, 'md-ref-palette', 'neutral10'),
      'surface-bright': #37393b,
      'surface-container': #1e1f20,
      'surface-container-high': #282a2c,
      'surface-container-highest': #333537,
      'surface-container-low': #1b1b1b,
      'surface-container-lowest': #0e0e0e,
      'surface-dim': #131313,
      'surface-tint': #d1e1ff,
    ));
  }

  @return $values;
}

$_default-light: (
  'md-ref-palette': md-ref-palette.values(),
);

@function values-light($deps: $_default-light) {
  $values: (
    'background': map.get($deps, 'md-ref-palette', 'neutral98'),
    'error': map.get($deps, 'md-ref-palette', 'error40'),
    'error-container': map.get($deps, 'md-ref-palette', 'error90'),
    'inverse-on-surface': map.get($deps, 'md-ref-palette', 'neutral95'),
    'inverse-primary': map.get($deps, 'md-ref-palette', 'primary80'),
    'inverse-surface': map.get($deps, 'md-ref-palette', 'neutral20'),
    'on-background': map.get($deps, 'md-ref-palette', 'neutral10'),
    'on-error': map.get($deps, 'md-ref-palette', 'error100'),
    'on-error-container': map.get($deps, 'md-ref-palette', 'error30'),
    'on-primary': map.get($deps, 'md-ref-palette', 'primary100'),
    'on-primary-container': map.get($deps, 'md-ref-palette', 'primary30'),
    'on-primary-fixed': map.get($deps, 'md-ref-palette', 'primary10'),
    'on-primary-fixed-variant': map.get($deps, 'md-ref-palette', 'primary30'),
    'on-secondary': map.get($deps, 'md-ref-palette', 'secondary100'),
    'on-secondary-container': map.get($deps, 'md-ref-palette', 'secondary30'),
    'on-secondary-fixed': map.get($deps, 'md-ref-palette', 'secondary10'),
    'on-secondary-fixed-variant':
      map.get($deps, 'md-ref-palette', 'secondary30'),
    'on-surface': map.get($deps, 'md-ref-palette', 'neutral10'),
    'on-surface-variant': map.get($deps, 'md-ref-palette', 'neutral-variant30'),
    'on-tertiary': map.get($deps, 'md-ref-palette', 'tertiary100'),
    'on-tertiary-container': map.get($deps, 'md-ref-palette', 'tertiary30'),
    'on-tertiary-fixed': map.get($deps, 'md-ref-palette', 'tertiary10'),
    'on-tertiary-fixed-variant': map.get($deps, 'md-ref-palette', 'tertiary30'),
    'outline': map.get($deps, 'md-ref-palette', 'neutral-variant50'),
    'outline-variant': map.get($deps, 'md-ref-palette', 'neutral-variant80'),
    'primary': map.get($deps, 'md-ref-palette', 'primary40'),
    'primary-container': map.get($deps, 'md-ref-palette', 'primary90'),
    'primary-fixed': map.get($deps, 'md-ref-palette', 'primary90'),
    'primary-fixed-dim': map.get($deps, 'md-ref-palette', 'primary80'),
    'scrim': map.get($deps, 'md-ref-palette', 'neutral0'),
    'secondary': map.get($deps, 'md-ref-palette', 'secondary40'),
    'secondary-container': map.get($deps, 'md-ref-palette', 'secondary90'),
    'secondary-fixed': map.get($deps, 'md-ref-palette', 'secondary90'),
    'secondary-fixed-dim': map.get($deps, 'md-ref-palette', 'secondary80'),
    'shadow': map.get($deps, 'md-ref-palette', 'neutral0'),
    'surface': map.get($deps, 'md-ref-palette', 'neutral98'),
    'surface-bright': map.get($deps, 'md-ref-palette', 'neutral98'),
    'surface-container': map.get($deps, 'md-ref-palette', 'neutral94'),
    'surface-container-high': map.get($deps, 'md-ref-palette', 'neutral92'),
    'surface-container-highest': map.get($deps, 'md-ref-palette', 'neutral90'),
    'surface-container-low': map.get($deps, 'md-ref-palette', 'neutral96'),
    'surface-container-lowest': map.get($deps, 'md-ref-palette', 'neutral100'),
    'surface-dim': map.get($deps, 'md-ref-palette', 'neutral87'),
    'surface-tint': map.get($deps, 'md-ref-palette', 'primary40'),
    'surface-variant': map.get($deps, 'md-ref-palette', 'neutral-variant90'),
    'tertiary': map.get($deps, 'md-ref-palette', 'tertiary40'),
    'tertiary-container': map.get($deps, 'md-ref-palette', 'tertiary90'),
    'tertiary-fixed': map.get($deps, 'md-ref-palette', 'tertiary90'),
    'tertiary-fixed-dim': map.get($deps, 'md-ref-palette', 'tertiary80')
  );

  @if ($_alternate-tokens) {
    $values: map.merge($values, (
      'background': map.get($deps, 'md-ref-palette', 'neutral100'),
      'on-error-container': map.get($deps, 'md-ref-palette', 'error10'),
      'on-primary-container': map.get($deps, 'md-ref-palette', 'primary10'),
      'on-secondary-container': map.get($deps, 'md-ref-palette', 'secondary10'),
      'on-tertiary-container': map.get($deps, 'md-ref-palette', 'tertiary10'),
      'surface': map.get($deps, 'md-ref-palette', 'neutral100'),
      'surface-bright': map.get($deps, 'md-ref-palette', 'neutral100'),
      'surface-container': #f0f4f9,
      'surface-container-high': #e9eef6,
      'surface-container-highest': #dde3ea,
      'surface-container-low': #f8fafd,
      'surface-container-lowest': map.get($deps, 'md-ref-palette', 'primary100'),
      'surface-dim': #d3dbe5,
      'surface-tint': #6991d6,
    ));
  }

  @return $values;
}
