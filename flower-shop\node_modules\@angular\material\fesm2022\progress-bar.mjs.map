{"version": 3, "file": "progress-bar.mjs", "sources": ["../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/progress-bar/progress-bar.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/progress-bar/progress-bar.html", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/progress-bar/module.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  ChangeDetectionStrategy,\n  Component,\n  ViewEncapsulation,\n  ElementRef,\n  NgZone,\n  Input,\n  Output,\n  EventEmitter,\n  AfterViewInit,\n  OnDestroy,\n  ChangeDetectorRef,\n  InjectionToken,\n  inject,\n  numberAttribute,\n  ANIMATION_MODULE_TYPE,\n  Renderer2,\n} from '@angular/core';\nimport {DOCUMENT} from '@angular/common';\nimport {ThemePalette} from '../core';\n\n/** Last animation end data. */\nexport interface ProgressAnimationEnd {\n  value: number;\n}\n\n/** Default `mat-progress-bar` options that can be overridden. */\nexport interface MatProgressBarDefaultOptions {\n  /**\n   * Default theme color of the progress bar. This API is supported in M2 themes only,\n   * it has no effect in M3 themes. For color customization in M3, see https://material.angular.dev/components/progress-bar/styling.\n   *\n   * For information on applying color variants in M3, see\n   * https://material.angular.dev/guide/material-2-theming#optional-add-backwards-compatibility-styles-for-color-variants\n   */\n  color?: ThemePalette;\n\n  /** Default mode of the progress bar. */\n  mode?: ProgressBarMode;\n}\n\n/** Injection token to be used to override the default options for `mat-progress-bar`. */\nexport const MAT_PROGRESS_BAR_DEFAULT_OPTIONS = new InjectionToken<MatProgressBarDefaultOptions>(\n  'MAT_PROGRESS_BAR_DEFAULT_OPTIONS',\n);\n\n/**\n * Injection token used to provide the current location to `MatProgressBar`.\n * Used to handle server-side rendering and to stub out during unit tests.\n * @docs-private\n */\nexport const MAT_PROGRESS_BAR_LOCATION = new InjectionToken<MatProgressBarLocation>(\n  'mat-progress-bar-location',\n  {providedIn: 'root', factory: MAT_PROGRESS_BAR_LOCATION_FACTORY},\n);\n\n/**\n * Stubbed out location for `MatProgressBar`.\n * @docs-private\n */\nexport interface MatProgressBarLocation {\n  getPathname: () => string;\n}\n\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nexport function MAT_PROGRESS_BAR_LOCATION_FACTORY(): MatProgressBarLocation {\n  const _document = inject(DOCUMENT);\n  const _location = _document ? _document.location : null;\n\n  return {\n    // Note that this needs to be a function, rather than a property, because Angular\n    // will only resolve it once, but we want the current path on each call.\n    getPathname: () => (_location ? _location.pathname + _location.search : ''),\n  };\n}\n\nexport type ProgressBarMode = 'determinate' | 'indeterminate' | 'buffer' | 'query';\n\n@Component({\n  selector: 'mat-progress-bar',\n  exportAs: 'matProgressBar',\n  host: {\n    'role': 'progressbar',\n    'aria-valuemin': '0',\n    'aria-valuemax': '100',\n    // set tab index to -1 so screen readers will read the aria-label\n    // Note: there is a known issue with JAWS that does not read progressbar aria labels on FireFox\n    'tabindex': '-1',\n    '[attr.aria-valuenow]': '_isIndeterminate() ? null : value',\n    '[attr.mode]': 'mode',\n    'class': 'mat-mdc-progress-bar mdc-linear-progress',\n    '[class]': '\"mat-\" + color',\n    '[class._mat-animation-noopable]': '_isNoopAnimation',\n    '[class.mdc-linear-progress--animation-ready]': '!_isNoopAnimation',\n    '[class.mdc-linear-progress--indeterminate]': '_isIndeterminate()',\n  },\n  templateUrl: 'progress-bar.html',\n  styleUrl: 'progress-bar.css',\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  encapsulation: ViewEncapsulation.None,\n})\nexport class MatProgressBar implements AfterViewInit, OnDestroy {\n  readonly _elementRef = inject<ElementRef<HTMLElement>>(ElementRef);\n  private _ngZone = inject(NgZone);\n  private _changeDetectorRef = inject(ChangeDetectorRef);\n  private _renderer = inject(Renderer2);\n  private _cleanupTransitionEnd: (() => void) | undefined;\n  _animationMode? = inject(ANIMATION_MODULE_TYPE, {optional: true});\n\n  constructor(...args: unknown[]);\n\n  constructor() {\n    const defaults = inject<MatProgressBarDefaultOptions>(MAT_PROGRESS_BAR_DEFAULT_OPTIONS, {\n      optional: true,\n    });\n\n    this._isNoopAnimation = this._animationMode === 'NoopAnimations';\n\n    if (defaults) {\n      if (defaults.color) {\n        this.color = this._defaultColor = defaults.color;\n      }\n\n      this.mode = defaults.mode || this.mode;\n    }\n  }\n\n  /** Flag that indicates whether NoopAnimations mode is set to true. */\n  _isNoopAnimation = false;\n\n  // TODO: should be typed as `ThemePalette` but internal apps pass in arbitrary strings.\n  /**\n   * Theme color of the progress bar. This API is supported in M2 themes only, it\n   * has no effect in M3 themes. For color customization in M3, see https://material.angular.dev/components/progress-bar/styling.\n   *\n   * For information on applying color variants in M3, see\n   * https://material.angular.dev/guide/material-2-theming#optional-add-backwards-compatibility-styles-for-color-variants\n   */\n  @Input()\n  get color() {\n    return this._color || this._defaultColor;\n  }\n  set color(value: string | null | undefined) {\n    this._color = value;\n  }\n  private _color: string | null | undefined;\n  private _defaultColor: ThemePalette = 'primary';\n\n  /** Value of the progress bar. Defaults to zero. Mirrored to aria-valuenow. */\n  @Input({transform: numberAttribute})\n  get value(): number {\n    return this._value;\n  }\n  set value(v: number) {\n    this._value = clamp(v || 0);\n    this._changeDetectorRef.markForCheck();\n  }\n  private _value = 0;\n\n  /** Buffer value of the progress bar. Defaults to zero. */\n  @Input({transform: numberAttribute})\n  get bufferValue(): number {\n    return this._bufferValue || 0;\n  }\n  set bufferValue(v: number) {\n    this._bufferValue = clamp(v || 0);\n    this._changeDetectorRef.markForCheck();\n  }\n  private _bufferValue = 0;\n\n  /**\n   * Event emitted when animation of the primary progress bar completes. This event will not\n   * be emitted when animations are disabled, nor will it be emitted for modes with continuous\n   * animations (indeterminate and query).\n   */\n  @Output() readonly animationEnd = new EventEmitter<ProgressAnimationEnd>();\n\n  /**\n   * Mode of the progress bar.\n   *\n   * Input must be one of these values: determinate, indeterminate, buffer, query, defaults to\n   * 'determinate'.\n   * Mirrored to mode attribute.\n   */\n  @Input()\n  get mode(): ProgressBarMode {\n    return this._mode;\n  }\n  set mode(value: ProgressBarMode) {\n    // Note that we don't technically need a getter and a setter here,\n    // but we use it to match the behavior of the existing mat-progress-bar.\n    this._mode = value;\n    this._changeDetectorRef.markForCheck();\n  }\n  private _mode: ProgressBarMode = 'determinate';\n\n  ngAfterViewInit() {\n    // Run outside angular so change detection didn't get triggered on every transition end\n    // instead only on the animation that we care about (primary value bar's transitionend)\n    this._ngZone.runOutsideAngular(() => {\n      this._cleanupTransitionEnd = this._renderer.listen(\n        this._elementRef.nativeElement,\n        'transitionend',\n        this._transitionendHandler,\n      );\n    });\n  }\n\n  ngOnDestroy() {\n    this._cleanupTransitionEnd?.();\n  }\n\n  /** Gets the transform style that should be applied to the primary bar. */\n  _getPrimaryBarTransform(): string {\n    return `scaleX(${this._isIndeterminate() ? 1 : this.value / 100})`;\n  }\n\n  /** Gets the `flex-basis` value that should be applied to the buffer bar. */\n  _getBufferBarFlexBasis(): string {\n    return `${this.mode === 'buffer' ? this.bufferValue : 100}%`;\n  }\n\n  /** Returns whether the progress bar is indeterminate. */\n  _isIndeterminate(): boolean {\n    return this.mode === 'indeterminate' || this.mode === 'query';\n  }\n\n  /** Event handler for `transitionend` events. */\n  private _transitionendHandler = (event: TransitionEvent) => {\n    if (\n      this.animationEnd.observers.length === 0 ||\n      !event.target ||\n      !(event.target as HTMLElement).classList.contains('mdc-linear-progress__primary-bar')\n    ) {\n      return;\n    }\n\n    if (this.mode === 'determinate' || this.mode === 'buffer') {\n      this._ngZone.run(() => this.animationEnd.next({value: this.value}));\n    }\n  };\n}\n\n/** Clamps a value to be between two numbers, by default 0 and 100. */\nfunction clamp(v: number, min = 0, max = 100) {\n  return Math.max(min, Math.min(max, v));\n}\n", "<!--\n  All children need to be hidden for screen readers in order to support ChromeVox.\n  More context in the issue: https://github.com/angular/components/issues/22165.\n-->\n<div class=\"mdc-linear-progress__buffer\" aria-hidden=\"true\">\n  <div\n    class=\"mdc-linear-progress__buffer-bar\"\n    [style.flex-basis]=\"_getBufferBarFlexBasis()\"></div>\n  <!-- Remove the dots outside of buffer mode since they can cause CSP issues (see #28938) -->\n  @if (mode === 'buffer') {\n    <div class=\"mdc-linear-progress__buffer-dots\"></div>\n  }\n</div>\n<div\n  class=\"mdc-linear-progress__bar mdc-linear-progress__primary-bar\"\n  aria-hidden=\"true\"\n  [style.transform]=\"_getPrimaryBarTransform()\">\n  <span class=\"mdc-linear-progress__bar-inner\"></span>\n</div>\n<div class=\"mdc-linear-progress__bar mdc-linear-progress__secondary-bar\" aria-hidden=\"true\">\n  <span class=\"mdc-linear-progress__bar-inner\"></span>\n</div>\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {NgModule} from '@angular/core';\nimport {MatCommonModule} from '../core';\nimport {MatProgressBar} from './progress-bar';\n\n@NgModule({\n  imports: [MatProgressBar],\n  exports: [MatProgressBar, MatCommonModule],\n})\nexport class MatProgressBarModule {}\n"], "names": [], "mappings": ";;;;;;;AAiDA;MACa,gCAAgC,GAAG,IAAI,cAAc,CAChE,kCAAkC;AAGpC;;;;AAIG;AACU,MAAA,yBAAyB,GAAG,IAAI,cAAc,CACzD,2BAA2B,EAC3B,EAAC,UAAU,EAAE,MAAM,EAAE,OAAO,EAAE,iCAAiC,EAAC;AAWlE;;;;AAIG;SACa,iCAAiC,GAAA;AAC/C,IAAA,MAAM,SAAS,GAAG,MAAM,CAAC,QAAQ,CAAC;AAClC,IAAA,MAAM,SAAS,GAAG,SAAS,GAAG,SAAS,CAAC,QAAQ,GAAG,IAAI;IAEvD,OAAO;;;QAGL,WAAW,EAAE,OAAO,SAAS,GAAG,SAAS,CAAC,QAAQ,GAAG,SAAS,CAAC,MAAM,GAAG,EAAE,CAAC;KAC5E;AACH;MA2Ba,cAAc,CAAA;AAChB,IAAA,WAAW,GAAG,MAAM,CAA0B,UAAU,CAAC;AAC1D,IAAA,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC;AACxB,IAAA,kBAAkB,GAAG,MAAM,CAAC,iBAAiB,CAAC;AAC9C,IAAA,SAAS,GAAG,MAAM,CAAC,SAAS,CAAC;AAC7B,IAAA,qBAAqB;IAC7B,cAAc,GAAI,MAAM,CAAC,qBAAqB,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;AAIjE,IAAA,WAAA,GAAA;AACE,QAAA,MAAM,QAAQ,GAAG,MAAM,CAA+B,gCAAgC,EAAE;AACtF,YAAA,QAAQ,EAAE,IAAI;AACf,SAAA,CAAC;QAEF,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,cAAc,KAAK,gBAAgB;QAEhE,IAAI,QAAQ,EAAE;AACZ,YAAA,IAAI,QAAQ,CAAC,KAAK,EAAE;gBAClB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,aAAa,GAAG,QAAQ,CAAC,KAAK;;YAGlD,IAAI,CAAC,IAAI,GAAG,QAAQ,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI;;;;IAK1C,gBAAgB,GAAG,KAAK;;AAGxB;;;;;;AAMG;AACH,IAAA,IACI,KAAK,GAAA;AACP,QAAA,OAAO,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,aAAa;;IAE1C,IAAI,KAAK,CAAC,KAAgC,EAAA;AACxC,QAAA,IAAI,CAAC,MAAM,GAAG,KAAK;;AAEb,IAAA,MAAM;IACN,aAAa,GAAiB,SAAS;;AAG/C,IAAA,IACI,KAAK,GAAA;QACP,OAAO,IAAI,CAAC,MAAM;;IAEpB,IAAI,KAAK,CAAC,CAAS,EAAA;QACjB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC;AAC3B,QAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE;;IAEhC,MAAM,GAAG,CAAC;;AAGlB,IAAA,IACI,WAAW,GAAA;AACb,QAAA,OAAO,IAAI,CAAC,YAAY,IAAI,CAAC;;IAE/B,IAAI,WAAW,CAAC,CAAS,EAAA;QACvB,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC;AACjC,QAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE;;IAEhC,YAAY,GAAG,CAAC;AAExB;;;;AAIG;AACgB,IAAA,YAAY,GAAG,IAAI,YAAY,EAAwB;AAE1E;;;;;;AAMG;AACH,IAAA,IACI,IAAI,GAAA;QACN,OAAO,IAAI,CAAC,KAAK;;IAEnB,IAAI,IAAI,CAAC,KAAsB,EAAA;;;AAG7B,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK;AAClB,QAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE;;IAEhC,KAAK,GAAoB,aAAa;IAE9C,eAAe,GAAA;;;AAGb,QAAA,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,MAAK;YAClC,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAChD,IAAI,CAAC,WAAW,CAAC,aAAa,EAC9B,eAAe,EACf,IAAI,CAAC,qBAAqB,CAC3B;AACH,SAAC,CAAC;;IAGJ,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,qBAAqB,IAAI;;;IAIhC,uBAAuB,GAAA;AACrB,QAAA,OAAO,UAAU,IAAI,CAAC,gBAAgB,EAAE,GAAG,CAAC,GAAG,IAAI,CAAC,KAAK,GAAG,GAAG,GAAG;;;IAIpE,sBAAsB,GAAA;AACpB,QAAA,OAAO,GAAG,IAAI,CAAC,IAAI,KAAK,QAAQ,GAAG,IAAI,CAAC,WAAW,GAAG,GAAG,GAAG;;;IAI9D,gBAAgB,GAAA;QACd,OAAO,IAAI,CAAC,IAAI,KAAK,eAAe,IAAI,IAAI,CAAC,IAAI,KAAK,OAAO;;;AAIvD,IAAA,qBAAqB,GAAG,CAAC,KAAsB,KAAI;QACzD,IACE,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC;YACxC,CAAC,KAAK,CAAC,MAAM;YACb,CAAE,KAAK,CAAC,MAAsB,CAAC,SAAS,CAAC,QAAQ,CAAC,kCAAkC,CAAC,EACrF;YACA;;AAGF,QAAA,IAAI,IAAI,CAAC,IAAI,KAAK,aAAa,IAAI,IAAI,CAAC,IAAI,KAAK,QAAQ,EAAE;YACzD,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,EAAC,KAAK,EAAE,IAAI,CAAC,KAAK,EAAC,CAAC,CAAC;;AAEvE,KAAC;uGA3IU,cAAc,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAd,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,cAAc,EAgDN,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,kBAAA,EAAA,MAAA,EAAA,EAAA,KAAA,EAAA,OAAA,EAAA,KAAA,EAAA,CAAA,OAAA,EAAA,OAAA,EAAA,eAAe,CAWf,EAAA,WAAA,EAAA,CAAA,aAAA,EAAA,aAAA,EAAA,eAAe,wlBC5KpC,66BAsBA,EAAA,MAAA,EAAA,CAAA,kzOAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FD2Fa,cAAc,EAAA,UAAA,EAAA,CAAA;kBAvB1B,SAAS;+BACE,kBAAkB,EAAA,QAAA,EAClB,gBAAgB,EACpB,IAAA,EAAA;AACJ,wBAAA,MAAM,EAAE,aAAa;AACrB,wBAAA,eAAe,EAAE,GAAG;AACpB,wBAAA,eAAe,EAAE,KAAK;;;AAGtB,wBAAA,UAAU,EAAE,IAAI;AAChB,wBAAA,sBAAsB,EAAE,mCAAmC;AAC3D,wBAAA,aAAa,EAAE,MAAM;AACrB,wBAAA,OAAO,EAAE,0CAA0C;AACnD,wBAAA,SAAS,EAAE,gBAAgB;AAC3B,wBAAA,iCAAiC,EAAE,kBAAkB;AACrD,wBAAA,8CAA8C,EAAE,mBAAmB;AACnE,wBAAA,4CAA4C,EAAE,oBAAoB;AACnE,qBAAA,EAAA,eAAA,EAGgB,uBAAuB,CAAC,MAAM,EAChC,aAAA,EAAA,iBAAiB,CAAC,IAAI,EAAA,QAAA,EAAA,66BAAA,EAAA,MAAA,EAAA,CAAA,kzOAAA,CAAA,EAAA;wDAwCjC,KAAK,EAAA,CAAA;sBADR;gBAYG,KAAK,EAAA,CAAA;sBADR,KAAK;uBAAC,EAAC,SAAS,EAAE,eAAe,EAAC;gBAY/B,WAAW,EAAA,CAAA;sBADd,KAAK;uBAAC,EAAC,SAAS,EAAE,eAAe,EAAC;gBAehB,YAAY,EAAA,CAAA;sBAA9B;gBAUG,IAAI,EAAA,CAAA;sBADP;;AA2DH;AACA,SAAS,KAAK,CAAC,CAAS,EAAE,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,GAAG,EAAA;AAC1C,IAAA,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;AACxC;;MElPa,oBAAoB,CAAA;uGAApB,oBAAoB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA;AAApB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,oBAAoB,EAHrB,OAAA,EAAA,CAAA,cAAc,CACd,EAAA,OAAA,EAAA,CAAA,cAAc,EAAE,eAAe,CAAA,EAAA,CAAA;AAE9B,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,oBAAoB,YAFL,eAAe,CAAA,EAAA,CAAA;;2FAE9B,oBAAoB,EAAA,UAAA,EAAA,CAAA;kBAJhC,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;oBACR,OAAO,EAAE,CAAC,cAAc,CAAC;AACzB,oBAAA,OAAO,EAAE,CAAC,cAAc,EAAE,eAAe,CAAC;AAC3C,iBAAA;;;;;"}