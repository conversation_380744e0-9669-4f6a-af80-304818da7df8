<div class="login-container">
    <mat-card class="login-card">
        <mat-card-header>
            <mat-card-title>Login to Flower Shop</mat-card-title>
            <mat-card-subtitle>Welcome back! Please sign in to your account.</mat-card-subtitle>
        </mat-card-header>

        <mat-card-content>
            <form [formGroup]="loginForm" (ngSubmit)="onSubmit()">
                <mat-form-field appearance="outline" class="full-width">
                    <mat-label>Email</mat-label>
                    <input matInput type="email" formControlName="email" placeholder="Enter your email">
                    <mat-error *ngIf="loginForm.get('email')?.hasError('required')">
                        Email is required
                    </mat-error>
                    <mat-error *ngIf="loginForm.get('email')?.hasError('email')">
                        Please enter a valid email
                    </mat-error>
                </mat-form-field>

                <mat-form-field appearance="outline" class="full-width">
                    <mat-label>Password</mat-label>
                    <input matInput type="password" formControlName="password" placeholder="Enter your password">
                    <mat-error *ngIf="loginForm.get('password')?.hasError('required')">
                        Password is required
                    </mat-error>
                    <mat-error *ngIf="loginForm.get('password')?.hasError('minlength')">
                        Password must be at least 3 characters
                    </mat-error>
                </mat-form-field>

                <div class="button-container">
                    <button mat-raised-button color="primary" type="submit" [disabled]="!loginForm.valid || isLoading"
                        class="full-width">
                        {{ isLoading ? 'Signing in...' : 'Sign In' }}
                    </button>
                </div>
            </form>

            <div class="demo-credentials">
                <p><strong>Demo Credentials:</strong></p>
                <p>Admin: admin&#64;flowershop.com / admin</p>
                <p>Or register as a new client below</p>
            </div>
        </mat-card-content>

        <mat-card-actions>
            <button mat-button color="accent" (click)="goToRegister()">
                Don't have an account? Register
            </button>
        </mat-card-actions>
    </mat-card>
</div>
