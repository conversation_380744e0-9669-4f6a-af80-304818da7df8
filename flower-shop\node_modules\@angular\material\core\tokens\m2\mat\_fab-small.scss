@use 'sass:meta';
@use '../../token-definition';
@use '../../../theming/theming';
@use '../../../theming/inspection';
@use '../../../style/sass-utils';

// The prefix used to generate the fully qualified name for tokens in this file.
$prefix: (mat, fab-small);

// Tokens that can't be configured through Angular Material's current theming API,
// but may be in a future version of the theming API.
@function get-unthemable-tokens() {
  @return ();
}

// Tokens that can be configured through Angular Material's color theming API.
@function get-color-tokens($theme) {
  $is-dark: inspection.get-theme-type($theme) == dark;

  @return (
    // Color of icons and text projected into a FAB.
    foreground-color: inspection.get-theme-color($theme, foreground, base),

    // Color of the element that shows the hover, focus and pressed states.
    state-layer-color: inspection.get-theme-color($theme, foreground, base),

    // Color of the element that shows the hover, focus and pressed states while disabled.
    disabled-state-layer-color: inspection.get-theme-color($theme, foreground, base),

    // Color of the ripple element.
    ripple-color: inspection.get-theme-color($theme, foreground, base, 0.1),

    // Opacity of the ripple when the button is hovered.
    hover-state-layer-opacity: if($is-dark, 0.08, 0.04),

    // Opacity of the ripple when the button is focused.
    focus-state-layer-opacity: if($is-dark, 0.24, 0.12),

    // Opacity of the ripple when the button is pressed.
    pressed-state-layer-opacity: if($is-dark, 0.24, 0.12),

    // MDC doesn't have tokens for disabled FABs so we need to implemented them ourselves.
    // Background color of the container when the FAB is disabled.
    disabled-state-container-color: inspection.get-theme-color($theme, background, disabled-button,
      0.12),

    // Color of the icons and projected text when the FAB is disabled.
    disabled-state-foreground-color: inspection.get-theme-color($theme, foreground, disabled-button,
      if($is-dark, 0.5, 0.38)),
  );
}

// Generates the mapping for the properties that change based on the FAB palette color.
@function private-get-color-palette-color-tokens($theme, $palette-name) {
  // Ideally we would derive all values directly from the theme, but it causes a lot of regressions
  // internally. For now we fall back to the old hardcoded behavior only for internal apps.
  $foreground-color: null;
  $state-layer-color: null;
  $ripple-color: null;
  $contrast-color: inspection.get-theme-color($theme, $palette-name, default-contrast);

  @if (token-definition.$private-is-internal-build or
      meta.type-of($contrast-color) != 'color') {
    $is-dark: inspection.get-theme-type($theme) == dark;
    $container-color: inspection.get-theme-color($theme, $palette-name);
    $contrast-tone: token-definition.contrast-tone($container-color, $is-dark);
    $color: if($contrast-tone == 'dark', #000, #fff);
    $foreground-color: $color;
    $state-layer-color: $color;
    $ripple-color: rgba($color, 0.1);
  }
  @else {
    $foreground-color: inspection.get-theme-color($theme, $palette-name, default-contrast, 1);
    $state-layer-color: inspection.get-theme-color($theme, $palette-name, default-contrast, 1);
    $ripple-color: inspection.get-theme-color($theme, $palette-name, default-contrast, 0.1);
  }

  @return (
    foreground-color: $foreground-color,
    state-layer-color: $state-layer-color,
    ripple-color: $ripple-color,
  );
}

// Tokens that can be configured through Angular Material's typography theming API.
@function get-typography-tokens($theme) {
  @return ();
}

// Tokens that can be configured through Angular Material's density theming API.
@function get-density-tokens($theme) {
  $density-scale: theming.clamp-density(inspection.get-theme-density($theme), -3);

  @return (
    touch-target-display: if($density-scale < -1, none, block),
  );
}

// Combines the tokens generated by the above functions into a single map with placeholder values.
// This is used to create token slots.
@function get-token-slots() {
  @return sass-utils.deep-merge-all(
      get-unthemable-tokens(),
      get-color-tokens(token-definition.$placeholder-color-config),
      get-typography-tokens(token-definition.$placeholder-typography-config),
      get-density-tokens(token-definition.$placeholder-density-config)
  );
}
