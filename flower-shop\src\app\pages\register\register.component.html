<div class="register-container">
    <mat-card class="register-card">
        <mat-card-header>
            <mat-card-title>Create Account</mat-card-title>
            <mat-card-subtitle>Join our flower shop community!</mat-card-subtitle>
        </mat-card-header>

        <mat-card-content>
            <form [formGroup]="registerForm" (ngSubmit)="onSubmit()">
                <mat-form-field appearance="outline" class="full-width">
                    <mat-label>Full Name</mat-label>
                    <input matInput type="text" formControlName="name" placeholder="Enter your full name">
                    <mat-error *ngIf="registerForm.get('name')?.hasError('required')">
                        Name is required
                    </mat-error>
                    <mat-error *ngIf="registerForm.get('name')?.hasError('minlength')">
                        Name must be at least 2 characters
                    </mat-error>
                </mat-form-field>

                <mat-form-field appearance="outline" class="full-width">
                    <mat-label>Email</mat-label>
                    <input matInput type="email" formControlName="email" placeholder="Enter your email">
                    <mat-error *ngIf="registerForm.get('email')?.hasError('required')">
                        Email is required
                    </mat-error>
                    <mat-error *ngIf="registerForm.get('email')?.hasError('email')">
                        Please enter a valid email
                    </mat-error>
                </mat-form-field>

                <mat-form-field appearance="outline" class="full-width">
                    <mat-label>Password</mat-label>
                    <input matInput type="password" formControlName="password" placeholder="Enter your password">
                    <mat-error *ngIf="registerForm.get('password')?.hasError('required')">
                        Password is required
                    </mat-error>
                    <mat-error *ngIf="registerForm.get('password')?.hasError('minlength')">
                        Password must be at least 6 characters
                    </mat-error>
                </mat-form-field>

                <mat-form-field appearance="outline" class="full-width">
                    <mat-label>Confirm Password</mat-label>
                    <input matInput type="password" formControlName="confirmPassword"
                        placeholder="Confirm your password">
                    <mat-error *ngIf="registerForm.get('confirmPassword')?.hasError('required')">
                        Please confirm your password
                    </mat-error>
                    <mat-error *ngIf="registerForm.get('confirmPassword')?.hasError('passwordMismatch')">
                        Passwords do not match
                    </mat-error>
                </mat-form-field>

                <div class="button-container">
                    <button mat-raised-button color="primary" type="submit"
                        [disabled]="!registerForm.valid || isLoading" class="full-width">
                        {{ isLoading ? 'Creating Account...' : 'Create Account' }}
                    </button>
                </div>
            </form>
        </mat-card-content>

        <mat-card-actions>
            <button mat-button color="accent" (click)="goToLogin()">
                Already have an account? Sign In
            </button>
        </mat-card-actions>
    </mat-card>
</div>
