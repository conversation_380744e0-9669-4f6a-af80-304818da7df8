import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { FormsModule } from '@angular/forms';
import { Observable } from 'rxjs';
import { Cart, CartItem } from '../../models';
import { CartService } from '../../services/cart.service';

@Component({
    selector: 'app-cart',
    imports: [
        CommonModule,
        FormsModule,
        MatCardModule,
        MatButtonModule,
        MatIconModule,
        MatInputModule,
        MatFormFieldModule,
        MatSnackBarModule
    ],
    templateUrl: './cart.component.html',
    styleUrl: './cart.component.scss'
})
export class CartComponent implements OnInit {
    cart$: Observable<Cart>;

    constructor(
        private cartService: CartService,
        private router: Router,
        private snackBar: MatSnackBar
    ) {
        this.cart$ = this.cartService.getCart();
    }

    ngOnInit(): void {
        // Component initialization
    }

    updateQuantity(flowerId: number, quantity: number): void {
        if (quantity < 1) {
            this.removeItem(flowerId);
            return;
        }

        this.cartService.updateQuantity(flowerId, quantity);
        this.snackBar.open('Quantity updated', 'Close', { duration: 2000 });
    }

    removeItem(flowerId: number): void {
        this.cartService.removeFromCart(flowerId);
        this.snackBar.open('Item removed from cart', 'Close', { duration: 2000 });
    }

    clearCart(): void {
        this.cartService.clearCart();
        this.snackBar.open('Cart cleared', 'Close', { duration: 2000 });
    }

    proceedToCheckout(): void {
        this.router.navigate(['/checkout']);
    }

    continueShopping(): void {
        this.router.navigate(['/']);
    }
}
