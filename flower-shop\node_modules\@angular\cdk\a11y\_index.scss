/// Emits a CSS class, `.cdk-visually-hidden`. This class can be applied to an element
/// to make that element visually hidden while remaining available to assistive technology.
@mixin a11y-visually-hidden() {
  .cdk-visually-hidden {
    border: 0;
    clip: rect(0 0 0 0);
    height: 1px;
    margin: -1px;
    overflow: hidden;
    padding: 0;
    position: absolute;
    width: 1px;

    // This works around a Chrome bug that can cause the tab to crash when large amounts of
    // non-English text get wrapped: https://bugs.chromium.org/p/chromium/issues/detail?id=1201444
    white-space: nowrap;

    // Avoid browsers rendering the focus ring in some cases.
    outline: 0;

    // Avoid some cases where the browser will still render the native controls (see #9049).
    -webkit-appearance: none;
    -moz-appearance: none;

    // We need at least one of top/bottom/left/right in order to prevent cases where the
    // absolute-positioned element is pushed down and can affect scrolling (see #24597).
    // `left` was chosen here, because it's the least likely to break overrides where the
    // element might have been positioned (e.g. `mat-checkbox`).
    left: 0;

    [dir='rtl'] & {
      left: auto;
      right: 0;
    }
  }
}

/// @deprecated Use `a11y-visually-hidden`.
@mixin a11y() {
  @include a11y-visually-hidden;
}

/// Applies styles for users in high contrast mode.
///
/// @param {String} target Type of high contrast setting to target. Can be `active` or `none`.
///   Defaults to `active`.
/// @param {String} encapsulation No longer used and will be removed.
@mixin high-contrast($target: active, $encapsulation: null) {
  // Historically we used to support `black-on-white` and `white-on-black` so we
  // allow them here anyway. They'll be coerced to `active` below.
  @if ($target != 'active' and $target != 'none' and $target != 'black-on-white' and
    $target != 'white-on-black') {
    @error 'Unknown cdk-high-contrast value "#{$target}" provided. ' +
           'Allowed values are "active" and "none"';
  }

  @media (forced-colors: #{if($target == none, none, active)}) {
    @content;
  }
}
