import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { Cart, CartItem, Flower } from '../models';

@Injectable({
    providedIn: 'root'
})
export class CartService {
    private cartSubject = new BehaviorSubject<Cart>(this.getInitialCart());
    public cart$ = this.cartSubject.asObservable();

    constructor() {
        // Load cart from localStorage if available (only in browser environment)
        if (typeof window !== 'undefined' && window.localStorage) {
            const savedCart = window.localStorage.getItem('cart');
            if (savedCart) {
                try {
                    const cart = JSON.parse(savedCart);
                    this.cartSubject.next(this.calculateCartTotals(cart));
                } catch (error) {
                    console.error('Error loading cart from localStorage:', error);
                }
            }
        }
    }

    private getInitialCart(): Cart {
        return {
            items: [],
            totalItems: 0,
            totalPrice: 0
        };
    }

    getCart(): Observable<Cart> {
        return this.cart$;
    }

    addToCart(flower: Flower, quantity: number = 1): void {
        const currentCart = this.cartSubject.value;
        const existingItemIndex = currentCart.items.findIndex(
            item => item.flower.id === flower.id
        );

        let updatedItems: CartItem[];

        if (existingItemIndex >= 0) {
            // Update existing item
            updatedItems = [...currentCart.items];
            updatedItems[existingItemIndex] = {
                ...updatedItems[existingItemIndex],
                quantity: updatedItems[existingItemIndex].quantity + quantity
            };
        } else {
            // Add new item
            const newItem: CartItem = { flower, quantity };
            updatedItems = [...currentCart.items, newItem];
        }

        const updatedCart = this.calculateCartTotals({ items: updatedItems });
        this.cartSubject.next(updatedCart);
        this.saveCartToStorage(updatedCart);
    }

    removeFromCart(flowerId: number): void {
        const currentCart = this.cartSubject.value;
        const updatedItems = currentCart.items.filter(
            item => item.flower.id !== flowerId
        );

        const updatedCart = this.calculateCartTotals({ items: updatedItems });
        this.cartSubject.next(updatedCart);
        this.saveCartToStorage(updatedCart);
    }

    updateQuantity(flowerId: number, quantity: number): void {
        if (quantity <= 0) {
            this.removeFromCart(flowerId);
            return;
        }

        const currentCart = this.cartSubject.value;
        const updatedItems = currentCart.items.map(item =>
            item.flower.id === flowerId
                ? { ...item, quantity }
                : item
        );

        const updatedCart = this.calculateCartTotals({ items: updatedItems });
        this.cartSubject.next(updatedCart);
        this.saveCartToStorage(updatedCart);
    }

    clearCart(): void {
        const emptyCart = this.getInitialCart();
        this.cartSubject.next(emptyCart);
        this.saveCartToStorage(emptyCart);
    }

    getCartItemCount(): Observable<number> {
        return this.cart$.pipe(
            map(cart => cart.totalItems)
        );
    }

    getCartTotal(): Observable<number> {
        return this.cart$.pipe(
            map(cart => cart.totalPrice)
        );
    }

    isInCart(flowerId: number): Observable<boolean> {
        return this.cart$.pipe(
            map(cart => cart.items.some(item => item.flower.id === flowerId))
        );
    }

    getItemQuantity(flowerId: number): Observable<number> {
        return this.cart$.pipe(
            map(cart => {
                const item = cart.items.find(item => item.flower.id === flowerId);
                return item ? item.quantity : 0;
            })
        );
    }

    private calculateCartTotals(cart: { items: CartItem[] }): Cart {
        const totalItems = cart.items.reduce((sum, item) => sum + item.quantity, 0);
        const totalPrice = cart.items.reduce(
            (sum, item) => sum + (item.flower.price * item.quantity),
            0
        );

        return {
            items: cart.items,
            totalItems,
            totalPrice: Math.round(totalPrice * 100) / 100 // Round to 2 decimal places
        };
    }

    private saveCartToStorage(cart: Cart): void {
        if (typeof window !== 'undefined' && window.localStorage) {
            window.localStorage.setItem('cart', JSON.stringify(cart));
        }
    }
}


