import { Injectable, Inject, PLATFORM_ID } from '@angular/core';
import { isPlatformBrowser } from '@angular/common';
import { BehaviorSubject, Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { Cart, CartItem, Flower } from '../models';

@Injectable({
  providedIn: 'root'
})
export class CartService {
  private cartSubject = new BehaviorSubject<Cart>({
    id: '1',
    userId: '',
    items: [],
    totalItems: 0,
    totalPrice: 0,
    createdAt: new Date(),
    updatedAt: new Date()
  });

  public cart$ = this.cartSubject.asObservable();
  private isBrowser: boolean;

  constructor(@Inject(PLATFORM_ID) private platformId: Object) {
    this.isBrowser = isPlatformBrowser(this.platformId);
    this.loadCartFromStorage();
  }

  private loadCartFromStorage(): void {
    if (this.isBrowser) {
      try {
        const cartData = localStorage.getItem('cart');
        if (cartData) {
          const cart = JSON.parse(cartData);
          this.cartSubject.next(cart);
        }
      } catch (error) {
        console.error('Error loading cart from storage:', error);
      }
    }
  }

  private saveCartToStorage(cart: Cart): void {
    if (this.isBrowser) {
      try {
        localStorage.setItem('cart', JSON.stringify(cart));
      } catch (error) {
        console.error('Error saving cart to storage:', error);
      }
    }
  }

  private updateCartTotals(cart: Cart): Cart {
    cart.totalItems = cart.items.reduce((total, item) => total + item.quantity, 0);
    cart.totalPrice = cart.items.reduce((total, item) => total + (item.price * item.quantity), 0);
    cart.updatedAt = new Date();
    return cart;
  }

  addToCart(flower: Flower, quantity: number = 1): void {
    const currentCart = this.cartSubject.value;
    const existingItemIndex = currentCart.items.findIndex(item => item.flowerId === flower.id);

    if (existingItemIndex > -1) {
      // Update existing item
      currentCart.items[existingItemIndex].quantity += quantity;
    } else {
      // Add new item
      const newItem: CartItem = {
        id: Date.now().toString(),
        flowerId: flower.id,
        flower: flower,
        quantity: quantity,
        price: flower.price,
        addedAt: new Date()
      };
      currentCart.items.push(newItem);
    }

    const updatedCart = this.updateCartTotals(currentCart);
    this.cartSubject.next(updatedCart);
    this.saveCartToStorage(updatedCart);
  }

  removeFromCart(itemId: string): void {
    const currentCart = this.cartSubject.value;
    currentCart.items = currentCart.items.filter(item => item.id !== itemId);
    
    const updatedCart = this.updateCartTotals(currentCart);
    this.cartSubject.next(updatedCart);
    this.saveCartToStorage(updatedCart);
  }

  updateQuantity(itemId: string, quantity: number): void {
    if (quantity <= 0) {
      this.removeFromCart(itemId);
      return;
    }

    const currentCart = this.cartSubject.value;
    const itemIndex = currentCart.items.findIndex(item => item.id === itemId);
    
    if (itemIndex > -1) {
      currentCart.items[itemIndex].quantity = quantity;
      const updatedCart = this.updateCartTotals(currentCart);
      this.cartSubject.next(updatedCart);
      this.saveCartToStorage(updatedCart);
    }
  }

  clearCart(): void {
    const emptyCart: Cart = {
      id: '1',
      userId: '',
      items: [],
      totalItems: 0,
      totalPrice: 0,
      createdAt: new Date(),
      updatedAt: new Date()
    };
    
    this.cartSubject.next(emptyCart);
    this.saveCartToStorage(emptyCart);
  }

  getCart(): Observable<Cart> {
    return this.cart$;
  }

  getItemCount(): Observable<number> {
    return this.cart$.pipe(
      map(cart => cart.totalItems)
    );
  }

  getTotalPrice(): Observable<number> {
    return this.cart$.pipe(
      map(cart => cart.totalPrice)
    );
  }

  isInCart(flowerId: string): Observable<boolean> {
    return this.cart$.pipe(
      map(cart => cart.items.some(item => item.flowerId === flowerId))
    );
  }
}
