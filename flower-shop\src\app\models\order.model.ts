import { CartItem, CheckoutData } from './cart.model';

export interface Order {
  id: number;
  items: CartItem[];
  customerInfo: CheckoutData;
  totalAmount: number;
  status: OrderStatus;
  orderDate: Date;
  deliveryDate?: Date;
}

export enum OrderStatus {
  PENDING = 'pending',
  CONFIRMED = 'confirmed',
  PROCESSING = 'processing',
  SHIPPED = 'shipped',
  DELIVERED = 'delivered',
  CANCELLED = 'cancelled'
}
