//
// Design system display name: Material 3
// Design system version: v0.161
//

@use 'sass:map';

@use './md-sys-color';

@use './md-sys-elevation';

@use './md-sys-shape';

@use './md-sys-state';

@use './md-sys-typescale';

$_default: (
  'md-sys-color': md-sys-color.values-light(),
  'md-sys-elevation': md-sys-elevation.values(),
  'md-sys-shape': md-sys-shape.values(),
  'md-sys-state': md-sys-state.values(),
  'md-sys-typescale': md-sys-typescale.values(),
);

@function values($deps: $_default, $exclude-hardcoded-values: false) {
  @return (
    'container-height': if($exclude-hardcoded-values, null, 32px),
    'container-shape': map.get($deps, 'md-sys-shape', 'corner-small'),
    'container-surface-tint-layer-color':
      map.get($deps, 'md-sys-color', 'surface-tint'),
    'disabled-label-text-color': map.get($deps, 'md-sys-color', 'on-surface'),
    'disabled-label-text-opacity': if($exclude-hardcoded-values, null, 0.38),
    'dragged-container-elevation': map.get($deps, 'md-sys-elevation', 'level4'),
    'dragged-label-text-color':
      map.get($deps, 'md-sys-color', 'on-surface-variant'),
    'dragged-state-layer-color':
      map.get($deps, 'md-sys-color', 'on-surface-variant'),
    'dragged-state-layer-opacity':
      map.get($deps, 'md-sys-state', 'dragged-state-layer-opacity'),
    'elevated-container-color': map.get($deps, 'md-sys-color', 'surface'),
    'elevated-container-elevation': map.get($deps, 'md-sys-elevation', 'level1'),
    'elevated-container-shadow-color': map.get($deps, 'md-sys-color', 'shadow'),
    'elevated-disabled-container-color':
      map.get($deps, 'md-sys-color', 'on-surface'),
    'elevated-disabled-container-elevation':
      map.get($deps, 'md-sys-elevation', 'level0'),
    'elevated-disabled-container-opacity':
      if($exclude-hardcoded-values, null, 0.12),
    'elevated-focus-container-elevation':
      map.get($deps, 'md-sys-elevation', 'level1'),
    'elevated-hover-container-elevation':
      map.get($deps, 'md-sys-elevation', 'level2'),
    'elevated-pressed-container-elevation':
      map.get($deps, 'md-sys-elevation', 'level1'),
    'flat-container-elevation': map.get($deps, 'md-sys-elevation', 'level0'),
    'flat-disabled-outline-color': map.get($deps, 'md-sys-color', 'on-surface'),
    'flat-disabled-outline-opacity': if($exclude-hardcoded-values, null, 0.12),
    'flat-focus-outline-color':
      map.get($deps, 'md-sys-color', 'on-surface-variant'),
    'flat-outline-color': map.get($deps, 'md-sys-color', 'outline'),
    'flat-outline-width': if($exclude-hardcoded-values, null, 1px),
    'focus-label-text-color':
      map.get($deps, 'md-sys-color', 'on-surface-variant'),
    'focus-state-layer-color':
      map.get($deps, 'md-sys-color', 'on-surface-variant'),
    'focus-state-layer-opacity':
      map.get($deps, 'md-sys-state', 'focus-state-layer-opacity'),
    'hover-label-text-color':
      map.get($deps, 'md-sys-color', 'on-surface-variant'),
    'hover-state-layer-color':
      map.get($deps, 'md-sys-color', 'on-surface-variant'),
    'hover-state-layer-opacity':
      map.get($deps, 'md-sys-state', 'hover-state-layer-opacity'),
    'label-text-color': map.get($deps, 'md-sys-color', 'on-surface-variant'),
    'label-text-font': map.get($deps, 'md-sys-typescale', 'label-large-font'),
    'label-text-line-height':
      map.get($deps, 'md-sys-typescale', 'label-large-line-height'),
    'label-text-size': map.get($deps, 'md-sys-typescale', 'label-large-size'),
    'label-text-tracking':
      map.get($deps, 'md-sys-typescale', 'label-large-tracking'),
    'label-text-type':
      if(
        $exclude-hardcoded-values,
        null,
        // Warning: risk of reduced fidelity from using this composite typography token.
        // Tokens md.comp.suggestion-chip.label-text.tracking cannot be represented in the "font"
        // property shorthand. Consider using the discrete properties instead.
          map.get($deps, 'md-sys-typescale', 'label-large-weight')
          map.get($deps, 'md-sys-typescale', 'label-large-size') #{'/'} map.get(
            $deps,
            'md-sys-typescale',
            'label-large-line-height'
          ) map.get($deps, 'md-sys-typescale', 'label-large-font')
      ),
    'label-text-weight':
      map.get($deps, 'md-sys-typescale', 'label-large-weight'),
    'pressed-label-text-color':
      map.get($deps, 'md-sys-color', 'on-surface-variant'),
    'pressed-state-layer-color':
      map.get($deps, 'md-sys-color', 'on-surface-variant'),
    'pressed-state-layer-opacity':
      map.get($deps, 'md-sys-state', 'pressed-state-layer-opacity'),
    'with-leading-icon-disabled-leading-icon-color':
      map.get($deps, 'md-sys-color', 'on-surface'),
    'with-leading-icon-disabled-leading-icon-opacity':
      if($exclude-hardcoded-values, null, 0.38),
    'with-leading-icon-dragged-leading-icon-color':
      map.get($deps, 'md-sys-color', 'primary'),
    'with-leading-icon-focus-leading-icon-color':
      map.get($deps, 'md-sys-color', 'primary'),
    'with-leading-icon-hover-leading-icon-color':
      map.get($deps, 'md-sys-color', 'primary'),
    'with-leading-icon-leading-icon-color':
      map.get($deps, 'md-sys-color', 'primary'),
    'with-leading-icon-leading-icon-size':
      if($exclude-hardcoded-values, null, 18px),
    'with-leading-icon-pressed-leading-icon-color':
      map.get($deps, 'md-sys-color', 'primary')
  );
}
