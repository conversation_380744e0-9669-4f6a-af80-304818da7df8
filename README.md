Here’s a refined and improved version of your prompt. It maintains all technical requirements while enhancing clarity, structure, and tone. I’ve also strengthened the professional language, optimized for a competent AI or developer assistant to interpret and act upon it efficiently:

---

**Prompt:**

Please assist me in building a complete, production-ready flower shop web application using Angular. The app should support both client-facing features and an admin dashboard, with the following detailed technical and functional requirements:

---

### **1. Environment Setup & Validation**

* Detect current Angular CLI and Node.js versions.
* Confirm that the installed Node.js version is compatible with Angular's LTS requirements.

---

### **2. Project Initialization**

* Scaffold a new Angular project named `flower-shop` using Angular CLI.
* Establish a clean folder structure: `components/`, `services/`, `models/`, `guards/`, `pages/`.
* Configure Angular routing and lazy loading with role-based navigation support (client and admin).

---

### **3. Authentication & Authorization**

* Implement two user roles:

  * **Client**: Browse, view details, manage cart, and checkout.
  * **Admin**: Full CRUD access to flower inventory and order management.
* Setup hardcoded credentials for prototyping:

  * Admin: `admin/admin`
  * Clients sign up, then log in to access features.
* Integrate route guards to protect admin routes.
* Build login/logout functionality with in-session state management.
* Dynamically render navigation menus based on authenticated role.

---

### **4. Client-Side Features**

* **Flower Catalog**: Paginated grid/list display.
* **Details Page**: Large images, rich descriptions.
* **Cart**: Add/remove/update items, compute totals.
* **Checkout**: Collect name, email, and address in a validated form.
* **Search/Filter**: Real-time name search and price filtering.

---

### **5. Admin Dashboard**

* CRUD management for flower inventory.
* Forms for creating/editing flowers (name, description, price, category, image URL).
* Confirmation dialogs on deletions.
* Optional: Order management view.

---

### **6. Data Handling**

* Use local JSON files (`assets/flowers.json`, `assets/orders.json`) to simulate persistence.
* Services:

  * `FlowerService`: Inventory management.
  * `CartService`: Cart state management.
  * `AuthService`: Role-based auth logic.
* Read/write data using Angular's HTTP client with robust error handling.

---

### **7. Image Management**

* Use placeholder images (e.g., Unsplash API or Lorem Picsum).
* Add fallback images for broken links.
* Implement lazy loading for performance optimization.

---

### **8. UI/UX & Responsiveness**

* Choose **Angular Material** or **Bootstrap** (preferably Angular Material for tighter Angular integration).
* Responsive layout: mobile-first design with adaptive components.
* Navigation menus adapt by user role.
* Use loading indicators and notification toasts for async actions.
* Smooth animations via Angular’s animation module.

---

### **9. Technical Best Practices**

* All forms use **Reactive Forms** with full validation.
* Define strict TypeScript models/interfaces.
* Apply dependency injection and proper lifecycle hooks.
* Centralize error handling across services.
* Add unit tests for critical logic in components/services.

---

### **10. Development Roadmap**

1. Validate Angular/Node environment.
2. Initialize project and set up folder architecture.
3. Implement routing and role-based authentication.
4. Build flower catalog and client browsing features.
5. Implement cart and checkout.
6. Build admin dashboard with full CRUD.
7. Add image handling and placeholder logic.
8. Integrate styling and responsive layout.
9. Finalize with testing and polish (error states, loaders, role menus).

---

### Next Steps:

Please start by verifying the Angular environment. Once that’s confirmed, outline a step-by-step implementation plan following the roadmap above, prioritizing a modular and testable approach.

---

Would you like this project integrated with a backend (e.g., FastAPI or Firebase) later for full data persistence?
