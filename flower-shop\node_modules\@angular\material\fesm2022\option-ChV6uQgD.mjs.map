{"version": 3, "file": "option-ChV6uQgD.mjs", "sources": ["../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/core/option/option-parent.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/core/option/optgroup.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/core/option/optgroup.html", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/core/option/option.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/core/option/option.html"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {InjectionToken, Signal} from '@angular/core';\n\n/**\n * Describes a parent component that manages a list of options.\n * Contains properties that the options can inherit.\n * @docs-private\n */\nexport interface MatOptionParentComponent {\n  disableRipple?: boolean | Signal<boolean>;\n  multiple?: boolean;\n  inertGroups?: boolean;\n  hideSingleSelectionIndicator?: boolean;\n}\n\n/**\n * Injection token used to provide the parent component to options.\n */\nexport const MAT_OPTION_PARENT_COMPONENT = new InjectionToken<MatOptionParentComponent>(\n  'MAT_OPTION_PARENT_COMPONENT',\n);\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  Component,\n  ViewEncapsulation,\n  ChangeDetectionStrategy,\n  Input,\n  InjectionToken,\n  booleanAttribute,\n  inject,\n} from '@angular/core';\nimport {_IdGenerator} from '@angular/cdk/a11y';\nimport {MatOptionParentComponent, MAT_OPTION_PARENT_COMPONENT} from './option-parent';\n\n// Notes on the accessibility pattern used for `mat-optgroup`.\n// The option group has two different \"modes\": regular and inert. The regular mode uses the\n// recommended a11y pattern which has `role=\"group\"` on the group element with `aria-labelledby`\n// pointing to the label. This works for `mat-select`, but it seems to hit a bug for autocomplete\n// under VoiceOver where the group doesn't get read out at all. The bug appears to be that if\n// there's __any__ a11y-related attribute on the group (e.g. `role` or `aria-labelledby`),\n// VoiceOver on Safari won't read it out.\n// We've introduced the `inert` mode as a workaround. Under this mode, all a11y attributes are\n// removed from the group, and we get the screen reader to read out the group label by mirroring it\n// inside an invisible element in the option. This is sub-optimal, because the screen reader will\n// repeat the group label on each navigation, whereas the default pattern only reads the group when\n// the user enters a new group. The following alternate approaches were considered:\n// 1. Reading out the group label using the `LiveAnnouncer` solves the problem, but we can't control\n//    when the text will be read out so sometimes it comes in too late or never if the user\n//    navigates quickly.\n// 2. `<mat-option aria-describedby=\"groupLabel\"` - This works on Safari, but VoiceOver in Chrome\n//    won't read out the description at all.\n// 3. `<mat-option aria-labelledby=\"optionLabel groupLabel\"` - This works on Chrome, but Safari\n//     doesn't read out the text at all. Furthermore, on\n\n/**\n * Injection token that can be used to reference instances of `MatOptgroup`. It serves as\n * alternative token to the actual `MatOptgroup` class which could cause unnecessary\n * retention of the class and its component metadata.\n */\nexport const MAT_OPTGROUP = new InjectionToken<MatOptgroup>('MatOptgroup');\n\n/**\n * Component that is used to group instances of `mat-option`.\n */\n@Component({\n  selector: 'mat-optgroup',\n  exportAs: 'matOptgroup',\n  templateUrl: 'optgroup.html',\n  encapsulation: ViewEncapsulation.None,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  styleUrl: 'optgroup.css',\n  host: {\n    'class': 'mat-mdc-optgroup',\n    '[attr.role]': '_inert ? null : \"group\"',\n    '[attr.aria-disabled]': '_inert ? null : disabled.toString()',\n    '[attr.aria-labelledby]': '_inert ? null : _labelId',\n  },\n  providers: [{provide: MAT_OPTGROUP, useExisting: MatOptgroup}],\n})\nexport class MatOptgroup {\n  /** Label for the option group. */\n  @Input() label: string;\n\n  /** whether the option group is disabled. */\n  @Input({transform: booleanAttribute}) disabled: boolean = false;\n\n  /** Unique id for the underlying label. */\n  _labelId: string = inject(_IdGenerator).getId('mat-optgroup-label-');\n\n  /** Whether the group is in inert a11y mode. */\n  _inert: boolean;\n\n  constructor(...args: unknown[]);\n\n  constructor() {\n    const parent = inject<MatOptionParentComponent>(MAT_OPTION_PARENT_COMPONENT, {optional: true});\n    this._inert = parent?.inertGroups ?? false;\n  }\n}\n", "<span\n  class=\"mat-mdc-optgroup-label\"\n  role=\"presentation\"\n  [class.mdc-list-item--disabled]=\"disabled\"\n  [id]=\"_labelId\">\n  <span class=\"mdc-list-item__primary-text\">{{ label }} <ng-content></ng-content></span>\n</span>\n\n<ng-content select=\"mat-option, ng-container\"></ng-content>\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {_IdGenerator, FocusableOption, FocusOrigin} from '@angular/cdk/a11y';\nimport {ENTER, hasModifier<PERSON>ey, SPACE} from '@angular/cdk/keycodes';\nimport {\n  Component,\n  ViewEncapsulation,\n  ChangeDetectionStrategy,\n  ElementRef,\n  ChangeDetectorRef,\n  AfterViewChecked,\n  OnDestroy,\n  Input,\n  Output,\n  EventEmitter,\n  QueryList,\n  ViewChild,\n  booleanAttribute,\n  inject,\n  isSignal,\n  Signal,\n} from '@angular/core';\nimport {Subject} from 'rxjs';\nimport {MAT_OPTGROUP, MatOptgroup} from './optgroup';\nimport {MatOptionParentComponent, MAT_OPTION_PARENT_COMPONENT} from './option-parent';\nimport {MatRipple} from '../ripple/ripple';\nimport {MatPseudoCheckbox} from '../selection/pseudo-checkbox/pseudo-checkbox';\nimport {_StructuralStylesLoader} from '../focus-indicators/structural-styles';\nimport {_CdkPrivateStyleLoader, _VisuallyHiddenLoader} from '@angular/cdk/private';\n\n/** Event object emitted by MatOption when selected or deselected. */\nexport class MatOptionSelectionChange<T = any> {\n  constructor(\n    /** Reference to the option that emitted the event. */\n    public source: MatOption<T>,\n    /** Whether the change in the option's value was a result of a user action. */\n    public isUserInput = false,\n  ) {}\n}\n\n/**\n * Single option inside of a `<mat-select>` element.\n */\n@Component({\n  selector: 'mat-option',\n  exportAs: 'matOption',\n  host: {\n    'role': 'option',\n    '[class.mdc-list-item--selected]': 'selected',\n    '[class.mat-mdc-option-multiple]': 'multiple',\n    '[class.mat-mdc-option-active]': 'active',\n    '[class.mdc-list-item--disabled]': 'disabled',\n    '[id]': 'id',\n    // Set aria-selected to false for non-selected items and true for selected items. Conform to\n    // [WAI ARIA Listbox authoring practices guide](\n    //  https://www.w3.org/WAI/ARIA/apg/patterns/listbox/), \"If any options are selected, each\n    // selected option has either aria-selected or aria-checked  set to true. All options that are\n    // selectable but not selected have either aria-selected or aria-checked set to false.\" Align\n    // aria-selected implementation of Chips and List components.\n    //\n    // Set `aria-selected=\"false\"` on not-selected listbox options to fix VoiceOver announcing\n    // every option as \"selected\" (#21491).\n    '[attr.aria-selected]': 'selected',\n    '[attr.aria-disabled]': 'disabled.toString()',\n    '(click)': '_selectViaInteraction()',\n    '(keydown)': '_handleKeydown($event)',\n    'class': 'mat-mdc-option mdc-list-item',\n  },\n  styleUrl: 'option.css',\n  templateUrl: 'option.html',\n  encapsulation: ViewEncapsulation.None,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  imports: [MatPseudoCheckbox, MatRipple],\n})\nexport class MatOption<T = any> implements FocusableOption, AfterViewChecked, OnDestroy {\n  private _element = inject<ElementRef<HTMLElement>>(ElementRef);\n  _changeDetectorRef = inject(ChangeDetectorRef);\n  private _parent = inject<MatOptionParentComponent>(MAT_OPTION_PARENT_COMPONENT, {optional: true});\n  group = inject<MatOptgroup>(MAT_OPTGROUP, {optional: true});\n\n  private _signalDisableRipple = false;\n  private _selected = false;\n  private _active = false;\n  private _disabled = false;\n  private _mostRecentViewValue = '';\n\n  /** Whether the wrapping component is in multiple selection mode. */\n  get multiple() {\n    return this._parent && this._parent.multiple;\n  }\n\n  /** Whether or not the option is currently selected. */\n  get selected(): boolean {\n    return this._selected;\n  }\n\n  /** The form value of the option. */\n  @Input() value: T;\n\n  /** The unique ID of the option. */\n  @Input() id: string = inject(_IdGenerator).getId('mat-option-');\n\n  /** Whether the option is disabled. */\n  @Input({transform: booleanAttribute})\n  get disabled(): boolean {\n    return (this.group && this.group.disabled) || this._disabled;\n  }\n  set disabled(value: boolean) {\n    this._disabled = value;\n  }\n\n  /** Whether ripples for the option are disabled. */\n  get disableRipple(): boolean {\n    return this._signalDisableRipple\n      ? (this._parent!.disableRipple as Signal<boolean>)()\n      : !!this._parent?.disableRipple;\n  }\n\n  /** Whether to display checkmark for single-selection. */\n  get hideSingleSelectionIndicator(): boolean {\n    return !!(this._parent && this._parent.hideSingleSelectionIndicator);\n  }\n\n  /** Event emitted when the option is selected or deselected. */\n  // tslint:disable-next-line:no-output-on-prefix\n  @Output() readonly onSelectionChange = new EventEmitter<MatOptionSelectionChange<T>>();\n\n  /** Element containing the option's text. */\n  @ViewChild('text', {static: true}) _text: ElementRef<HTMLElement> | undefined;\n\n  /** Emits when the state of the option changes and any parents have to be notified. */\n  readonly _stateChanges = new Subject<void>();\n\n  constructor(...args: unknown[]);\n  constructor() {\n    const styleLoader = inject(_CdkPrivateStyleLoader);\n    styleLoader.load(_StructuralStylesLoader);\n    styleLoader.load(_VisuallyHiddenLoader);\n    this._signalDisableRipple = !!this._parent && isSignal(this._parent.disableRipple);\n  }\n\n  /**\n   * Whether or not the option is currently active and ready to be selected.\n   * An active option displays styles as if it is focused, but the\n   * focus is actually retained somewhere else. This comes in handy\n   * for components like autocomplete where focus must remain on the input.\n   */\n  get active(): boolean {\n    return this._active;\n  }\n\n  /**\n   * The displayed value of the option. It is necessary to show the selected option in the\n   * select's trigger.\n   */\n  get viewValue(): string {\n    // TODO(kara): Add input property alternative for node envs.\n    return (this._text?.nativeElement.textContent || '').trim();\n  }\n\n  /** Selects the option. */\n  select(emitEvent = true): void {\n    if (!this._selected) {\n      this._selected = true;\n      this._changeDetectorRef.markForCheck();\n\n      if (emitEvent) {\n        this._emitSelectionChangeEvent();\n      }\n    }\n  }\n\n  /** Deselects the option. */\n  deselect(emitEvent = true): void {\n    if (this._selected) {\n      this._selected = false;\n      this._changeDetectorRef.markForCheck();\n\n      if (emitEvent) {\n        this._emitSelectionChangeEvent();\n      }\n    }\n  }\n\n  /** Sets focus onto this option. */\n  focus(_origin?: FocusOrigin, options?: FocusOptions): void {\n    // Note that we aren't using `_origin`, but we need to keep it because some internal consumers\n    // use `MatOption` in a `FocusKeyManager` and we need it to match `FocusableOption`.\n    const element = this._getHostElement();\n\n    if (typeof element.focus === 'function') {\n      element.focus(options);\n    }\n  }\n\n  /**\n   * This method sets display styles on the option to make it appear\n   * active. This is used by the ActiveDescendantKeyManager so key\n   * events will display the proper options as active on arrow key events.\n   */\n  setActiveStyles(): void {\n    if (!this._active) {\n      this._active = true;\n      this._changeDetectorRef.markForCheck();\n    }\n  }\n\n  /**\n   * This method removes display styles on the option that made it appear\n   * active. This is used by the ActiveDescendantKeyManager so key\n   * events will display the proper options as active on arrow key events.\n   */\n  setInactiveStyles(): void {\n    if (this._active) {\n      this._active = false;\n      this._changeDetectorRef.markForCheck();\n    }\n  }\n\n  /** Gets the label to be used when determining whether the option should be focused. */\n  getLabel(): string {\n    return this.viewValue;\n  }\n\n  /** Ensures the option is selected when activated from the keyboard. */\n  _handleKeydown(event: KeyboardEvent): void {\n    if ((event.keyCode === ENTER || event.keyCode === SPACE) && !hasModifierKey(event)) {\n      this._selectViaInteraction();\n\n      // Prevent the page from scrolling down and form submits.\n      event.preventDefault();\n    }\n  }\n\n  /**\n   * `Selects the option while indicating the selection came from the user. Used to\n   * determine if the select's view -> model callback should be invoked.`\n   */\n  _selectViaInteraction(): void {\n    if (!this.disabled) {\n      this._selected = this.multiple ? !this._selected : true;\n      this._changeDetectorRef.markForCheck();\n      this._emitSelectionChangeEvent(true);\n    }\n  }\n\n  /** Returns the correct tabindex for the option depending on disabled state. */\n  // This method is only used by `MatLegacyOption`. Keeping it here to avoid breaking the types.\n  // That's because `MatLegacyOption` use `MatOption` type in a few places such as\n  // `MatOptionSelectionChange`. It is safe to delete this when `MatLegacyOption` is deleted.\n  _getTabIndex(): string {\n    return this.disabled ? '-1' : '0';\n  }\n\n  /** Gets the host DOM element. */\n  _getHostElement(): HTMLElement {\n    return this._element.nativeElement;\n  }\n\n  ngAfterViewChecked() {\n    // Since parent components could be using the option's label to display the selected values\n    // (e.g. `mat-select`) and they don't have a way of knowing if the option's label has changed\n    // we have to check for changes in the DOM ourselves and dispatch an event. These checks are\n    // relatively cheap, however we still limit them only to selected options in order to avoid\n    // hitting the DOM too often.\n    if (this._selected) {\n      const viewValue = this.viewValue;\n\n      if (viewValue !== this._mostRecentViewValue) {\n        if (this._mostRecentViewValue) {\n          this._stateChanges.next();\n        }\n\n        this._mostRecentViewValue = viewValue;\n      }\n    }\n  }\n\n  ngOnDestroy() {\n    this._stateChanges.complete();\n  }\n\n  /** Emits the selection change event. */\n  private _emitSelectionChangeEvent(isUserInput = false): void {\n    this.onSelectionChange.emit(new MatOptionSelectionChange<T>(this, isUserInput));\n  }\n}\n\n/**\n * Counts the amount of option group labels that precede the specified option.\n * @param optionIndex Index of the option at which to start counting.\n * @param options Flat list of all of the options.\n * @param optionGroups Flat list of all of the option groups.\n * @docs-private\n */\nexport function _countGroupLabelsBeforeOption(\n  optionIndex: number,\n  options: QueryList<MatOption>,\n  optionGroups: QueryList<MatOptgroup>,\n): number {\n  if (optionGroups.length) {\n    let optionsArray = options.toArray();\n    let groups = optionGroups.toArray();\n    let groupCounter = 0;\n\n    for (let i = 0; i < optionIndex + 1; i++) {\n      if (optionsArray[i].group && optionsArray[i].group === groups[groupCounter]) {\n        groupCounter++;\n      }\n    }\n\n    return groupCounter;\n  }\n\n  return 0;\n}\n\n/**\n * Determines the position to which to scroll a panel in order for an option to be into view.\n * @param optionOffset Offset of the option from the top of the panel.\n * @param optionHeight Height of the options.\n * @param currentScrollPosition Current scroll position of the panel.\n * @param panelHeight Height of the panel.\n * @docs-private\n */\nexport function _getOptionScrollPosition(\n  optionOffset: number,\n  optionHeight: number,\n  currentScrollPosition: number,\n  panelHeight: number,\n): number {\n  if (optionOffset < currentScrollPosition) {\n    return optionOffset;\n  }\n\n  if (optionOffset + optionHeight > currentScrollPosition + panelHeight) {\n    return Math.max(0, optionOffset - panelHeight + optionHeight);\n  }\n\n  return currentScrollPosition;\n}\n", "<!-- Set aria-hidden=\"true\" to this DOM node and other decorative nodes in this file. This might\n be contributing to issue where sometimes VoiceOver focuses on a TextNode in the a11y tree instead\n of the Option node (#23202). Most assistive technology will generally ignore non-role,\n non-text-content elements. Adding aria-hidden seems to make VoiceOver behave more consistently. -->\n@if (multiple) {\n    <mat-pseudo-checkbox\n        class=\"mat-mdc-option-pseudo-checkbox\"\n        [disabled]=\"disabled\"\n        [state]=\"selected ? 'checked' : 'unchecked'\"\n        aria-hidden=\"true\"></mat-pseudo-checkbox>\n}\n\n<ng-content select=\"mat-icon\"></ng-content>\n\n<span class=\"mdc-list-item__primary-text\" #text><ng-content></ng-content></span>\n\n<!-- Render checkmark at the end for single-selection. -->\n@if (!multiple && selected && !hideSingleSelectionIndicator) {\n    <mat-pseudo-checkbox\n        class=\"mat-mdc-option-pseudo-checkbox\"\n        [disabled]=\"disabled\"\n        state=\"checked\"\n        aria-hidden=\"true\"\n        appearance=\"minimal\"></mat-pseudo-checkbox>\n}\n\n<!-- See a11y notes inside optgroup.ts for context behind this element. -->\n@if (group && group._inert) {\n    <span class=\"cdk-visually-hidden\">({{ group.label }})</span>\n}\n\n<div class=\"mat-mdc-option-ripple mat-focus-indicator\" aria-hidden=\"true\" mat-ripple\n     [matRippleTrigger]=\"_getHostElement()\" [matRippleDisabled]=\"disabled || disableRipple\">\n</div>\n"], "names": [], "mappings": ";;;;;;;;;;AAsBA;;AAEG;MACU,2BAA2B,GAAG,IAAI,cAAc,CAC3D,6BAA6B;;ACN/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;;;;AAIG;MACU,YAAY,GAAG,IAAI,cAAc,CAAc,aAAa;AAEzE;;AAEG;MAgBU,WAAW,CAAA;;AAEb,IAAA,KAAK;;IAGwB,QAAQ,GAAY,KAAK;;IAG/D,QAAQ,GAAW,MAAM,CAAC,YAAY,CAAC,CAAC,KAAK,CAAC,qBAAqB,CAAC;;AAGpE,IAAA,MAAM;AAIN,IAAA,WAAA,GAAA;AACE,QAAA,MAAM,MAAM,GAAG,MAAM,CAA2B,2BAA2B,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;QAC9F,IAAI,CAAC,MAAM,GAAG,MAAM,EAAE,WAAW,IAAI,KAAK;;uGAjBjC,WAAW,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAX,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,WAAW,EAKH,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,cAAA,EAAA,MAAA,EAAA,EAAA,KAAA,EAAA,OAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAAA,gBAAgB,CAPxB,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,WAAA,EAAA,2BAAA,EAAA,oBAAA,EAAA,qCAAA,EAAA,sBAAA,EAAA,0BAAA,EAAA,EAAA,cAAA,EAAA,kBAAA,EAAA,EAAA,SAAA,EAAA,CAAC,EAAC,OAAO,EAAE,YAAY,EAAE,WAAW,EAAE,WAAW,EAAC,CAAC,qDC/DhE,kTASA,EAAA,MAAA,EAAA,CAAA,29BAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FDwDa,WAAW,EAAA,UAAA,EAAA,CAAA;kBAfvB,SAAS;+BACE,cAAc,EAAA,QAAA,EACd,aAAa,EAAA,aAAA,EAER,iBAAiB,CAAC,IAAI,EACpB,eAAA,EAAA,uBAAuB,CAAC,MAAM,EAEzC,IAAA,EAAA;AACJ,wBAAA,OAAO,EAAE,kBAAkB;AAC3B,wBAAA,aAAa,EAAE,yBAAyB;AACxC,wBAAA,sBAAsB,EAAE,qCAAqC;AAC7D,wBAAA,wBAAwB,EAAE,0BAA0B;qBACrD,EACU,SAAA,EAAA,CAAC,EAAC,OAAO,EAAE,YAAY,EAAE,WAAW,EAAa,WAAA,EAAC,CAAC,EAAA,QAAA,EAAA,kTAAA,EAAA,MAAA,EAAA,CAAA,29BAAA,CAAA,EAAA;wDAIrD,KAAK,EAAA,CAAA;sBAAb;gBAGqC,QAAQ,EAAA,CAAA;sBAA7C,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC;;;AElCtC;MACa,wBAAwB,CAAA;AAG1B,IAAA,MAAA;AAEA,IAAA,WAAA;AAJT,IAAA,WAAA;;IAES,MAAoB;;AAEpB,IAAA,WAAA,GAAc,KAAK,EAAA;QAFnB,IAAM,CAAA,MAAA,GAAN,MAAM;QAEN,IAAW,CAAA,WAAA,GAAX,WAAW;;AAErB;AAED;;AAEG;MAgCU,SAAS,CAAA;AACZ,IAAA,QAAQ,GAAG,MAAM,CAA0B,UAAU,CAAC;AAC9D,IAAA,kBAAkB,GAAG,MAAM,CAAC,iBAAiB,CAAC;IACtC,OAAO,GAAG,MAAM,CAA2B,2BAA2B,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;IACjG,KAAK,GAAG,MAAM,CAAc,YAAY,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;IAEnD,oBAAoB,GAAG,KAAK;IAC5B,SAAS,GAAG,KAAK;IACjB,OAAO,GAAG,KAAK;IACf,SAAS,GAAG,KAAK;IACjB,oBAAoB,GAAG,EAAE;;AAGjC,IAAA,IAAI,QAAQ,GAAA;QACV,OAAO,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ;;;AAI9C,IAAA,IAAI,QAAQ,GAAA;QACV,OAAO,IAAI,CAAC,SAAS;;;AAId,IAAA,KAAK;;IAGL,EAAE,GAAW,MAAM,CAAC,YAAY,CAAC,CAAC,KAAK,CAAC,aAAa,CAAC;;AAG/D,IAAA,IACI,QAAQ,GAAA;AACV,QAAA,OAAO,CAAC,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,KAAK,IAAI,CAAC,SAAS;;IAE9D,IAAI,QAAQ,CAAC,KAAc,EAAA;AACzB,QAAA,IAAI,CAAC,SAAS,GAAG,KAAK;;;AAIxB,IAAA,IAAI,aAAa,GAAA;QACf,OAAO,IAAI,CAAC;AACV,cAAG,IAAI,CAAC,OAAQ,CAAC,aAAiC;cAChD,CAAC,CAAC,IAAI,CAAC,OAAO,EAAE,aAAa;;;AAInC,IAAA,IAAI,4BAA4B,GAAA;AAC9B,QAAA,OAAO,CAAC,EAAE,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,4BAA4B,CAAC;;;;AAKnD,IAAA,iBAAiB,GAAG,IAAI,YAAY,EAA+B;;AAGnD,IAAA,KAAK;;AAG/B,IAAA,aAAa,GAAG,IAAI,OAAO,EAAQ;AAG5C,IAAA,WAAA,GAAA;AACE,QAAA,MAAM,WAAW,GAAG,MAAM,CAAC,sBAAsB,CAAC;AAClD,QAAA,WAAW,CAAC,IAAI,CAAC,uBAAuB,CAAC;AACzC,QAAA,WAAW,CAAC,IAAI,CAAC,qBAAqB,CAAC;AACvC,QAAA,IAAI,CAAC,oBAAoB,GAAG,CAAC,CAAC,IAAI,CAAC,OAAO,IAAI,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC;;AAGpF;;;;;AAKG;AACH,IAAA,IAAI,MAAM,GAAA;QACR,OAAO,IAAI,CAAC,OAAO;;AAGrB;;;AAGG;AACH,IAAA,IAAI,SAAS,GAAA;;AAEX,QAAA,OAAO,CAAC,IAAI,CAAC,KAAK,EAAE,aAAa,CAAC,WAAW,IAAI,EAAE,EAAE,IAAI,EAAE;;;IAI7D,MAAM,CAAC,SAAS,GAAG,IAAI,EAAA;AACrB,QAAA,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;AACnB,YAAA,IAAI,CAAC,SAAS,GAAG,IAAI;AACrB,YAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE;YAEtC,IAAI,SAAS,EAAE;gBACb,IAAI,CAAC,yBAAyB,EAAE;;;;;IAMtC,QAAQ,CAAC,SAAS,GAAG,IAAI,EAAA;AACvB,QAAA,IAAI,IAAI,CAAC,SAAS,EAAE;AAClB,YAAA,IAAI,CAAC,SAAS,GAAG,KAAK;AACtB,YAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE;YAEtC,IAAI,SAAS,EAAE;gBACb,IAAI,CAAC,yBAAyB,EAAE;;;;;IAMtC,KAAK,CAAC,OAAqB,EAAE,OAAsB,EAAA;;;AAGjD,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,eAAe,EAAE;AAEtC,QAAA,IAAI,OAAO,OAAO,CAAC,KAAK,KAAK,UAAU,EAAE;AACvC,YAAA,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC;;;AAI1B;;;;AAIG;IACH,eAAe,GAAA;AACb,QAAA,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;AACjB,YAAA,IAAI,CAAC,OAAO,GAAG,IAAI;AACnB,YAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE;;;AAI1C;;;;AAIG;IACH,iBAAiB,GAAA;AACf,QAAA,IAAI,IAAI,CAAC,OAAO,EAAE;AAChB,YAAA,IAAI,CAAC,OAAO,GAAG,KAAK;AACpB,YAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE;;;;IAK1C,QAAQ,GAAA;QACN,OAAO,IAAI,CAAC,SAAS;;;AAIvB,IAAA,cAAc,CAAC,KAAoB,EAAA;QACjC,IAAI,CAAC,KAAK,CAAC,OAAO,KAAK,KAAK,IAAI,KAAK,CAAC,OAAO,KAAK,KAAK,KAAK,CAAC,cAAc,CAAC,KAAK,CAAC,EAAE;YAClF,IAAI,CAAC,qBAAqB,EAAE;;YAG5B,KAAK,CAAC,cAAc,EAAE;;;AAI1B;;;AAGG;IACH,qBAAqB,GAAA;AACnB,QAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;AAClB,YAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,QAAQ,GAAG,CAAC,IAAI,CAAC,SAAS,GAAG,IAAI;AACvD,YAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE;AACtC,YAAA,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC;;;;;;;IAQxC,YAAY,GAAA;QACV,OAAO,IAAI,CAAC,QAAQ,GAAG,IAAI,GAAG,GAAG;;;IAInC,eAAe,GAAA;AACb,QAAA,OAAO,IAAI,CAAC,QAAQ,CAAC,aAAa;;IAGpC,kBAAkB,GAAA;;;;;;AAMhB,QAAA,IAAI,IAAI,CAAC,SAAS,EAAE;AAClB,YAAA,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS;AAEhC,YAAA,IAAI,SAAS,KAAK,IAAI,CAAC,oBAAoB,EAAE;AAC3C,gBAAA,IAAI,IAAI,CAAC,oBAAoB,EAAE;AAC7B,oBAAA,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE;;AAG3B,gBAAA,IAAI,CAAC,oBAAoB,GAAG,SAAS;;;;IAK3C,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE;;;IAIvB,yBAAyB,CAAC,WAAW,GAAG,KAAK,EAAA;AACnD,QAAA,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,wBAAwB,CAAI,IAAI,EAAE,WAAW,CAAC,CAAC;;uGAlNtE,SAAS,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAT,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,SAAS,qHA6BD,gBAAgB,CAAA,EAAA,EAAA,OAAA,EAAA,EAAA,iBAAA,EAAA,mBAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,MAAA,EAAA,QAAA,EAAA,EAAA,SAAA,EAAA,EAAA,OAAA,EAAA,yBAAA,EAAA,SAAA,EAAA,wBAAA,EAAA,EAAA,UAAA,EAAA,EAAA,+BAAA,EAAA,UAAA,EAAA,+BAAA,EAAA,UAAA,EAAA,6BAAA,EAAA,QAAA,EAAA,+BAAA,EAAA,UAAA,EAAA,IAAA,EAAA,IAAA,EAAA,oBAAA,EAAA,UAAA,EAAA,oBAAA,EAAA,qBAAA,EAAA,EAAA,cAAA,EAAA,8BAAA,EAAA,EAAA,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,OAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,MAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,MAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,CAAA,WAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EC7GrC,w9CAkCA,ED4CY,MAAA,EAAA,CAAA,ivHAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,iBAAiB,6GAAE,SAAS,EAAA,QAAA,EAAA,2BAAA,EAAA,MAAA,EAAA,CAAA,gBAAA,EAAA,oBAAA,EAAA,mBAAA,EAAA,iBAAA,EAAA,oBAAA,EAAA,mBAAA,EAAA,kBAAA,CAAA,EAAA,QAAA,EAAA,CAAA,WAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FAE3B,SAAS,EAAA,UAAA,EAAA,CAAA;kBA/BrB,SAAS;+BACE,YAAY,EAAA,QAAA,EACZ,WAAW,EACf,IAAA,EAAA;AACJ,wBAAA,MAAM,EAAE,QAAQ;AAChB,wBAAA,iCAAiC,EAAE,UAAU;AAC7C,wBAAA,iCAAiC,EAAE,UAAU;AAC7C,wBAAA,+BAA+B,EAAE,QAAQ;AACzC,wBAAA,iCAAiC,EAAE,UAAU;AAC7C,wBAAA,MAAM,EAAE,IAAI;;;;;;;;;;AAUZ,wBAAA,sBAAsB,EAAE,UAAU;AAClC,wBAAA,sBAAsB,EAAE,qBAAqB;AAC7C,wBAAA,SAAS,EAAE,yBAAyB;AACpC,wBAAA,WAAW,EAAE,wBAAwB;AACrC,wBAAA,OAAO,EAAE,8BAA8B;AACxC,qBAAA,EAAA,aAAA,EAGc,iBAAiB,CAAC,IAAI,EAAA,eAAA,EACpB,uBAAuB,CAAC,MAAM,EAAA,OAAA,EACtC,CAAC,iBAAiB,EAAE,SAAS,CAAC,EAAA,QAAA,EAAA,w9CAAA,EAAA,MAAA,EAAA,CAAA,ivHAAA,CAAA,EAAA;wDAyB9B,KAAK,EAAA,CAAA;sBAAb;gBAGQ,EAAE,EAAA,CAAA;sBAAV;gBAIG,QAAQ,EAAA,CAAA;sBADX,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC;gBAsBjB,iBAAiB,EAAA,CAAA;sBAAnC;gBAGkC,KAAK,EAAA,CAAA;sBAAvC,SAAS;AAAC,gBAAA,IAAA,EAAA,CAAA,MAAM,EAAE,EAAC,MAAM,EAAE,IAAI,EAAC;;AAgKnC;;;;;;AAMG;SACa,6BAA6B,CAC3C,WAAmB,EACnB,OAA6B,EAC7B,YAAoC,EAAA;AAEpC,IAAA,IAAI,YAAY,CAAC,MAAM,EAAE;AACvB,QAAA,IAAI,YAAY,GAAG,OAAO,CAAC,OAAO,EAAE;AACpC,QAAA,IAAI,MAAM,GAAG,YAAY,CAAC,OAAO,EAAE;QACnC,IAAI,YAAY,GAAG,CAAC;AAEpB,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;AACxC,YAAA,IAAI,YAAY,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI,YAAY,CAAC,CAAC,CAAC,CAAC,KAAK,KAAK,MAAM,CAAC,YAAY,CAAC,EAAE;AAC3E,gBAAA,YAAY,EAAE;;;AAIlB,QAAA,OAAO,YAAY;;AAGrB,IAAA,OAAO,CAAC;AACV;AAEA;;;;;;;AAOG;AACG,SAAU,wBAAwB,CACtC,YAAoB,EACpB,YAAoB,EACpB,qBAA6B,EAC7B,WAAmB,EAAA;AAEnB,IAAA,IAAI,YAAY,GAAG,qBAAqB,EAAE;AACxC,QAAA,OAAO,YAAY;;IAGrB,IAAI,YAAY,GAAG,YAAY,GAAG,qBAAqB,GAAG,WAAW,EAAE;AACrE,QAAA,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,YAAY,GAAG,WAAW,GAAG,YAAY,CAAC;;AAG/D,IAAA,OAAO,qBAAqB;AAC9B;;;;"}