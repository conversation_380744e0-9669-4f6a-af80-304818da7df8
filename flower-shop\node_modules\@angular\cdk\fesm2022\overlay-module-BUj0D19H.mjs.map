{"version": 3, "file": "overlay-module-BUj0D19H.mjs", "sources": ["../../../../../k8-fastbuild-ST-46c76129e412/bin/src/cdk/overlay/scroll/block-scroll-strategy.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/cdk/overlay/scroll/scroll-strategy.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/cdk/overlay/scroll/close-scroll-strategy.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/cdk/overlay/scroll/noop-scroll-strategy.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/cdk/overlay/position/scroll-clip.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/cdk/overlay/scroll/reposition-scroll-strategy.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/cdk/overlay/scroll/scroll-strategy-options.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/cdk/overlay/overlay-config.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/cdk/overlay/position/connected-position.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/cdk/overlay/dispatchers/base-overlay-dispatcher.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/cdk/overlay/dispatchers/overlay-keyboard-dispatcher.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/cdk/overlay/dispatchers/overlay-outside-click-dispatcher.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/cdk/overlay/overlay-container.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/cdk/overlay/backdrop-ref.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/cdk/overlay/overlay-ref.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/cdk/overlay/position/flexible-connected-position-strategy.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/cdk/overlay/position/global-position-strategy.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/cdk/overlay/position/overlay-position-builder.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/cdk/overlay/overlay.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/cdk/overlay/overlay-directives.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/cdk/overlay/overlay-module.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {ScrollStrategy} from './scroll-strategy';\nimport {ViewportRuler} from '../../scrolling';\nimport {coerceCssPixelValue} from '../../coercion';\nimport {supportsScrollBehavior} from '../../platform';\n\nconst scrollBehaviorSupported = supportsScrollBehavior();\n\n/**\n * Strategy that will prevent the user from scrolling while the overlay is visible.\n */\nexport class BlockScrollStrategy implements ScrollStrategy {\n  private _previousHTMLStyles = {top: '', left: ''};\n  private _previousScrollPosition: {top: number; left: number};\n  private _isEnabled = false;\n  private _document: Document;\n\n  constructor(\n    private _viewportRuler: ViewportRuler,\n    document: any,\n  ) {\n    this._document = document;\n  }\n\n  /** Attaches this scroll strategy to an overlay. */\n  attach() {}\n\n  /** Blocks page-level scroll while the attached overlay is open. */\n  enable() {\n    if (this._canBeEnabled()) {\n      const root = this._document.documentElement!;\n\n      this._previousScrollPosition = this._viewportRuler.getViewportScrollPosition();\n\n      // Cache the previous inline styles in case the user had set them.\n      this._previousHTMLStyles.left = root.style.left || '';\n      this._previousHTMLStyles.top = root.style.top || '';\n\n      // Note: we're using the `html` node, instead of the `body`, because the `body` may\n      // have the user agent margin, whereas the `html` is guaranteed not to have one.\n      root.style.left = coerceCssPixelValue(-this._previousScrollPosition.left);\n      root.style.top = coerceCssPixelValue(-this._previousScrollPosition.top);\n      root.classList.add('cdk-global-scrollblock');\n      this._isEnabled = true;\n    }\n  }\n\n  /** Unblocks page-level scroll while the attached overlay is open. */\n  disable() {\n    if (this._isEnabled) {\n      const html = this._document.documentElement!;\n      const body = this._document.body!;\n      const htmlStyle = html.style;\n      const bodyStyle = body.style;\n      const previousHtmlScrollBehavior = htmlStyle.scrollBehavior || '';\n      const previousBodyScrollBehavior = bodyStyle.scrollBehavior || '';\n\n      this._isEnabled = false;\n\n      htmlStyle.left = this._previousHTMLStyles.left;\n      htmlStyle.top = this._previousHTMLStyles.top;\n      html.classList.remove('cdk-global-scrollblock');\n\n      // Disable user-defined smooth scrolling temporarily while we restore the scroll position.\n      // See https://developer.mozilla.org/en-US/docs/Web/CSS/scroll-behavior\n      // Note that we don't mutate the property if the browser doesn't support `scroll-behavior`,\n      // because it can throw off feature detections in `supportsScrollBehavior` which\n      // checks for `'scrollBehavior' in documentElement.style`.\n      if (scrollBehaviorSupported) {\n        htmlStyle.scrollBehavior = bodyStyle.scrollBehavior = 'auto';\n      }\n\n      window.scroll(this._previousScrollPosition.left, this._previousScrollPosition.top);\n\n      if (scrollBehaviorSupported) {\n        htmlStyle.scrollBehavior = previousHtmlScrollBehavior;\n        bodyStyle.scrollBehavior = previousBodyScrollBehavior;\n      }\n    }\n  }\n\n  private _canBeEnabled(): boolean {\n    // Since the scroll strategies can't be singletons, we have to use a global CSS class\n    // (`cdk-global-scrollblock`) to make sure that we don't try to disable global\n    // scrolling multiple times.\n    const html = this._document.documentElement!;\n\n    if (html.classList.contains('cdk-global-scrollblock') || this._isEnabled) {\n      return false;\n    }\n\n    const rootElement = this._document.documentElement;\n    const viewport = this._viewportRuler.getViewportSize();\n    return rootElement.scrollHeight > viewport.height || rootElement.scrollWidth > viewport.width;\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport type {OverlayRef} from '../overlay-ref';\n\n/**\n * Describes a strategy that will be used by an overlay to handle scroll events while it is open.\n */\nexport interface ScrollStrategy {\n  /** Enable this scroll strategy (called when the attached overlay is attached to a portal). */\n  enable: () => void;\n\n  /** Disable this scroll strategy (called when the attached overlay is detached from a portal). */\n  disable: () => void;\n\n  /** Attaches this `ScrollStrategy` to an overlay. */\n  attach: (overlayRef: OverlayRef) => void;\n\n  /** Detaches the scroll strategy from the current overlay. */\n  detach?: () => void;\n}\n\n/**\n * Returns an error to be thrown when attempting to attach an already-attached scroll strategy.\n */\nexport function getMatScrollStrategyAlreadyAttachedError(): Error {\n  return Error(`Scroll strategy has already been attached.`);\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\nimport {NgZone} from '@angular/core';\nimport {ScrollStrategy, getMatScrollStrategyAlreadyAttachedError} from './scroll-strategy';\nimport {Subscription} from 'rxjs';\nimport {ScrollDispatcher, ViewportRuler} from '../../scrolling';\nimport {filter} from 'rxjs/operators';\nimport type {OverlayRef} from '../overlay-ref';\n\n/**\n * Config options for the CloseScrollStrategy.\n */\nexport interface CloseScrollStrategyConfig {\n  /** Amount of pixels the user has to scroll before the overlay is closed. */\n  threshold?: number;\n}\n\n/**\n * Strategy that will close the overlay as soon as the user starts scrolling.\n */\nexport class CloseScrollStrategy implements ScrollStrategy {\n  private _scrollSubscription: Subscription | null = null;\n  private _overlayRef: OverlayRef;\n  private _initialScrollPosition: number;\n\n  constructor(\n    private _scrollDispatcher: ScrollDispatcher,\n    private _ngZone: NgZone,\n    private _viewportRuler: ViewportRuler,\n    private _config?: CloseScrollStrategyConfig,\n  ) {}\n\n  /** Attaches this scroll strategy to an overlay. */\n  attach(overlayRef: OverlayRef) {\n    if (this._overlayRef && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw getMatScrollStrategyAlreadyAttachedError();\n    }\n\n    this._overlayRef = overlayRef;\n  }\n\n  /** Enables the closing of the attached overlay on scroll. */\n  enable() {\n    if (this._scrollSubscription) {\n      return;\n    }\n\n    const stream = this._scrollDispatcher.scrolled(0).pipe(\n      filter(scrollable => {\n        return (\n          !scrollable ||\n          !this._overlayRef.overlayElement.contains(scrollable.getElementRef().nativeElement)\n        );\n      }),\n    );\n\n    if (this._config && this._config.threshold && this._config.threshold > 1) {\n      this._initialScrollPosition = this._viewportRuler.getViewportScrollPosition().top;\n\n      this._scrollSubscription = stream.subscribe(() => {\n        const scrollPosition = this._viewportRuler.getViewportScrollPosition().top;\n\n        if (Math.abs(scrollPosition - this._initialScrollPosition) > this._config!.threshold!) {\n          this._detach();\n        } else {\n          this._overlayRef.updatePosition();\n        }\n      });\n    } else {\n      this._scrollSubscription = stream.subscribe(this._detach);\n    }\n  }\n\n  /** Disables the closing the attached overlay on scroll. */\n  disable() {\n    if (this._scrollSubscription) {\n      this._scrollSubscription.unsubscribe();\n      this._scrollSubscription = null;\n    }\n  }\n\n  detach() {\n    this.disable();\n    this._overlayRef = null!;\n  }\n\n  /** Detaches the overlay ref and disables the scroll strategy. */\n  private _detach = () => {\n    this.disable();\n\n    if (this._overlayRef.hasAttached()) {\n      this._ngZone.run(() => this._overlayRef.detach());\n    }\n  };\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {ScrollStrategy} from './scroll-strategy';\n\n/** Scroll strategy that doesn't do anything. */\nexport class NoopScrollStrategy implements ScrollStrategy {\n  /** Does nothing, as this scroll strategy is a no-op. */\n  enable() {}\n  /** Does nothing, as this scroll strategy is a no-op. */\n  disable() {}\n  /** Does nothing, as this scroll strategy is a no-op. */\n  attach() {}\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n// TODO(jelbourn): move this to live with the rest of the scrolling code\n// TODO(jelbourn): someday replace this with IntersectionObservers\n\n/** Equivalent of `DOMRect` without some of the properties we don't care about. */\ntype Dimensions = Omit<DOMRect, 'x' | 'y' | 'toJSON'>;\n\n/**\n * Gets whether an element is scrolled outside of view by any of its parent scrolling containers.\n * @param element Dimensions of the element (from getBoundingClientRect)\n * @param scrollContainers Dimensions of element's scrolling containers (from getBoundingClientRect)\n * @returns Whether the element is scrolled out of view\n * @docs-private\n */\nexport function isElementScrolledOutsideView(element: Dimensions, scrollContainers: Dimensions[]) {\n  return scrollContainers.some(containerBounds => {\n    const outsideAbove = element.bottom < containerBounds.top;\n    const outsideBelow = element.top > containerBounds.bottom;\n    const outsideLeft = element.right < containerBounds.left;\n    const outsideRight = element.left > containerBounds.right;\n\n    return outsideAbove || outsideBelow || outsideLeft || outsideRight;\n  });\n}\n\n/**\n * Gets whether an element is clipped by any of its scrolling containers.\n * @param element Dimensions of the element (from getBoundingClientRect)\n * @param scrollContainers Dimensions of element's scrolling containers (from getBoundingClientRect)\n * @returns Whether the element is clipped\n * @docs-private\n */\nexport function isElementClippedByScrolling(element: Dimensions, scrollContainers: Dimensions[]) {\n  return scrollContainers.some(scrollContainerRect => {\n    const clippedAbove = element.top < scrollContainerRect.top;\n    const clippedBelow = element.bottom > scrollContainerRect.bottom;\n    const clippedLeft = element.left < scrollContainerRect.left;\n    const clippedRight = element.right > scrollContainerRect.right;\n\n    return clippedAbove || clippedBelow || clippedLeft || clippedRight;\n  });\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {NgZone} from '@angular/core';\nimport {Subscription} from 'rxjs';\nimport {ScrollStrategy, getMatScrollStrategyAlreadyAttachedError} from './scroll-strategy';\nimport {ScrollDispatcher, ViewportRuler} from '../../scrolling';\nimport {isElementScrolledOutsideView} from '../position/scroll-clip';\nimport type {OverlayRef} from '../overlay-ref';\n\n/**\n * Config options for the RepositionScrollStrategy.\n */\nexport interface RepositionScrollStrategyConfig {\n  /** Time in milliseconds to throttle the scroll events. */\n  scrollThrottle?: number;\n\n  /** Whether to close the overlay once the user has scrolled away completely. */\n  autoClose?: boolean;\n}\n\n/**\n * Strategy that will update the element position as the user is scrolling.\n */\nexport class RepositionScrollStrategy implements ScrollStrategy {\n  private _scrollSubscription: Subscription | null = null;\n  private _overlayRef: OverlayRef;\n\n  constructor(\n    private _scrollDispatcher: ScrollDispatcher,\n    private _viewportRuler: ViewportRuler,\n    private _ngZone: NgZone,\n    private _config?: RepositionScrollStrategyConfig,\n  ) {}\n\n  /** Attaches this scroll strategy to an overlay. */\n  attach(overlayRef: OverlayRef) {\n    if (this._overlayRef && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw getMatScrollStrategyAlreadyAttachedError();\n    }\n\n    this._overlayRef = overlayRef;\n  }\n\n  /** Enables repositioning of the attached overlay on scroll. */\n  enable() {\n    if (!this._scrollSubscription) {\n      const throttle = this._config ? this._config.scrollThrottle : 0;\n\n      this._scrollSubscription = this._scrollDispatcher.scrolled(throttle).subscribe(() => {\n        this._overlayRef.updatePosition();\n\n        // TODO(crisbeto): make `close` on by default once all components can handle it.\n        if (this._config && this._config.autoClose) {\n          const overlayRect = this._overlayRef.overlayElement.getBoundingClientRect();\n          const {width, height} = this._viewportRuler.getViewportSize();\n\n          // TODO(crisbeto): include all ancestor scroll containers here once\n          // we have a way of exposing the trigger element to the scroll strategy.\n          const parentRects = [{width, height, bottom: height, right: width, top: 0, left: 0}];\n\n          if (isElementScrolledOutsideView(overlayRect, parentRects)) {\n            this.disable();\n            this._ngZone.run(() => this._overlayRef.detach());\n          }\n        }\n      });\n    }\n  }\n\n  /** Disables repositioning of the attached overlay on scroll. */\n  disable() {\n    if (this._scrollSubscription) {\n      this._scrollSubscription.unsubscribe();\n      this._scrollSubscription = null;\n    }\n  }\n\n  detach() {\n    this.disable();\n    this._overlayRef = null!;\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {ScrollDispatcher, ViewportRuler} from '../../scrolling';\nimport {DOCUMENT} from '@angular/common';\nimport {Injectable, NgZone, inject} from '@angular/core';\nimport {BlockScrollStrategy} from './block-scroll-strategy';\nimport {CloseScrollStrategy, CloseScrollStrategyConfig} from './close-scroll-strategy';\nimport {NoopScrollStrategy} from './noop-scroll-strategy';\nimport {\n  RepositionScrollStrategy,\n  RepositionScrollStrategyConfig,\n} from './reposition-scroll-strategy';\n\n/**\n * Options for how an overlay will handle scrolling.\n *\n * Users can provide a custom value for `ScrollStrategyOptions` to replace the default\n * behaviors. This class primarily acts as a factory for ScrollStrategy instances.\n */\n@Injectable({providedIn: 'root'})\nexport class ScrollStrategyOptions {\n  private _scrollDispatcher = inject(ScrollDispatcher);\n  private _viewportRuler = inject(ViewportRuler);\n  private _ngZone = inject(NgZone);\n\n  private _document = inject(DOCUMENT);\n\n  constructor(...args: unknown[]);\n  constructor() {}\n\n  /** Do nothing on scroll. */\n  noop = () => new NoopScrollStrategy();\n\n  /**\n   * Close the overlay as soon as the user scrolls.\n   * @param config Configuration to be used inside the scroll strategy.\n   */\n  close = (config?: CloseScrollStrategyConfig) =>\n    new CloseScrollStrategy(this._scrollDispatcher, this._ngZone, this._viewportRuler, config);\n\n  /** Block scrolling. */\n  block = () => new BlockScrollStrategy(this._viewportRuler, this._document);\n\n  /**\n   * Update the overlay's position on scroll.\n   * @param config Configuration to be used inside the scroll strategy.\n   * Allows debouncing the reposition calls.\n   */\n  reposition = (config?: RepositionScrollStrategyConfig) =>\n    new RepositionScrollStrategy(this._scrollDispatcher, this._viewportRuler, this._ngZone, config);\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {PositionStrategy} from './position/position-strategy';\nimport {Direction, Directionality} from '../bidi';\nimport {ScrollStrategy, NoopScrollStrategy} from './scroll/index';\n\n/** Initial configuration used when creating an overlay. */\nexport class OverlayConfig {\n  /** Strategy with which to position the overlay. */\n  positionStrategy?: PositionStrategy;\n\n  /** Strategy to be used when handling scroll events while the overlay is open. */\n  scrollStrategy?: ScrollStrategy = new NoopScrollStrategy();\n\n  /** Custom class to add to the overlay pane. */\n  panelClass?: string | string[] = '';\n\n  /** Whether the overlay has a backdrop. */\n  hasBackdrop?: boolean = false;\n\n  /** Custom class to add to the backdrop */\n  backdropClass?: string | string[] = 'cdk-overlay-dark-backdrop';\n\n  /** The width of the overlay panel. If a number is provided, pixel units are assumed. */\n  width?: number | string;\n\n  /** The height of the overlay panel. If a number is provided, pixel units are assumed. */\n  height?: number | string;\n\n  /** The min-width of the overlay panel. If a number is provided, pixel units are assumed. */\n  minWidth?: number | string;\n\n  /** The min-height of the overlay panel. If a number is provided, pixel units are assumed. */\n  minHeight?: number | string;\n\n  /** The max-width of the overlay panel. If a number is provided, pixel units are assumed. */\n  maxWidth?: number | string;\n\n  /** The max-height of the overlay panel. If a number is provided, pixel units are assumed. */\n  maxHeight?: number | string;\n\n  /**\n   * Direction of the text in the overlay panel. If a `Directionality` instance\n   * is passed in, the overlay will handle changes to its value automatically.\n   */\n  direction?: Direction | Directionality;\n\n  /**\n   * Whether the overlay should be disposed of when the user goes backwards/forwards in history.\n   * Note that this usually doesn't include clicking on links (unless the user is using\n   * the `HashLocationStrategy`).\n   */\n  disposeOnNavigation?: boolean = false;\n\n  constructor(config?: OverlayConfig) {\n    if (config) {\n      // Use `Iterable` instead of `Array` because TypeScript, as of 3.6.3,\n      // loses the array generic type in the `for of`. But we *also* have to use `Array` because\n      // typescript won't iterate over an `Iterable` unless you compile with `--downlevelIteration`\n      const configKeys = Object.keys(config) as Iterable<keyof OverlayConfig> &\n        (keyof OverlayConfig)[];\n      for (const key of configKeys) {\n        if (config[key] !== undefined) {\n          // TypeScript, as of version 3.5, sees the left-hand-side of this expression\n          // as \"I don't know *which* key this is, so the only valid value is the intersection\n          // of all the possible values.\" In this case, that happens to be `undefined`. TypeScript\n          // is not smart enough to see that the right-hand-side is actually an access of the same\n          // exact type with the same exact key, meaning that the value type must be identical.\n          // So we use `any` to work around this.\n          this[key] = config[key] as any;\n        }\n      }\n    }\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n/** Horizontal dimension of a connection point on the perimeter of the origin or overlay element. */\nexport type HorizontalConnectionPos = 'start' | 'center' | 'end';\n\n/** Vertical dimension of a connection point on the perimeter of the origin or overlay element. */\nexport type VerticalConnectionPos = 'top' | 'center' | 'bottom';\n\n/** A connection point on the origin element. */\nexport interface OriginConnectionPosition {\n  originX: HorizontalConnectionPos;\n  originY: VerticalConnectionPos;\n}\n\n/** A connection point on the overlay element. */\nexport interface OverlayConnectionPosition {\n  overlayX: HorizontalConnectionPos;\n  overlayY: VerticalConnectionPos;\n}\n\n/** The points of the origin element and the overlay element to connect. */\nexport class ConnectionPositionPair {\n  /** X-axis attachment point for connected overlay origin. Can be 'start', 'end', or 'center'. */\n  originX: HorizontalConnectionPos;\n  /** Y-axis attachment point for connected overlay origin. Can be 'top', 'bottom', or 'center'. */\n  originY: VerticalConnectionPos;\n  /** X-axis attachment point for connected overlay. Can be 'start', 'end', or 'center'. */\n  overlayX: HorizontalConnectionPos;\n  /** Y-axis attachment point for connected overlay. Can be 'top', 'bottom', or 'center'. */\n  overlayY: VerticalConnectionPos;\n\n  constructor(\n    origin: OriginConnectionPosition,\n    overlay: OverlayConnectionPosition,\n    /** Offset along the X axis. */\n    public offsetX?: number,\n    /** Offset along the Y axis. */\n    public offsetY?: number,\n    /** Class(es) to be applied to the panel while this position is active. */\n    public panelClass?: string | string[],\n  ) {\n    this.originX = origin.originX;\n    this.originY = origin.originY;\n    this.overlayX = overlay.overlayX;\n    this.overlayY = overlay.overlayY;\n  }\n}\n\n/**\n * Set of properties regarding the position of the origin and overlay relative to the viewport\n * with respect to the containing Scrollable elements.\n *\n * The overlay and origin are clipped if any part of their bounding client rectangle exceeds the\n * bounds of any one of the strategy's Scrollable's bounding client rectangle.\n *\n * The overlay and origin are outside view if there is no overlap between their bounding client\n * rectangle and any one of the strategy's Scrollable's bounding client rectangle.\n *\n *       -----------                    -----------\n *       | outside |                    | clipped |\n *       |  view   |              --------------------------\n *       |         |              |     |         |        |\n *       ----------               |     -----------        |\n *  --------------------------    |                        |\n *  |                        |    |      Scrollable        |\n *  |                        |    |                        |\n *  |                        |     --------------------------\n *  |      Scrollable        |\n *  |                        |\n *  --------------------------\n *\n *  @docs-private\n */\nexport class ScrollingVisibility {\n  isOriginClipped: boolean;\n  isOriginOutsideView: boolean;\n  isOverlayClipped: boolean;\n  isOverlayOutsideView: boolean;\n}\n\n/** The change event emitted by the strategy when a fallback position is used. */\nexport class ConnectedOverlayPositionChange {\n  constructor(\n    /** The position used as a result of this change. */\n    public connectionPair: ConnectionPositionPair,\n    /** @docs-private */\n    public scrollableViewProperties: ScrollingVisibility,\n  ) {}\n}\n\n/**\n * Validates whether a vertical position property matches the expected values.\n * @param property Name of the property being validated.\n * @param value Value of the property being validated.\n * @docs-private\n */\nexport function validateVerticalPosition(property: string, value: VerticalConnectionPos) {\n  if (value !== 'top' && value !== 'bottom' && value !== 'center') {\n    throw Error(\n      `ConnectedPosition: Invalid ${property} \"${value}\". ` +\n        `Expected \"top\", \"bottom\" or \"center\".`,\n    );\n  }\n}\n\n/**\n * Validates whether a horizontal position property matches the expected values.\n * @param property Name of the property being validated.\n * @param value Value of the property being validated.\n * @docs-private\n */\nexport function validateHorizontalPosition(property: string, value: HorizontalConnectionPos) {\n  if (value !== 'start' && value !== 'end' && value !== 'center') {\n    throw Error(\n      `ConnectedPosition: Invalid ${property} \"${value}\". ` +\n        `Expected \"start\", \"end\" or \"center\".`,\n    );\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {DOCUMENT} from '@angular/common';\nimport {Injectable, OnDestroy, inject} from '@angular/core';\nimport type {OverlayRef} from '../overlay-ref';\n\n/**\n * Service for dispatching events that land on the body to appropriate overlay ref,\n * if any. It maintains a list of attached overlays to determine best suited overlay based\n * on event target and order of overlay opens.\n */\n@Injectable({providedIn: 'root'})\nexport abstract class BaseOverlayDispatcher implements OnDestroy {\n  /** Currently attached overlays in the order they were attached. */\n  _attachedOverlays: OverlayRef[] = [];\n\n  protected _document = inject(DOCUMENT);\n  protected _isAttached: boolean;\n\n  constructor(...args: unknown[]);\n\n  constructor() {}\n\n  ngOnDestroy(): void {\n    this.detach();\n  }\n\n  /** Add a new overlay to the list of attached overlay refs. */\n  add(overlayRef: OverlayRef): void {\n    // Ensure that we don't get the same overlay multiple times.\n    this.remove(overlayRef);\n    this._attachedOverlays.push(overlayRef);\n  }\n\n  /** Remove an overlay from the list of attached overlay refs. */\n  remove(overlayRef: OverlayRef): void {\n    const index = this._attachedOverlays.indexOf(overlayRef);\n\n    if (index > -1) {\n      this._attachedOverlays.splice(index, 1);\n    }\n\n    // Remove the global listener once there are no more overlays.\n    if (this._attachedOverlays.length === 0) {\n      this.detach();\n    }\n  }\n\n  /** Detaches the global event listener. */\n  protected abstract detach(): void;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {Injectable, NgZone, RendererFactory2, inject} from '@angular/core';\nimport {BaseOverlayDispatcher} from './base-overlay-dispatcher';\nimport type {OverlayRef} from '../overlay-ref';\n\n/**\n * Service for dispatching keyboard events that land on the body to appropriate overlay ref,\n * if any. It maintains a list of attached overlays to determine best suited overlay based\n * on event target and order of overlay opens.\n */\n@Injectable({providedIn: 'root'})\nexport class OverlayKeyboardDispatcher extends BaseOverlayDispatcher {\n  private _ngZone = inject(NgZone);\n  private _renderer = inject(RendererFactory2).createRenderer(null, null);\n  private _cleanupKeydown: (() => void) | undefined;\n\n  /** Add a new overlay to the list of attached overlay refs. */\n  override add(overlayRef: OverlayRef): void {\n    super.add(overlayRef);\n\n    // Lazily start dispatcher once first overlay is added\n    if (!this._isAttached) {\n      this._ngZone.runOutsideAngular(() => {\n        this._cleanupKeydown = this._renderer.listen('body', 'keydown', this._keydownListener);\n      });\n\n      this._isAttached = true;\n    }\n  }\n\n  /** Detaches the global keyboard event listener. */\n  protected detach() {\n    if (this._isAttached) {\n      this._cleanupKeydown?.();\n      this._isAttached = false;\n    }\n  }\n\n  /** Keyboard event listener that will be attached to the body. */\n  private _keydownListener = (event: KeyboardEvent) => {\n    const overlays = this._attachedOverlays;\n\n    for (let i = overlays.length - 1; i > -1; i--) {\n      // Dispatch the keydown event to the top overlay which has subscribers to its keydown events.\n      // We want to target the most recent overlay, rather than trying to match where the event came\n      // from, because some components might open an overlay, but keep focus on a trigger element\n      // (e.g. for select and autocomplete). We skip overlays without keydown event subscriptions,\n      // because we don't want overlays that don't handle keyboard events to block the ones below\n      // them that do.\n      if (overlays[i]._keydownEvents.observers.length > 0) {\n        this._ngZone.run(() => overlays[i]._keydownEvents.next(event));\n        break;\n      }\n    }\n  };\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {Injectable, NgZone, RendererFactory2, inject} from '@angular/core';\nimport {Platform, _bindEventWithOptions, _getEventTarget} from '../../platform';\nimport {BaseOverlayDispatcher} from './base-overlay-dispatcher';\nimport type {OverlayRef} from '../overlay-ref';\n\n/**\n * Service for dispatching mouse click events that land on the body to appropriate overlay ref,\n * if any. It maintains a list of attached overlays to determine best suited overlay based\n * on event target and order of overlay opens.\n */\n@Injectable({providedIn: 'root'})\nexport class OverlayOutsideClickDispatcher extends BaseOverlayDispatcher {\n  private _platform = inject(Platform);\n  private _ngZone = inject(NgZone);\n  private _renderer = inject(RendererFactory2).createRenderer(null, null);\n\n  private _cursorOriginalValue: string;\n  private _cursorStyleIsSet = false;\n  private _pointerDownEventTarget: HTMLElement | null;\n  private _cleanups: (() => void)[] | undefined;\n\n  /** Add a new overlay to the list of attached overlay refs. */\n  override add(overlayRef: OverlayRef): void {\n    super.add(overlayRef);\n\n    // Safari on iOS does not generate click events for non-interactive\n    // elements. However, we want to receive a click for any element outside\n    // the overlay. We can force a \"clickable\" state by setting\n    // `cursor: pointer` on the document body. See:\n    // https://developer.mozilla.org/en-US/docs/Web/API/Element/click_event#Safari_Mobile\n    // https://developer.apple.com/library/archive/documentation/AppleApplications/Reference/SafariWebContent/HandlingEvents/HandlingEvents.html\n    if (!this._isAttached) {\n      const body = this._document.body;\n      const eventOptions = {capture: true};\n\n      this._cleanups = this._ngZone.runOutsideAngular(() => [\n        _bindEventWithOptions(\n          this._renderer,\n          body,\n          'pointerdown',\n          this._pointerDownListener,\n          eventOptions,\n        ),\n        _bindEventWithOptions(this._renderer, body, 'click', this._clickListener, eventOptions),\n        _bindEventWithOptions(this._renderer, body, 'auxclick', this._clickListener, eventOptions),\n        _bindEventWithOptions(\n          this._renderer,\n          body,\n          'contextmenu',\n          this._clickListener,\n          eventOptions,\n        ),\n      ]);\n\n      // click event is not fired on iOS. To make element \"clickable\" we are\n      // setting the cursor to pointer\n      if (this._platform.IOS && !this._cursorStyleIsSet) {\n        this._cursorOriginalValue = body.style.cursor;\n        body.style.cursor = 'pointer';\n        this._cursorStyleIsSet = true;\n      }\n\n      this._isAttached = true;\n    }\n  }\n\n  /** Detaches the global keyboard event listener. */\n  protected detach() {\n    if (this._isAttached) {\n      this._cleanups?.forEach(cleanup => cleanup());\n      this._cleanups = undefined;\n      if (this._platform.IOS && this._cursorStyleIsSet) {\n        this._document.body.style.cursor = this._cursorOriginalValue;\n        this._cursorStyleIsSet = false;\n      }\n      this._isAttached = false;\n    }\n  }\n\n  /** Store pointerdown event target to track origin of click. */\n  private _pointerDownListener = (event: PointerEvent) => {\n    this._pointerDownEventTarget = _getEventTarget<HTMLElement>(event);\n  };\n\n  /** Click event listener that will be attached to the body propagate phase. */\n  private _clickListener = (event: MouseEvent) => {\n    const target = _getEventTarget<HTMLElement>(event);\n    // In case of a click event, we want to check the origin of the click\n    // (e.g. in case where a user starts a click inside the overlay and\n    // releases the click outside of it).\n    // This is done by using the event target of the preceding pointerdown event.\n    // Every click event caused by a pointer device has a preceding pointerdown\n    // event, unless the click was programmatically triggered (e.g. in a unit test).\n    const origin =\n      event.type === 'click' && this._pointerDownEventTarget\n        ? this._pointerDownEventTarget\n        : target;\n    // Reset the stored pointerdown event target, to avoid having it interfere\n    // in subsequent events.\n    this._pointerDownEventTarget = null;\n\n    // We copy the array because the original may be modified asynchronously if the\n    // outsidePointerEvents listener decides to detach overlays resulting in index errors inside\n    // the for loop.\n    const overlays = this._attachedOverlays.slice();\n\n    // Dispatch the mouse event to the top overlay which has subscribers to its mouse events.\n    // We want to target all overlays for which the click could be considered as outside click.\n    // As soon as we reach an overlay for which the click is not outside click we break off\n    // the loop.\n    for (let i = overlays.length - 1; i > -1; i--) {\n      const overlayRef = overlays[i];\n      if (overlayRef._outsidePointerEvents.observers.length < 1 || !overlayRef.hasAttached()) {\n        continue;\n      }\n\n      // If it's a click inside the overlay, just break - we should do nothing\n      // If it's an outside click (both origin and target of the click) dispatch the mouse event,\n      // and proceed with the next overlay\n      if (\n        containsPierceShadowDom(overlayRef.overlayElement, target) ||\n        containsPierceShadowDom(overlayRef.overlayElement, origin)\n      ) {\n        break;\n      }\n\n      const outsidePointerEvents = overlayRef._outsidePointerEvents;\n      /** @breaking-change 14.0.0 _ngZone will be required. */\n      if (this._ngZone) {\n        this._ngZone.run(() => outsidePointerEvents.next(event));\n      } else {\n        outsidePointerEvents.next(event);\n      }\n    }\n  };\n}\n\n/** Version of `Element.contains` that transcends shadow DOM boundaries. */\nfunction containsPierceShadowDom(parent: HTMLElement, child: HTMLElement | null): boolean {\n  const supportsShadowRoot = typeof ShadowRoot !== 'undefined' && ShadowRoot;\n  let current: Node | null = child;\n\n  while (current) {\n    if (current === parent) {\n      return true;\n    }\n\n    current =\n      supportsShadowRoot && current instanceof ShadowRoot ? current.host : current.parentNode;\n  }\n\n  return false;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {DOCUMENT} from '@angular/common';\nimport {\n  Injectable,\n  OnDestroy,\n  Component,\n  ChangeDetectionStrategy,\n  ViewEncapsulation,\n  inject,\n} from '@angular/core';\nimport {_CdkPrivateStyleLoader} from '../private';\nimport {Platform, _isTestEnvironment} from '../platform';\n\n@Component({\n  template: '',\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  encapsulation: ViewEncapsulation.None,\n  styleUrl: 'overlay-structure.css',\n  host: {'cdk-overlay-style-loader': ''},\n})\nexport class _CdkOverlayStyleLoader {}\n\n/** Container inside which all overlays will render. */\n@Injectable({providedIn: 'root'})\nexport class OverlayContainer implements OnDestroy {\n  protected _platform = inject(Platform);\n\n  protected _containerElement: HTMLElement;\n  protected _document = inject(DOCUMENT);\n  protected _styleLoader = inject(_CdkPrivateStyleLoader);\n\n  constructor(...args: unknown[]);\n  constructor() {}\n\n  ngOnDestroy() {\n    this._containerElement?.remove();\n  }\n\n  /**\n   * This method returns the overlay container element. It will lazily\n   * create the element the first time it is called to facilitate using\n   * the container in non-browser environments.\n   * @returns the container element\n   */\n  getContainerElement(): HTMLElement {\n    this._loadStyles();\n\n    if (!this._containerElement) {\n      this._createContainer();\n    }\n\n    return this._containerElement;\n  }\n\n  /**\n   * Create the overlay container element, which is simply a div\n   * with the 'cdk-overlay-container' class on the document body.\n   */\n  protected _createContainer(): void {\n    const containerClass = 'cdk-overlay-container';\n\n    // TODO(crisbeto): remove the testing check once we have an overlay testing\n    // module or Angular starts tearing down the testing `NgModule`. See:\n    // https://github.com/angular/angular/issues/18831\n    if (this._platform.isBrowser || _isTestEnvironment()) {\n      const oppositePlatformContainers = this._document.querySelectorAll(\n        `.${containerClass}[platform=\"server\"], ` + `.${containerClass}[platform=\"test\"]`,\n      );\n\n      // Remove any old containers from the opposite platform.\n      // This can happen when transitioning from the server to the client.\n      for (let i = 0; i < oppositePlatformContainers.length; i++) {\n        oppositePlatformContainers[i].remove();\n      }\n    }\n\n    const container = this._document.createElement('div');\n    container.classList.add(containerClass);\n\n    // A long time ago we kept adding new overlay containers whenever a new app was instantiated,\n    // but at some point we added logic which clears the duplicate ones in order to avoid leaks.\n    // The new logic was a little too aggressive since it was breaking some legitimate use cases.\n    // To mitigate the problem we made it so that only containers from a different platform are\n    // cleared, but the side-effect was that people started depending on the overly-aggressive\n    // logic to clean up their tests for them. Until we can introduce an overlay-specific testing\n    // module which does the cleanup, we try to detect that we're in a test environment and we\n    // always clear the container. See #17006.\n    // TODO(crisbeto): remove the test environment check once we have an overlay testing module.\n    if (_isTestEnvironment()) {\n      container.setAttribute('platform', 'test');\n    } else if (!this._platform.isBrowser) {\n      container.setAttribute('platform', 'server');\n    }\n\n    this._document.body.appendChild(container);\n    this._containerElement = container;\n  }\n\n  /** Loads the structural styles necessary for the overlay to work. */\n  protected _loadStyles(): void {\n    this._styleLoader.load(_CdkOverlayStyleLoader);\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {NgZone, Renderer2} from '@angular/core';\n\n/** Encapsulates the logic for attaching and detaching a backdrop. */\nexport class BackdropRef {\n  readonly element: HTMLElement;\n  private _cleanupClick: (() => void) | undefined;\n  private _cleanupTransitionEnd: (() => void) | undefined;\n  private _fallbackTimeout: ReturnType<typeof setTimeout> | undefined;\n\n  constructor(\n    document: Document,\n    private _renderer: Renderer2,\n    private _ngZone: NgZone,\n    onClick: (event: MouseEvent) => void,\n  ) {\n    this.element = document.createElement('div');\n    this.element.classList.add('cdk-overlay-backdrop');\n    this._cleanupClick = _renderer.listen(this.element, 'click', onClick);\n  }\n\n  detach() {\n    this._ngZone.runOutsideAngular(() => {\n      const element = this.element;\n      clearTimeout(this._fallbackTimeout);\n      this._cleanupTransitionEnd?.();\n      this._cleanupTransitionEnd = this._renderer.listen(element, 'transitionend', this.dispose);\n      this._fallbackTimeout = setTimeout(this.dispose, 500);\n\n      // If the backdrop doesn't have a transition, the `transitionend` event won't fire.\n      // In this case we make it unclickable and we try to remove it after a delay.\n      element.style.pointerEvents = 'none';\n      element.classList.remove('cdk-overlay-backdrop-showing');\n    });\n  }\n\n  dispose = () => {\n    clearTimeout(this._fallbackTimeout);\n    this._cleanupClick?.();\n    this._cleanupTransitionEnd?.();\n    this._cleanupClick = this._cleanupTransitionEnd = this._fallbackTimeout = undefined;\n    this.element.remove();\n  };\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {Direction, Directionality} from '../bidi';\nimport {ComponentPortal, Portal, PortalOutlet, TemplatePortal} from '../portal';\nimport {\n  AfterRenderRef,\n  ComponentRef,\n  EmbeddedViewRef,\n  EnvironmentInjector,\n  NgZone,\n  Renderer2,\n  afterNextRender,\n  afterRender,\n  untracked,\n} from '@angular/core';\nimport {Location} from '@angular/common';\nimport {Observable, Subject, merge, SubscriptionLike, Subscription} from 'rxjs';\nimport {takeUntil} from 'rxjs/operators';\nimport {OverlayKeyboardDispatcher} from './dispatchers/overlay-keyboard-dispatcher';\nimport {OverlayOutsideClickDispatcher} from './dispatchers/overlay-outside-click-dispatcher';\nimport {OverlayConfig} from './overlay-config';\nimport {coerceCssPixelValue, coerceArray} from '../coercion';\nimport {PositionStrategy} from './position/position-strategy';\nimport {ScrollStrategy} from './scroll';\nimport {BackdropRef} from './backdrop-ref';\n\n/** An object where all of its properties cannot be written. */\nexport type ImmutableObject<T> = {\n  readonly [P in keyof T]: T[P];\n};\n\n/**\n * Reference to an overlay that has been created with the Overlay service.\n * Used to manipulate or dispose of said overlay.\n */\nexport class OverlayRef implements PortalOutlet {\n  private readonly _backdropClick = new Subject<MouseEvent>();\n  private readonly _attachments = new Subject<void>();\n  private readonly _detachments = new Subject<void>();\n  private _positionStrategy: PositionStrategy | undefined;\n  private _scrollStrategy: ScrollStrategy | undefined;\n  private _locationChanges: SubscriptionLike = Subscription.EMPTY;\n  private _backdropRef: BackdropRef | null = null;\n\n  /**\n   * Reference to the parent of the `_host` at the time it was detached. Used to restore\n   * the `_host` to its original position in the DOM when it gets re-attached.\n   */\n  private _previousHostParent: HTMLElement;\n\n  /** Stream of keydown events dispatched to this overlay. */\n  readonly _keydownEvents = new Subject<KeyboardEvent>();\n\n  /** Stream of mouse outside events dispatched to this overlay. */\n  readonly _outsidePointerEvents = new Subject<MouseEvent>();\n\n  private _renders = new Subject<void>();\n\n  private _afterRenderRef: AfterRenderRef;\n\n  /** Reference to the currently-running `afterNextRender` call. */\n  private _afterNextRenderRef: AfterRenderRef | undefined;\n\n  constructor(\n    private _portalOutlet: PortalOutlet,\n    private _host: HTMLElement,\n    private _pane: HTMLElement,\n    private _config: ImmutableObject<OverlayConfig>,\n    private _ngZone: NgZone,\n    private _keyboardDispatcher: OverlayKeyboardDispatcher,\n    private _document: Document,\n    private _location: Location,\n    private _outsideClickDispatcher: OverlayOutsideClickDispatcher,\n    private _animationsDisabled = false,\n    private _injector: EnvironmentInjector,\n    private _renderer: Renderer2,\n  ) {\n    if (_config.scrollStrategy) {\n      this._scrollStrategy = _config.scrollStrategy;\n      this._scrollStrategy.attach(this);\n    }\n\n    this._positionStrategy = _config.positionStrategy;\n\n    // Users could open the overlay from an `effect`, in which case we need to\n    // run the `afterRender` as `untracked`. We don't recommend that users do\n    // this, but we also don't want to break users who are doing it.\n    this._afterRenderRef = untracked(() =>\n      afterRender(\n        () => {\n          this._renders.next();\n        },\n        {injector: this._injector},\n      ),\n    );\n  }\n\n  /** The overlay's HTML element */\n  get overlayElement(): HTMLElement {\n    return this._pane;\n  }\n\n  /** The overlay's backdrop HTML element. */\n  get backdropElement(): HTMLElement | null {\n    return this._backdropRef?.element || null;\n  }\n\n  /**\n   * Wrapper around the panel element. Can be used for advanced\n   * positioning where a wrapper with specific styling is\n   * required around the overlay pane.\n   */\n  get hostElement(): HTMLElement {\n    return this._host;\n  }\n\n  attach<T>(portal: ComponentPortal<T>): ComponentRef<T>;\n  attach<T>(portal: TemplatePortal<T>): EmbeddedViewRef<T>;\n  attach(portal: any): any;\n\n  /**\n   * Attaches content, given via a Portal, to the overlay.\n   * If the overlay is configured to have a backdrop, it will be created.\n   *\n   * @param portal Portal instance to which to attach the overlay.\n   * @returns The portal attachment result.\n   */\n  attach(portal: Portal<any>): any {\n    // Insert the host into the DOM before attaching the portal, otherwise\n    // the animations module will skip animations on repeat attachments.\n    if (!this._host.parentElement && this._previousHostParent) {\n      this._previousHostParent.appendChild(this._host);\n    }\n\n    const attachResult = this._portalOutlet.attach(portal);\n\n    if (this._positionStrategy) {\n      this._positionStrategy.attach(this);\n    }\n\n    this._updateStackingOrder();\n    this._updateElementSize();\n    this._updateElementDirection();\n\n    if (this._scrollStrategy) {\n      this._scrollStrategy.enable();\n    }\n\n    // We need to clean this up ourselves, because we're passing in an\n    // `EnvironmentInjector` below which won't ever be destroyed.\n    // Otherwise it causes some callbacks to be retained (see #29696).\n    this._afterNextRenderRef?.destroy();\n\n    // Update the position once the overlay is fully rendered before attempting to position it,\n    // as the position may depend on the size of the rendered content.\n    this._afterNextRenderRef = afterNextRender(\n      () => {\n        // The overlay could've been detached before the callback executed.\n        if (this.hasAttached()) {\n          this.updatePosition();\n        }\n      },\n      {injector: this._injector},\n    );\n\n    // Enable pointer events for the overlay pane element.\n    this._togglePointerEvents(true);\n\n    if (this._config.hasBackdrop) {\n      this._attachBackdrop();\n    }\n\n    if (this._config.panelClass) {\n      this._toggleClasses(this._pane, this._config.panelClass, true);\n    }\n\n    // Only emit the `attachments` event once all other setup is done.\n    this._attachments.next();\n\n    // Track this overlay by the keyboard dispatcher\n    this._keyboardDispatcher.add(this);\n\n    if (this._config.disposeOnNavigation) {\n      this._locationChanges = this._location.subscribe(() => this.dispose());\n    }\n\n    this._outsideClickDispatcher.add(this);\n\n    // TODO(crisbeto): the null check is here, because the portal outlet returns `any`.\n    // We should be guaranteed for the result to be `ComponentRef | EmbeddedViewRef`, but\n    // `instanceof EmbeddedViewRef` doesn't appear to work at the moment.\n    if (typeof attachResult?.onDestroy === 'function') {\n      // In most cases we control the portal and we know when it is being detached so that\n      // we can finish the disposal process. The exception is if the user passes in a custom\n      // `ViewContainerRef` that isn't destroyed through the overlay API. Note that we use\n      // `detach` here instead of `dispose`, because we don't know if the user intends to\n      // reattach the overlay at a later point. It also has the advantage of waiting for animations.\n      attachResult.onDestroy(() => {\n        if (this.hasAttached()) {\n          // We have to delay the `detach` call, because detaching immediately prevents\n          // other destroy hooks from running. This is likely a framework bug similar to\n          // https://github.com/angular/angular/issues/46119\n          this._ngZone.runOutsideAngular(() => Promise.resolve().then(() => this.detach()));\n        }\n      });\n    }\n\n    return attachResult;\n  }\n\n  /**\n   * Detaches an overlay from a portal.\n   * @returns The portal detachment result.\n   */\n  detach(): any {\n    if (!this.hasAttached()) {\n      return;\n    }\n\n    this.detachBackdrop();\n\n    // When the overlay is detached, the pane element should disable pointer events.\n    // This is necessary because otherwise the pane element will cover the page and disable\n    // pointer events therefore. Depends on the position strategy and the applied pane boundaries.\n    this._togglePointerEvents(false);\n\n    if (this._positionStrategy && this._positionStrategy.detach) {\n      this._positionStrategy.detach();\n    }\n\n    if (this._scrollStrategy) {\n      this._scrollStrategy.disable();\n    }\n\n    const detachmentResult = this._portalOutlet.detach();\n\n    // Only emit after everything is detached.\n    this._detachments.next();\n\n    // Remove this overlay from keyboard dispatcher tracking.\n    this._keyboardDispatcher.remove(this);\n\n    // Keeping the host element in the DOM can cause scroll jank, because it still gets\n    // rendered, even though it's transparent and unclickable which is why we remove it.\n    this._detachContentWhenEmpty();\n    this._locationChanges.unsubscribe();\n    this._outsideClickDispatcher.remove(this);\n    return detachmentResult;\n  }\n\n  /** Cleans up the overlay from the DOM. */\n  dispose(): void {\n    const isAttached = this.hasAttached();\n\n    if (this._positionStrategy) {\n      this._positionStrategy.dispose();\n    }\n\n    this._disposeScrollStrategy();\n    this._backdropRef?.dispose();\n    this._locationChanges.unsubscribe();\n    this._keyboardDispatcher.remove(this);\n    this._portalOutlet.dispose();\n    this._attachments.complete();\n    this._backdropClick.complete();\n    this._keydownEvents.complete();\n    this._outsidePointerEvents.complete();\n    this._outsideClickDispatcher.remove(this);\n    this._host?.remove();\n    this._afterNextRenderRef?.destroy();\n    this._previousHostParent = this._pane = this._host = this._backdropRef = null!;\n\n    if (isAttached) {\n      this._detachments.next();\n    }\n\n    this._detachments.complete();\n    this._afterRenderRef.destroy();\n    this._renders.complete();\n  }\n\n  /** Whether the overlay has attached content. */\n  hasAttached(): boolean {\n    return this._portalOutlet.hasAttached();\n  }\n\n  /** Gets an observable that emits when the backdrop has been clicked. */\n  backdropClick(): Observable<MouseEvent> {\n    return this._backdropClick;\n  }\n\n  /** Gets an observable that emits when the overlay has been attached. */\n  attachments(): Observable<void> {\n    return this._attachments;\n  }\n\n  /** Gets an observable that emits when the overlay has been detached. */\n  detachments(): Observable<void> {\n    return this._detachments;\n  }\n\n  /** Gets an observable of keydown events targeted to this overlay. */\n  keydownEvents(): Observable<KeyboardEvent> {\n    return this._keydownEvents;\n  }\n\n  /** Gets an observable of pointer events targeted outside this overlay. */\n  outsidePointerEvents(): Observable<MouseEvent> {\n    return this._outsidePointerEvents;\n  }\n\n  /** Gets the current overlay configuration, which is immutable. */\n  getConfig(): OverlayConfig {\n    return this._config;\n  }\n\n  /** Updates the position of the overlay based on the position strategy. */\n  updatePosition(): void {\n    if (this._positionStrategy) {\n      this._positionStrategy.apply();\n    }\n  }\n\n  /** Switches to a new position strategy and updates the overlay position. */\n  updatePositionStrategy(strategy: PositionStrategy): void {\n    if (strategy === this._positionStrategy) {\n      return;\n    }\n\n    if (this._positionStrategy) {\n      this._positionStrategy.dispose();\n    }\n\n    this._positionStrategy = strategy;\n\n    if (this.hasAttached()) {\n      strategy.attach(this);\n      this.updatePosition();\n    }\n  }\n\n  /** Update the size properties of the overlay. */\n  updateSize(sizeConfig: OverlaySizeConfig): void {\n    this._config = {...this._config, ...sizeConfig};\n    this._updateElementSize();\n  }\n\n  /** Sets the LTR/RTL direction for the overlay. */\n  setDirection(dir: Direction | Directionality): void {\n    this._config = {...this._config, direction: dir};\n    this._updateElementDirection();\n  }\n\n  /** Add a CSS class or an array of classes to the overlay pane. */\n  addPanelClass(classes: string | string[]): void {\n    if (this._pane) {\n      this._toggleClasses(this._pane, classes, true);\n    }\n  }\n\n  /** Remove a CSS class or an array of classes from the overlay pane. */\n  removePanelClass(classes: string | string[]): void {\n    if (this._pane) {\n      this._toggleClasses(this._pane, classes, false);\n    }\n  }\n\n  /**\n   * Returns the layout direction of the overlay panel.\n   */\n  getDirection(): Direction {\n    const direction = this._config.direction;\n\n    if (!direction) {\n      return 'ltr';\n    }\n\n    return typeof direction === 'string' ? direction : direction.value;\n  }\n\n  /** Switches to a new scroll strategy. */\n  updateScrollStrategy(strategy: ScrollStrategy): void {\n    if (strategy === this._scrollStrategy) {\n      return;\n    }\n\n    this._disposeScrollStrategy();\n    this._scrollStrategy = strategy;\n\n    if (this.hasAttached()) {\n      strategy.attach(this);\n      strategy.enable();\n    }\n  }\n\n  /** Updates the text direction of the overlay panel. */\n  private _updateElementDirection() {\n    this._host.setAttribute('dir', this.getDirection());\n  }\n\n  /** Updates the size of the overlay element based on the overlay config. */\n  private _updateElementSize() {\n    if (!this._pane) {\n      return;\n    }\n\n    const style = this._pane.style;\n\n    style.width = coerceCssPixelValue(this._config.width);\n    style.height = coerceCssPixelValue(this._config.height);\n    style.minWidth = coerceCssPixelValue(this._config.minWidth);\n    style.minHeight = coerceCssPixelValue(this._config.minHeight);\n    style.maxWidth = coerceCssPixelValue(this._config.maxWidth);\n    style.maxHeight = coerceCssPixelValue(this._config.maxHeight);\n  }\n\n  /** Toggles the pointer events for the overlay pane element. */\n  private _togglePointerEvents(enablePointer: boolean) {\n    this._pane.style.pointerEvents = enablePointer ? '' : 'none';\n  }\n\n  /** Attaches a backdrop for this overlay. */\n  private _attachBackdrop() {\n    const showingClass = 'cdk-overlay-backdrop-showing';\n\n    this._backdropRef?.dispose();\n    this._backdropRef = new BackdropRef(this._document, this._renderer, this._ngZone, event => {\n      this._backdropClick.next(event);\n    });\n\n    if (this._animationsDisabled) {\n      this._backdropRef.element.classList.add('cdk-overlay-backdrop-noop-animation');\n    }\n\n    if (this._config.backdropClass) {\n      this._toggleClasses(this._backdropRef.element, this._config.backdropClass, true);\n    }\n\n    // Insert the backdrop before the pane in the DOM order,\n    // in order to handle stacked overlays properly.\n    this._host.parentElement!.insertBefore(this._backdropRef.element, this._host);\n\n    // Add class to fade-in the backdrop after one frame.\n    if (!this._animationsDisabled && typeof requestAnimationFrame !== 'undefined') {\n      this._ngZone.runOutsideAngular(() => {\n        requestAnimationFrame(() => this._backdropRef?.element.classList.add(showingClass));\n      });\n    } else {\n      this._backdropRef.element.classList.add(showingClass);\n    }\n  }\n\n  /**\n   * Updates the stacking order of the element, moving it to the top if necessary.\n   * This is required in cases where one overlay was detached, while another one,\n   * that should be behind it, was destroyed. The next time both of them are opened,\n   * the stacking will be wrong, because the detached element's pane will still be\n   * in its original DOM position.\n   */\n  private _updateStackingOrder() {\n    if (this._host.nextSibling) {\n      this._host.parentNode!.appendChild(this._host);\n    }\n  }\n\n  /** Detaches the backdrop (if any) associated with the overlay. */\n  detachBackdrop(): void {\n    if (this._animationsDisabled) {\n      this._backdropRef?.dispose();\n      this._backdropRef = null;\n    } else {\n      this._backdropRef?.detach();\n    }\n  }\n\n  /** Toggles a single CSS class or an array of classes on an element. */\n  private _toggleClasses(element: HTMLElement, cssClasses: string | string[], isAdd: boolean) {\n    const classes = coerceArray(cssClasses || []).filter(c => !!c);\n\n    if (classes.length) {\n      isAdd ? element.classList.add(...classes) : element.classList.remove(...classes);\n    }\n  }\n\n  /** Detaches the overlay content next time the zone stabilizes. */\n  private _detachContentWhenEmpty() {\n    // Normally we wouldn't have to explicitly run this outside the `NgZone`, however\n    // if the consumer is using `zone-patch-rxjs`, the `Subscription.unsubscribe` call will\n    // be patched to run inside the zone, which will throw us into an infinite loop.\n    this._ngZone.runOutsideAngular(() => {\n      // We can't remove the host here immediately, because the overlay pane's content\n      // might still be animating. This stream helps us avoid interrupting the animation\n      // by waiting for the pane to become empty.\n      const subscription = this._renders\n        .pipe(takeUntil(merge(this._attachments, this._detachments)))\n        .subscribe(() => {\n          // Needs a couple of checks for the pane and host, because\n          // they may have been removed by the time the zone stabilizes.\n          if (!this._pane || !this._host || this._pane.children.length === 0) {\n            if (this._pane && this._config.panelClass) {\n              this._toggleClasses(this._pane, this._config.panelClass, false);\n            }\n\n            if (this._host && this._host.parentElement) {\n              this._previousHostParent = this._host.parentElement;\n              this._host.remove();\n            }\n\n            subscription.unsubscribe();\n          }\n        });\n    });\n  }\n\n  /** Disposes of a scroll strategy. */\n  private _disposeScrollStrategy() {\n    const scrollStrategy = this._scrollStrategy;\n    scrollStrategy?.disable();\n    scrollStrategy?.detach?.();\n  }\n}\n\n/** Size properties for an overlay. */\nexport interface OverlaySizeConfig {\n  width?: number | string;\n  height?: number | string;\n  minWidth?: number | string;\n  minHeight?: number | string;\n  maxWidth?: number | string;\n  maxHeight?: number | string;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {PositionStrategy} from './position-strategy';\nimport {ElementRef} from '@angular/core';\nimport {ViewportRuler, CdkScrollable, ViewportScrollPosition} from '../../scrolling';\nimport {\n  ConnectedOverlayPositionChange,\n  ConnectionPositionPair,\n  ScrollingVisibility,\n  validateHorizontalPosition,\n  validateVerticalPosition,\n} from './connected-position';\nimport {Observable, Subscription, Subject} from 'rxjs';\nimport {isElementScrolledOutsideView, isElementClippedByScrolling} from './scroll-clip';\nimport {coerceCssPixelValue, coerceArray} from '../../coercion';\nimport {Platform} from '../../platform';\nimport {OverlayContainer} from '../overlay-container';\nimport {OverlayRef} from '../overlay-ref';\n\n// TODO: refactor clipping detection into a separate thing (part of scrolling module)\n// TODO: doesn't handle both flexible width and height when it has to scroll along both axis.\n\n/** Class to be added to the overlay bounding box. */\nconst boundingBoxClass = 'cdk-overlay-connected-position-bounding-box';\n\n/** Regex used to split a string on its CSS units. */\nconst cssUnitPattern = /([A-Za-z%]+)$/;\n\n/** Possible values that can be set as the origin of a FlexibleConnectedPositionStrategy. */\nexport type FlexibleConnectedPositionStrategyOrigin =\n  | ElementRef\n  | Element\n  | (Point & {\n      width?: number;\n      height?: number;\n    });\n\n/** Equivalent of `DOMRect` without some of the properties we don't care about. */\ntype Dimensions = Omit<DOMRect, 'x' | 'y' | 'toJSON'>;\n\n/**\n * A strategy for positioning overlays. Using this strategy, an overlay is given an\n * implicit position relative some origin element. The relative position is defined in terms of\n * a point on the origin element that is connected to a point on the overlay element. For example,\n * a basic dropdown is connecting the bottom-left corner of the origin to the top-left corner\n * of the overlay.\n */\nexport class FlexibleConnectedPositionStrategy implements PositionStrategy {\n  /** The overlay to which this strategy is attached. */\n  private _overlayRef: OverlayRef;\n\n  /** Whether we're performing the very first positioning of the overlay. */\n  private _isInitialRender: boolean;\n\n  /** Last size used for the bounding box. Used to avoid resizing the overlay after open. */\n  private _lastBoundingBoxSize = {width: 0, height: 0};\n\n  /** Whether the overlay was pushed in a previous positioning. */\n  private _isPushed = false;\n\n  /** Whether the overlay can be pushed on-screen on the initial open. */\n  private _canPush = true;\n\n  /** Whether the overlay can grow via flexible width/height after the initial open. */\n  private _growAfterOpen = false;\n\n  /** Whether the overlay's width and height can be constrained to fit within the viewport. */\n  private _hasFlexibleDimensions = true;\n\n  /** Whether the overlay position is locked. */\n  private _positionLocked = false;\n\n  /** Cached origin dimensions */\n  private _originRect: Dimensions;\n\n  /** Cached overlay dimensions */\n  private _overlayRect: Dimensions;\n\n  /** Cached viewport dimensions */\n  private _viewportRect: Dimensions;\n\n  /** Cached container dimensions */\n  private _containerRect: Dimensions;\n\n  /** Amount of space that must be maintained between the overlay and the edge of the viewport. */\n  private _viewportMargin = 0;\n\n  /** The Scrollable containers used to check scrollable view properties on position change. */\n  private _scrollables: CdkScrollable[] = [];\n\n  /** Ordered list of preferred positions, from most to least desirable. */\n  _preferredPositions: ConnectionPositionPair[] = [];\n\n  /** The origin element against which the overlay will be positioned. */\n  _origin: FlexibleConnectedPositionStrategyOrigin;\n\n  /** The overlay pane element. */\n  private _pane: HTMLElement;\n\n  /** Whether the strategy has been disposed of already. */\n  private _isDisposed: boolean;\n\n  /**\n   * Parent element for the overlay panel used to constrain the overlay panel's size to fit\n   * within the viewport.\n   */\n  private _boundingBox: HTMLElement | null;\n\n  /** The last position to have been calculated as the best fit position. */\n  private _lastPosition: ConnectedPosition | null;\n\n  /** The last calculated scroll visibility. Only tracked  */\n  private _lastScrollVisibility: ScrollingVisibility | null;\n\n  /** Subject that emits whenever the position changes. */\n  private readonly _positionChanges = new Subject<ConnectedOverlayPositionChange>();\n\n  /** Subscription to viewport size changes. */\n  private _resizeSubscription = Subscription.EMPTY;\n\n  /** Default offset for the overlay along the x axis. */\n  private _offsetX = 0;\n\n  /** Default offset for the overlay along the y axis. */\n  private _offsetY = 0;\n\n  /** Selector to be used when finding the elements on which to set the transform origin. */\n  private _transformOriginSelector: string;\n\n  /** Keeps track of the CSS classes that the position strategy has applied on the overlay panel. */\n  private _appliedPanelClasses: string[] = [];\n\n  /** Amount by which the overlay was pushed in each axis during the last time it was positioned. */\n  private _previousPushAmount: {x: number; y: number} | null;\n\n  /** Observable sequence of position changes. */\n  positionChanges: Observable<ConnectedOverlayPositionChange> = this._positionChanges;\n\n  /** Ordered list of preferred positions, from most to least desirable. */\n  get positions(): ConnectionPositionPair[] {\n    return this._preferredPositions;\n  }\n\n  constructor(\n    connectedTo: FlexibleConnectedPositionStrategyOrigin,\n    private _viewportRuler: ViewportRuler,\n    private _document: Document,\n    private _platform: Platform,\n    private _overlayContainer: OverlayContainer,\n  ) {\n    this.setOrigin(connectedTo);\n  }\n\n  /** Attaches this position strategy to an overlay. */\n  attach(overlayRef: OverlayRef): void {\n    if (\n      this._overlayRef &&\n      overlayRef !== this._overlayRef &&\n      (typeof ngDevMode === 'undefined' || ngDevMode)\n    ) {\n      throw Error('This position strategy is already attached to an overlay');\n    }\n\n    this._validatePositions();\n\n    overlayRef.hostElement.classList.add(boundingBoxClass);\n\n    this._overlayRef = overlayRef;\n    this._boundingBox = overlayRef.hostElement;\n    this._pane = overlayRef.overlayElement;\n    this._isDisposed = false;\n    this._isInitialRender = true;\n    this._lastPosition = null;\n    this._resizeSubscription.unsubscribe();\n    this._resizeSubscription = this._viewportRuler.change().subscribe(() => {\n      // When the window is resized, we want to trigger the next reposition as if it\n      // was an initial render, in order for the strategy to pick a new optimal position,\n      // otherwise position locking will cause it to stay at the old one.\n      this._isInitialRender = true;\n      this.apply();\n    });\n  }\n\n  /**\n   * Updates the position of the overlay element, using whichever preferred position relative\n   * to the origin best fits on-screen.\n   *\n   * The selection of a position goes as follows:\n   *  - If any positions fit completely within the viewport as-is,\n   *      choose the first position that does so.\n   *  - If flexible dimensions are enabled and at least one satisfies the given minimum width/height,\n   *      choose the position with the greatest available size modified by the positions' weight.\n   *  - If pushing is enabled, take the position that went off-screen the least and push it\n   *      on-screen.\n   *  - If none of the previous criteria were met, use the position that goes off-screen the least.\n   * @docs-private\n   */\n  apply(): void {\n    // We shouldn't do anything if the strategy was disposed or we're on the server.\n    if (this._isDisposed || !this._platform.isBrowser) {\n      return;\n    }\n\n    // If the position has been applied already (e.g. when the overlay was opened) and the\n    // consumer opted into locking in the position, re-use the old position, in order to\n    // prevent the overlay from jumping around.\n    if (!this._isInitialRender && this._positionLocked && this._lastPosition) {\n      this.reapplyLastPosition();\n      return;\n    }\n\n    this._clearPanelClasses();\n    this._resetOverlayElementStyles();\n    this._resetBoundingBoxStyles();\n\n    // We need the bounding rects for the origin, the overlay and the container to determine how to position\n    // the overlay relative to the origin.\n    // We use the viewport rect to determine whether a position would go off-screen.\n    this._viewportRect = this._getNarrowedViewportRect();\n    this._originRect = this._getOriginRect();\n    this._overlayRect = this._pane.getBoundingClientRect();\n    this._containerRect = this._overlayContainer.getContainerElement().getBoundingClientRect();\n\n    const originRect = this._originRect;\n    const overlayRect = this._overlayRect;\n    const viewportRect = this._viewportRect;\n    const containerRect = this._containerRect;\n\n    // Positions where the overlay will fit with flexible dimensions.\n    const flexibleFits: FlexibleFit[] = [];\n\n    // Fallback if none of the preferred positions fit within the viewport.\n    let fallback: FallbackPosition | undefined;\n\n    // Go through each of the preferred positions looking for a good fit.\n    // If a good fit is found, it will be applied immediately.\n    for (let pos of this._preferredPositions) {\n      // Get the exact (x, y) coordinate for the point-of-origin on the origin element.\n      let originPoint = this._getOriginPoint(originRect, containerRect, pos);\n\n      // From that point-of-origin, get the exact (x, y) coordinate for the top-left corner of the\n      // overlay in this position. We use the top-left corner for calculations and later translate\n      // this into an appropriate (top, left, bottom, right) style.\n      let overlayPoint = this._getOverlayPoint(originPoint, overlayRect, pos);\n\n      // Calculate how well the overlay would fit into the viewport with this point.\n      let overlayFit = this._getOverlayFit(overlayPoint, overlayRect, viewportRect, pos);\n\n      // If the overlay, without any further work, fits into the viewport, use this position.\n      if (overlayFit.isCompletelyWithinViewport) {\n        this._isPushed = false;\n        this._applyPosition(pos, originPoint);\n        return;\n      }\n\n      // If the overlay has flexible dimensions, we can use this position\n      // so long as there's enough space for the minimum dimensions.\n      if (this._canFitWithFlexibleDimensions(overlayFit, overlayPoint, viewportRect)) {\n        // Save positions where the overlay will fit with flexible dimensions. We will use these\n        // if none of the positions fit *without* flexible dimensions.\n        flexibleFits.push({\n          position: pos,\n          origin: originPoint,\n          overlayRect,\n          boundingBoxRect: this._calculateBoundingBoxRect(originPoint, pos),\n        });\n\n        continue;\n      }\n\n      // If the current preferred position does not fit on the screen, remember the position\n      // if it has more visible area on-screen than we've seen and move onto the next preferred\n      // position.\n      if (!fallback || fallback.overlayFit.visibleArea < overlayFit.visibleArea) {\n        fallback = {overlayFit, overlayPoint, originPoint, position: pos, overlayRect};\n      }\n    }\n\n    // If there are any positions where the overlay would fit with flexible dimensions, choose the\n    // one that has the greatest area available modified by the position's weight\n    if (flexibleFits.length) {\n      let bestFit: FlexibleFit | null = null;\n      let bestScore = -1;\n      for (const fit of flexibleFits) {\n        const score =\n          fit.boundingBoxRect.width * fit.boundingBoxRect.height * (fit.position.weight || 1);\n        if (score > bestScore) {\n          bestScore = score;\n          bestFit = fit;\n        }\n      }\n\n      this._isPushed = false;\n      this._applyPosition(bestFit!.position, bestFit!.origin);\n      return;\n    }\n\n    // When none of the preferred positions fit within the viewport, take the position\n    // that went off-screen the least and attempt to push it on-screen.\n    if (this._canPush) {\n      // TODO(jelbourn): after pushing, the opening \"direction\" of the overlay might not make sense.\n      this._isPushed = true;\n      this._applyPosition(fallback!.position, fallback!.originPoint);\n      return;\n    }\n\n    // All options for getting the overlay within the viewport have been exhausted, so go with the\n    // position that went off-screen the least.\n    this._applyPosition(fallback!.position, fallback!.originPoint);\n  }\n\n  detach(): void {\n    this._clearPanelClasses();\n    this._lastPosition = null;\n    this._previousPushAmount = null;\n    this._resizeSubscription.unsubscribe();\n  }\n\n  /** Cleanup after the element gets destroyed. */\n  dispose(): void {\n    if (this._isDisposed) {\n      return;\n    }\n\n    // We can't use `_resetBoundingBoxStyles` here, because it resets\n    // some properties to zero, rather than removing them.\n    if (this._boundingBox) {\n      extendStyles(this._boundingBox.style, {\n        top: '',\n        left: '',\n        right: '',\n        bottom: '',\n        height: '',\n        width: '',\n        alignItems: '',\n        justifyContent: '',\n      } as CSSStyleDeclaration);\n    }\n\n    if (this._pane) {\n      this._resetOverlayElementStyles();\n    }\n\n    if (this._overlayRef) {\n      this._overlayRef.hostElement.classList.remove(boundingBoxClass);\n    }\n\n    this.detach();\n    this._positionChanges.complete();\n    this._overlayRef = this._boundingBox = null!;\n    this._isDisposed = true;\n  }\n\n  /**\n   * This re-aligns the overlay element with the trigger in its last calculated position,\n   * even if a position higher in the \"preferred positions\" list would now fit. This\n   * allows one to re-align the panel without changing the orientation of the panel.\n   */\n  reapplyLastPosition(): void {\n    if (this._isDisposed || !this._platform.isBrowser) {\n      return;\n    }\n\n    const lastPosition = this._lastPosition;\n\n    if (lastPosition) {\n      this._originRect = this._getOriginRect();\n      this._overlayRect = this._pane.getBoundingClientRect();\n      this._viewportRect = this._getNarrowedViewportRect();\n      this._containerRect = this._overlayContainer.getContainerElement().getBoundingClientRect();\n\n      const originPoint = this._getOriginPoint(this._originRect, this._containerRect, lastPosition);\n      this._applyPosition(lastPosition, originPoint);\n    } else {\n      this.apply();\n    }\n  }\n\n  /**\n   * Sets the list of Scrollable containers that host the origin element so that\n   * on reposition we can evaluate if it or the overlay has been clipped or outside view. Every\n   * Scrollable must be an ancestor element of the strategy's origin element.\n   */\n  withScrollableContainers(scrollables: CdkScrollable[]): this {\n    this._scrollables = scrollables;\n    return this;\n  }\n\n  /**\n   * Adds new preferred positions.\n   * @param positions List of positions options for this overlay.\n   */\n  withPositions(positions: ConnectedPosition[]): this {\n    this._preferredPositions = positions;\n\n    // If the last calculated position object isn't part of the positions anymore, clear\n    // it in order to avoid it being picked up if the consumer tries to re-apply.\n    if (positions.indexOf(this._lastPosition!) === -1) {\n      this._lastPosition = null;\n    }\n\n    this._validatePositions();\n\n    return this;\n  }\n\n  /**\n   * Sets a minimum distance the overlay may be positioned to the edge of the viewport.\n   * @param margin Required margin between the overlay and the viewport edge in pixels.\n   */\n  withViewportMargin(margin: number): this {\n    this._viewportMargin = margin;\n    return this;\n  }\n\n  /** Sets whether the overlay's width and height can be constrained to fit within the viewport. */\n  withFlexibleDimensions(flexibleDimensions = true): this {\n    this._hasFlexibleDimensions = flexibleDimensions;\n    return this;\n  }\n\n  /** Sets whether the overlay can grow after the initial open via flexible width/height. */\n  withGrowAfterOpen(growAfterOpen = true): this {\n    this._growAfterOpen = growAfterOpen;\n    return this;\n  }\n\n  /** Sets whether the overlay can be pushed on-screen if none of the provided positions fit. */\n  withPush(canPush = true): this {\n    this._canPush = canPush;\n    return this;\n  }\n\n  /**\n   * Sets whether the overlay's position should be locked in after it is positioned\n   * initially. When an overlay is locked in, it won't attempt to reposition itself\n   * when the position is re-applied (e.g. when the user scrolls away).\n   * @param isLocked Whether the overlay should locked in.\n   */\n  withLockedPosition(isLocked = true): this {\n    this._positionLocked = isLocked;\n    return this;\n  }\n\n  /**\n   * Sets the origin, relative to which to position the overlay.\n   * Using an element origin is useful for building components that need to be positioned\n   * relatively to a trigger (e.g. dropdown menus or tooltips), whereas using a point can be\n   * used for cases like contextual menus which open relative to the user's pointer.\n   * @param origin Reference to the new origin.\n   */\n  setOrigin(origin: FlexibleConnectedPositionStrategyOrigin): this {\n    this._origin = origin;\n    return this;\n  }\n\n  /**\n   * Sets the default offset for the overlay's connection point on the x-axis.\n   * @param offset New offset in the X axis.\n   */\n  withDefaultOffsetX(offset: number): this {\n    this._offsetX = offset;\n    return this;\n  }\n\n  /**\n   * Sets the default offset for the overlay's connection point on the y-axis.\n   * @param offset New offset in the Y axis.\n   */\n  withDefaultOffsetY(offset: number): this {\n    this._offsetY = offset;\n    return this;\n  }\n\n  /**\n   * Configures that the position strategy should set a `transform-origin` on some elements\n   * inside the overlay, depending on the current position that is being applied. This is\n   * useful for the cases where the origin of an animation can change depending on the\n   * alignment of the overlay.\n   * @param selector CSS selector that will be used to find the target\n   *    elements onto which to set the transform origin.\n   */\n  withTransformOriginOn(selector: string): this {\n    this._transformOriginSelector = selector;\n    return this;\n  }\n\n  /**\n   * Gets the (x, y) coordinate of a connection point on the origin based on a relative position.\n   */\n  private _getOriginPoint(\n    originRect: Dimensions,\n    containerRect: Dimensions,\n    pos: ConnectedPosition,\n  ): Point {\n    let x: number;\n    if (pos.originX == 'center') {\n      // Note: when centering we should always use the `left`\n      // offset, otherwise the position will be wrong in RTL.\n      x = originRect.left + originRect.width / 2;\n    } else {\n      const startX = this._isRtl() ? originRect.right : originRect.left;\n      const endX = this._isRtl() ? originRect.left : originRect.right;\n      x = pos.originX == 'start' ? startX : endX;\n    }\n\n    // When zooming in Safari the container rectangle contains negative values for the position\n    // and we need to re-add them to the calculated coordinates.\n    if (containerRect.left < 0) {\n      x -= containerRect.left;\n    }\n\n    let y: number;\n    if (pos.originY == 'center') {\n      y = originRect.top + originRect.height / 2;\n    } else {\n      y = pos.originY == 'top' ? originRect.top : originRect.bottom;\n    }\n\n    // Normally the containerRect's top value would be zero, however when the overlay is attached to an input\n    // (e.g. in an autocomplete), mobile browsers will shift everything in order to put the input in the middle\n    // of the screen and to make space for the virtual keyboard. We need to account for this offset,\n    // otherwise our positioning will be thrown off.\n    // Additionally, when zooming in Safari this fixes the vertical position.\n    if (containerRect.top < 0) {\n      y -= containerRect.top;\n    }\n\n    return {x, y};\n  }\n\n  /**\n   * Gets the (x, y) coordinate of the top-left corner of the overlay given a given position and\n   * origin point to which the overlay should be connected.\n   */\n  private _getOverlayPoint(\n    originPoint: Point,\n    overlayRect: Dimensions,\n    pos: ConnectedPosition,\n  ): Point {\n    // Calculate the (overlayStartX, overlayStartY), the start of the\n    // potential overlay position relative to the origin point.\n    let overlayStartX: number;\n    if (pos.overlayX == 'center') {\n      overlayStartX = -overlayRect.width / 2;\n    } else if (pos.overlayX === 'start') {\n      overlayStartX = this._isRtl() ? -overlayRect.width : 0;\n    } else {\n      overlayStartX = this._isRtl() ? 0 : -overlayRect.width;\n    }\n\n    let overlayStartY: number;\n    if (pos.overlayY == 'center') {\n      overlayStartY = -overlayRect.height / 2;\n    } else {\n      overlayStartY = pos.overlayY == 'top' ? 0 : -overlayRect.height;\n    }\n\n    // The (x, y) coordinates of the overlay.\n    return {\n      x: originPoint.x + overlayStartX,\n      y: originPoint.y + overlayStartY,\n    };\n  }\n\n  /** Gets how well an overlay at the given point will fit within the viewport. */\n  private _getOverlayFit(\n    point: Point,\n    rawOverlayRect: Dimensions,\n    viewport: Dimensions,\n    position: ConnectedPosition,\n  ): OverlayFit {\n    // Round the overlay rect when comparing against the\n    // viewport, because the viewport is always rounded.\n    const overlay = getRoundedBoundingClientRect(rawOverlayRect);\n    let {x, y} = point;\n    let offsetX = this._getOffset(position, 'x');\n    let offsetY = this._getOffset(position, 'y');\n\n    // Account for the offsets since they could push the overlay out of the viewport.\n    if (offsetX) {\n      x += offsetX;\n    }\n\n    if (offsetY) {\n      y += offsetY;\n    }\n\n    // How much the overlay would overflow at this position, on each side.\n    let leftOverflow = 0 - x;\n    let rightOverflow = x + overlay.width - viewport.width;\n    let topOverflow = 0 - y;\n    let bottomOverflow = y + overlay.height - viewport.height;\n\n    // Visible parts of the element on each axis.\n    let visibleWidth = this._subtractOverflows(overlay.width, leftOverflow, rightOverflow);\n    let visibleHeight = this._subtractOverflows(overlay.height, topOverflow, bottomOverflow);\n    let visibleArea = visibleWidth * visibleHeight;\n\n    return {\n      visibleArea,\n      isCompletelyWithinViewport: overlay.width * overlay.height === visibleArea,\n      fitsInViewportVertically: visibleHeight === overlay.height,\n      fitsInViewportHorizontally: visibleWidth == overlay.width,\n    };\n  }\n\n  /**\n   * Whether the overlay can fit within the viewport when it may resize either its width or height.\n   * @param fit How well the overlay fits in the viewport at some position.\n   * @param point The (x, y) coordinates of the overlay at some position.\n   * @param viewport The geometry of the viewport.\n   */\n  private _canFitWithFlexibleDimensions(fit: OverlayFit, point: Point, viewport: Dimensions) {\n    if (this._hasFlexibleDimensions) {\n      const availableHeight = viewport.bottom - point.y;\n      const availableWidth = viewport.right - point.x;\n      const minHeight = getPixelValue(this._overlayRef.getConfig().minHeight);\n      const minWidth = getPixelValue(this._overlayRef.getConfig().minWidth);\n\n      const verticalFit =\n        fit.fitsInViewportVertically || (minHeight != null && minHeight <= availableHeight);\n      const horizontalFit =\n        fit.fitsInViewportHorizontally || (minWidth != null && minWidth <= availableWidth);\n\n      return verticalFit && horizontalFit;\n    }\n    return false;\n  }\n\n  /**\n   * Gets the point at which the overlay can be \"pushed\" on-screen. If the overlay is larger than\n   * the viewport, the top-left corner will be pushed on-screen (with overflow occurring on the\n   * right and bottom).\n   *\n   * @param start Starting point from which the overlay is pushed.\n   * @param rawOverlayRect Dimensions of the overlay.\n   * @param scrollPosition Current viewport scroll position.\n   * @returns The point at which to position the overlay after pushing. This is effectively a new\n   *     originPoint.\n   */\n  private _pushOverlayOnScreen(\n    start: Point,\n    rawOverlayRect: Dimensions,\n    scrollPosition: ViewportScrollPosition,\n  ): Point {\n    // If the position is locked and we've pushed the overlay already, reuse the previous push\n    // amount, rather than pushing it again. If we were to continue pushing, the element would\n    // remain in the viewport, which goes against the expectations when position locking is enabled.\n    if (this._previousPushAmount && this._positionLocked) {\n      return {\n        x: start.x + this._previousPushAmount.x,\n        y: start.y + this._previousPushAmount.y,\n      };\n    }\n\n    // Round the overlay rect when comparing against the\n    // viewport, because the viewport is always rounded.\n    const overlay = getRoundedBoundingClientRect(rawOverlayRect);\n    const viewport = this._viewportRect;\n\n    // Determine how much the overlay goes outside the viewport on each\n    // side, which we'll use to decide which direction to push it.\n    const overflowRight = Math.max(start.x + overlay.width - viewport.width, 0);\n    const overflowBottom = Math.max(start.y + overlay.height - viewport.height, 0);\n    const overflowTop = Math.max(viewport.top - scrollPosition.top - start.y, 0);\n    const overflowLeft = Math.max(viewport.left - scrollPosition.left - start.x, 0);\n\n    // Amount by which to push the overlay in each axis such that it remains on-screen.\n    let pushX = 0;\n    let pushY = 0;\n\n    // If the overlay fits completely within the bounds of the viewport, push it from whichever\n    // direction is goes off-screen. Otherwise, push the top-left corner such that its in the\n    // viewport and allow for the trailing end of the overlay to go out of bounds.\n    if (overlay.width <= viewport.width) {\n      pushX = overflowLeft || -overflowRight;\n    } else {\n      pushX = start.x < this._viewportMargin ? viewport.left - scrollPosition.left - start.x : 0;\n    }\n\n    if (overlay.height <= viewport.height) {\n      pushY = overflowTop || -overflowBottom;\n    } else {\n      pushY = start.y < this._viewportMargin ? viewport.top - scrollPosition.top - start.y : 0;\n    }\n\n    this._previousPushAmount = {x: pushX, y: pushY};\n\n    return {\n      x: start.x + pushX,\n      y: start.y + pushY,\n    };\n  }\n\n  /**\n   * Applies a computed position to the overlay and emits a position change.\n   * @param position The position preference\n   * @param originPoint The point on the origin element where the overlay is connected.\n   */\n  private _applyPosition(position: ConnectedPosition, originPoint: Point) {\n    this._setTransformOrigin(position);\n    this._setOverlayElementStyles(originPoint, position);\n    this._setBoundingBoxStyles(originPoint, position);\n\n    if (position.panelClass) {\n      this._addPanelClasses(position.panelClass);\n    }\n\n    // Notify that the position has been changed along with its change properties.\n    // We only emit if we've got any subscriptions, because the scroll visibility\n    // calculations can be somewhat expensive.\n    if (this._positionChanges.observers.length) {\n      const scrollVisibility = this._getScrollVisibility();\n\n      // We're recalculating on scroll, but we only want to emit if anything\n      // changed since downstream code might be hitting the `NgZone`.\n      if (\n        position !== this._lastPosition ||\n        !this._lastScrollVisibility ||\n        !compareScrollVisibility(this._lastScrollVisibility, scrollVisibility)\n      ) {\n        const changeEvent = new ConnectedOverlayPositionChange(position, scrollVisibility);\n        this._positionChanges.next(changeEvent);\n      }\n\n      this._lastScrollVisibility = scrollVisibility;\n    }\n\n    // Save the last connected position in case the position needs to be re-calculated.\n    this._lastPosition = position;\n    this._isInitialRender = false;\n  }\n\n  /** Sets the transform origin based on the configured selector and the passed-in position.  */\n  private _setTransformOrigin(position: ConnectedPosition) {\n    if (!this._transformOriginSelector) {\n      return;\n    }\n\n    const elements: NodeListOf<HTMLElement> = this._boundingBox!.querySelectorAll(\n      this._transformOriginSelector,\n    );\n    let xOrigin: 'left' | 'right' | 'center';\n    let yOrigin: 'top' | 'bottom' | 'center' = position.overlayY;\n\n    if (position.overlayX === 'center') {\n      xOrigin = 'center';\n    } else if (this._isRtl()) {\n      xOrigin = position.overlayX === 'start' ? 'right' : 'left';\n    } else {\n      xOrigin = position.overlayX === 'start' ? 'left' : 'right';\n    }\n\n    for (let i = 0; i < elements.length; i++) {\n      elements[i].style.transformOrigin = `${xOrigin} ${yOrigin}`;\n    }\n  }\n\n  /**\n   * Gets the position and size of the overlay's sizing container.\n   *\n   * This method does no measuring and applies no styles so that we can cheaply compute the\n   * bounds for all positions and choose the best fit based on these results.\n   */\n  private _calculateBoundingBoxRect(origin: Point, position: ConnectedPosition): BoundingBoxRect {\n    const viewport = this._viewportRect;\n    const isRtl = this._isRtl();\n    let height: number, top: number, bottom: number;\n\n    if (position.overlayY === 'top') {\n      // Overlay is opening \"downward\" and thus is bound by the bottom viewport edge.\n      top = origin.y;\n      height = viewport.height - top + this._viewportMargin;\n    } else if (position.overlayY === 'bottom') {\n      // Overlay is opening \"upward\" and thus is bound by the top viewport edge. We need to add\n      // the viewport margin back in, because the viewport rect is narrowed down to remove the\n      // margin, whereas the `origin` position is calculated based on its `DOMRect`.\n      bottom = viewport.height - origin.y + this._viewportMargin * 2;\n      height = viewport.height - bottom + this._viewportMargin;\n    } else {\n      // If neither top nor bottom, it means that the overlay is vertically centered on the\n      // origin point. Note that we want the position relative to the viewport, rather than\n      // the page, which is why we don't use something like `viewport.bottom - origin.y` and\n      // `origin.y - viewport.top`.\n      const smallestDistanceToViewportEdge = Math.min(\n        viewport.bottom - origin.y + viewport.top,\n        origin.y,\n      );\n\n      const previousHeight = this._lastBoundingBoxSize.height;\n\n      height = smallestDistanceToViewportEdge * 2;\n      top = origin.y - smallestDistanceToViewportEdge;\n\n      if (height > previousHeight && !this._isInitialRender && !this._growAfterOpen) {\n        top = origin.y - previousHeight / 2;\n      }\n    }\n\n    // The overlay is opening 'right-ward' (the content flows to the right).\n    const isBoundedByRightViewportEdge =\n      (position.overlayX === 'start' && !isRtl) || (position.overlayX === 'end' && isRtl);\n\n    // The overlay is opening 'left-ward' (the content flows to the left).\n    const isBoundedByLeftViewportEdge =\n      (position.overlayX === 'end' && !isRtl) || (position.overlayX === 'start' && isRtl);\n\n    let width: number, left: number, right: number;\n\n    if (isBoundedByLeftViewportEdge) {\n      right = viewport.width - origin.x + this._viewportMargin * 2;\n      width = origin.x - this._viewportMargin;\n    } else if (isBoundedByRightViewportEdge) {\n      left = origin.x;\n      width = viewport.right - origin.x;\n    } else {\n      // If neither start nor end, it means that the overlay is horizontally centered on the\n      // origin point. Note that we want the position relative to the viewport, rather than\n      // the page, which is why we don't use something like `viewport.right - origin.x` and\n      // `origin.x - viewport.left`.\n      const smallestDistanceToViewportEdge = Math.min(\n        viewport.right - origin.x + viewport.left,\n        origin.x,\n      );\n      const previousWidth = this._lastBoundingBoxSize.width;\n\n      width = smallestDistanceToViewportEdge * 2;\n      left = origin.x - smallestDistanceToViewportEdge;\n\n      if (width > previousWidth && !this._isInitialRender && !this._growAfterOpen) {\n        left = origin.x - previousWidth / 2;\n      }\n    }\n\n    return {top: top!, left: left!, bottom: bottom!, right: right!, width, height};\n  }\n\n  /**\n   * Sets the position and size of the overlay's sizing wrapper. The wrapper is positioned on the\n   * origin's connection point and stretches to the bounds of the viewport.\n   *\n   * @param origin The point on the origin element where the overlay is connected.\n   * @param position The position preference\n   */\n  private _setBoundingBoxStyles(origin: Point, position: ConnectedPosition): void {\n    const boundingBoxRect = this._calculateBoundingBoxRect(origin, position);\n\n    // It's weird if the overlay *grows* while scrolling, so we take the last size into account\n    // when applying a new size.\n    if (!this._isInitialRender && !this._growAfterOpen) {\n      boundingBoxRect.height = Math.min(boundingBoxRect.height, this._lastBoundingBoxSize.height);\n      boundingBoxRect.width = Math.min(boundingBoxRect.width, this._lastBoundingBoxSize.width);\n    }\n\n    const styles = {} as CSSStyleDeclaration;\n\n    if (this._hasExactPosition()) {\n      styles.top = styles.left = '0';\n      styles.bottom = styles.right = styles.maxHeight = styles.maxWidth = '';\n      styles.width = styles.height = '100%';\n    } else {\n      const maxHeight = this._overlayRef.getConfig().maxHeight;\n      const maxWidth = this._overlayRef.getConfig().maxWidth;\n\n      styles.height = coerceCssPixelValue(boundingBoxRect.height);\n      styles.top = coerceCssPixelValue(boundingBoxRect.top);\n      styles.bottom = coerceCssPixelValue(boundingBoxRect.bottom);\n      styles.width = coerceCssPixelValue(boundingBoxRect.width);\n      styles.left = coerceCssPixelValue(boundingBoxRect.left);\n      styles.right = coerceCssPixelValue(boundingBoxRect.right);\n\n      // Push the pane content towards the proper direction.\n      if (position.overlayX === 'center') {\n        styles.alignItems = 'center';\n      } else {\n        styles.alignItems = position.overlayX === 'end' ? 'flex-end' : 'flex-start';\n      }\n\n      if (position.overlayY === 'center') {\n        styles.justifyContent = 'center';\n      } else {\n        styles.justifyContent = position.overlayY === 'bottom' ? 'flex-end' : 'flex-start';\n      }\n\n      if (maxHeight) {\n        styles.maxHeight = coerceCssPixelValue(maxHeight);\n      }\n\n      if (maxWidth) {\n        styles.maxWidth = coerceCssPixelValue(maxWidth);\n      }\n    }\n\n    this._lastBoundingBoxSize = boundingBoxRect;\n\n    extendStyles(this._boundingBox!.style, styles);\n  }\n\n  /** Resets the styles for the bounding box so that a new positioning can be computed. */\n  private _resetBoundingBoxStyles() {\n    extendStyles(this._boundingBox!.style, {\n      top: '0',\n      left: '0',\n      right: '0',\n      bottom: '0',\n      height: '',\n      width: '',\n      alignItems: '',\n      justifyContent: '',\n    } as CSSStyleDeclaration);\n  }\n\n  /** Resets the styles for the overlay pane so that a new positioning can be computed. */\n  private _resetOverlayElementStyles() {\n    extendStyles(this._pane.style, {\n      top: '',\n      left: '',\n      bottom: '',\n      right: '',\n      position: '',\n      transform: '',\n    } as CSSStyleDeclaration);\n  }\n\n  /** Sets positioning styles to the overlay element. */\n  private _setOverlayElementStyles(originPoint: Point, position: ConnectedPosition): void {\n    const styles = {} as CSSStyleDeclaration;\n    const hasExactPosition = this._hasExactPosition();\n    const hasFlexibleDimensions = this._hasFlexibleDimensions;\n    const config = this._overlayRef.getConfig();\n\n    if (hasExactPosition) {\n      const scrollPosition = this._viewportRuler.getViewportScrollPosition();\n      extendStyles(styles, this._getExactOverlayY(position, originPoint, scrollPosition));\n      extendStyles(styles, this._getExactOverlayX(position, originPoint, scrollPosition));\n    } else {\n      styles.position = 'static';\n    }\n\n    // Use a transform to apply the offsets. We do this because the `center` positions rely on\n    // being in the normal flex flow and setting a `top` / `left` at all will completely throw\n    // off the position. We also can't use margins, because they won't have an effect in some\n    // cases where the element doesn't have anything to \"push off of\". Finally, this works\n    // better both with flexible and non-flexible positioning.\n    let transformString = '';\n    let offsetX = this._getOffset(position, 'x');\n    let offsetY = this._getOffset(position, 'y');\n\n    if (offsetX) {\n      transformString += `translateX(${offsetX}px) `;\n    }\n\n    if (offsetY) {\n      transformString += `translateY(${offsetY}px)`;\n    }\n\n    styles.transform = transformString.trim();\n\n    // If a maxWidth or maxHeight is specified on the overlay, we remove them. We do this because\n    // we need these values to both be set to \"100%\" for the automatic flexible sizing to work.\n    // The maxHeight and maxWidth are set on the boundingBox in order to enforce the constraint.\n    // Note that this doesn't apply when we have an exact position, in which case we do want to\n    // apply them because they'll be cleared from the bounding box.\n    if (config.maxHeight) {\n      if (hasExactPosition) {\n        styles.maxHeight = coerceCssPixelValue(config.maxHeight);\n      } else if (hasFlexibleDimensions) {\n        styles.maxHeight = '';\n      }\n    }\n\n    if (config.maxWidth) {\n      if (hasExactPosition) {\n        styles.maxWidth = coerceCssPixelValue(config.maxWidth);\n      } else if (hasFlexibleDimensions) {\n        styles.maxWidth = '';\n      }\n    }\n\n    extendStyles(this._pane.style, styles);\n  }\n\n  /** Gets the exact top/bottom for the overlay when not using flexible sizing or when pushing. */\n  private _getExactOverlayY(\n    position: ConnectedPosition,\n    originPoint: Point,\n    scrollPosition: ViewportScrollPosition,\n  ) {\n    // Reset any existing styles. This is necessary in case the\n    // preferred position has changed since the last `apply`.\n    let styles = {top: '', bottom: ''} as CSSStyleDeclaration;\n    let overlayPoint = this._getOverlayPoint(originPoint, this._overlayRect, position);\n\n    if (this._isPushed) {\n      overlayPoint = this._pushOverlayOnScreen(overlayPoint, this._overlayRect, scrollPosition);\n    }\n\n    // We want to set either `top` or `bottom` based on whether the overlay wants to appear\n    // above or below the origin and the direction in which the element will expand.\n    if (position.overlayY === 'bottom') {\n      // When using `bottom`, we adjust the y position such that it is the distance\n      // from the bottom of the viewport rather than the top.\n      const documentHeight = this._document.documentElement!.clientHeight;\n      styles.bottom = `${documentHeight - (overlayPoint.y + this._overlayRect.height)}px`;\n    } else {\n      styles.top = coerceCssPixelValue(overlayPoint.y);\n    }\n\n    return styles;\n  }\n\n  /** Gets the exact left/right for the overlay when not using flexible sizing or when pushing. */\n  private _getExactOverlayX(\n    position: ConnectedPosition,\n    originPoint: Point,\n    scrollPosition: ViewportScrollPosition,\n  ) {\n    // Reset any existing styles. This is necessary in case the preferred position has\n    // changed since the last `apply`.\n    let styles = {left: '', right: ''} as CSSStyleDeclaration;\n    let overlayPoint = this._getOverlayPoint(originPoint, this._overlayRect, position);\n\n    if (this._isPushed) {\n      overlayPoint = this._pushOverlayOnScreen(overlayPoint, this._overlayRect, scrollPosition);\n    }\n\n    // We want to set either `left` or `right` based on whether the overlay wants to appear \"before\"\n    // or \"after\" the origin, which determines the direction in which the element will expand.\n    // For the horizontal axis, the meaning of \"before\" and \"after\" change based on whether the\n    // page is in RTL or LTR.\n    let horizontalStyleProperty: 'left' | 'right';\n\n    if (this._isRtl()) {\n      horizontalStyleProperty = position.overlayX === 'end' ? 'left' : 'right';\n    } else {\n      horizontalStyleProperty = position.overlayX === 'end' ? 'right' : 'left';\n    }\n\n    // When we're setting `right`, we adjust the x position such that it is the distance\n    // from the right edge of the viewport rather than the left edge.\n    if (horizontalStyleProperty === 'right') {\n      const documentWidth = this._document.documentElement!.clientWidth;\n      styles.right = `${documentWidth - (overlayPoint.x + this._overlayRect.width)}px`;\n    } else {\n      styles.left = coerceCssPixelValue(overlayPoint.x);\n    }\n\n    return styles;\n  }\n\n  /**\n   * Gets the view properties of the trigger and overlay, including whether they are clipped\n   * or completely outside the view of any of the strategy's scrollables.\n   */\n  private _getScrollVisibility(): ScrollingVisibility {\n    // Note: needs fresh rects since the position could've changed.\n    const originBounds = this._getOriginRect();\n    const overlayBounds = this._pane.getBoundingClientRect();\n\n    // TODO(jelbourn): instead of needing all of the client rects for these scrolling containers\n    // every time, we should be able to use the scrollTop of the containers if the size of those\n    // containers hasn't changed.\n    const scrollContainerBounds = this._scrollables.map(scrollable => {\n      return scrollable.getElementRef().nativeElement.getBoundingClientRect();\n    });\n\n    return {\n      isOriginClipped: isElementClippedByScrolling(originBounds, scrollContainerBounds),\n      isOriginOutsideView: isElementScrolledOutsideView(originBounds, scrollContainerBounds),\n      isOverlayClipped: isElementClippedByScrolling(overlayBounds, scrollContainerBounds),\n      isOverlayOutsideView: isElementScrolledOutsideView(overlayBounds, scrollContainerBounds),\n    };\n  }\n\n  /** Subtracts the amount that an element is overflowing on an axis from its length. */\n  private _subtractOverflows(length: number, ...overflows: number[]): number {\n    return overflows.reduce((currentValue: number, currentOverflow: number) => {\n      return currentValue - Math.max(currentOverflow, 0);\n    }, length);\n  }\n\n  /** Narrows the given viewport rect by the current _viewportMargin. */\n  private _getNarrowedViewportRect(): Dimensions {\n    // We recalculate the viewport rect here ourselves, rather than using the ViewportRuler,\n    // because we want to use the `clientWidth` and `clientHeight` as the base. The difference\n    // being that the client properties don't include the scrollbar, as opposed to `innerWidth`\n    // and `innerHeight` that do. This is necessary, because the overlay container uses\n    // 100% `width` and `height` which don't include the scrollbar either.\n    const width = this._document.documentElement!.clientWidth;\n    const height = this._document.documentElement!.clientHeight;\n    const scrollPosition = this._viewportRuler.getViewportScrollPosition();\n\n    return {\n      top: scrollPosition.top + this._viewportMargin,\n      left: scrollPosition.left + this._viewportMargin,\n      right: scrollPosition.left + width - this._viewportMargin,\n      bottom: scrollPosition.top + height - this._viewportMargin,\n      width: width - 2 * this._viewportMargin,\n      height: height - 2 * this._viewportMargin,\n    };\n  }\n\n  /** Whether the we're dealing with an RTL context */\n  private _isRtl() {\n    return this._overlayRef.getDirection() === 'rtl';\n  }\n\n  /** Determines whether the overlay uses exact or flexible positioning. */\n  private _hasExactPosition() {\n    return !this._hasFlexibleDimensions || this._isPushed;\n  }\n\n  /** Retrieves the offset of a position along the x or y axis. */\n  private _getOffset(position: ConnectedPosition, axis: 'x' | 'y') {\n    if (axis === 'x') {\n      // We don't do something like `position['offset' + axis]` in\n      // order to avoid breaking minifiers that rename properties.\n      return position.offsetX == null ? this._offsetX : position.offsetX;\n    }\n\n    return position.offsetY == null ? this._offsetY : position.offsetY;\n  }\n\n  /** Validates that the current position match the expected values. */\n  private _validatePositions(): void {\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      if (!this._preferredPositions.length) {\n        throw Error('FlexibleConnectedPositionStrategy: At least one position is required.');\n      }\n\n      // TODO(crisbeto): remove these once Angular's template type\n      // checking is advanced enough to catch these cases.\n      this._preferredPositions.forEach(pair => {\n        validateHorizontalPosition('originX', pair.originX);\n        validateVerticalPosition('originY', pair.originY);\n        validateHorizontalPosition('overlayX', pair.overlayX);\n        validateVerticalPosition('overlayY', pair.overlayY);\n      });\n    }\n  }\n\n  /** Adds a single CSS class or an array of classes on the overlay panel. */\n  private _addPanelClasses(cssClasses: string | string[]) {\n    if (this._pane) {\n      coerceArray(cssClasses).forEach(cssClass => {\n        if (cssClass !== '' && this._appliedPanelClasses.indexOf(cssClass) === -1) {\n          this._appliedPanelClasses.push(cssClass);\n          this._pane.classList.add(cssClass);\n        }\n      });\n    }\n  }\n\n  /** Clears the classes that the position strategy has applied from the overlay panel. */\n  private _clearPanelClasses() {\n    if (this._pane) {\n      this._appliedPanelClasses.forEach(cssClass => {\n        this._pane.classList.remove(cssClass);\n      });\n      this._appliedPanelClasses = [];\n    }\n  }\n\n  /** Returns the DOMRect of the current origin. */\n  private _getOriginRect(): Dimensions {\n    const origin = this._origin;\n\n    if (origin instanceof ElementRef) {\n      return origin.nativeElement.getBoundingClientRect();\n    }\n\n    // Check for Element so SVG elements are also supported.\n    if (origin instanceof Element) {\n      return origin.getBoundingClientRect();\n    }\n\n    const width = origin.width || 0;\n    const height = origin.height || 0;\n\n    // If the origin is a point, return a client rect as if it was a 0x0 element at the point.\n    return {\n      top: origin.y,\n      bottom: origin.y + height,\n      left: origin.x,\n      right: origin.x + width,\n      height,\n      width,\n    };\n  }\n}\n\n/** A simple (x, y) coordinate. */\ninterface Point {\n  x: number;\n  y: number;\n}\n\n/** Record of measurements for how an overlay (at a given position) fits into the viewport. */\ninterface OverlayFit {\n  /** Whether the overlay fits completely in the viewport. */\n  isCompletelyWithinViewport: boolean;\n\n  /** Whether the overlay fits in the viewport on the y-axis. */\n  fitsInViewportVertically: boolean;\n\n  /** Whether the overlay fits in the viewport on the x-axis. */\n  fitsInViewportHorizontally: boolean;\n\n  /** The total visible area (in px^2) of the overlay inside the viewport. */\n  visibleArea: number;\n}\n\n/** Record of the measurements determining whether an overlay will fit in a specific position. */\ninterface FallbackPosition {\n  position: ConnectedPosition;\n  originPoint: Point;\n  overlayPoint: Point;\n  overlayFit: OverlayFit;\n  overlayRect: Dimensions;\n}\n\n/** Position and size of the overlay sizing wrapper for a specific position. */\ninterface BoundingBoxRect {\n  top: number;\n  left: number;\n  bottom: number;\n  right: number;\n  height: number;\n  width: number;\n}\n\n/** Record of measures determining how well a given position will fit with flexible dimensions. */\ninterface FlexibleFit {\n  position: ConnectedPosition;\n  origin: Point;\n  overlayRect: Dimensions;\n  boundingBoxRect: BoundingBoxRect;\n}\n\n/** A connected position as specified by the user. */\nexport interface ConnectedPosition {\n  originX: 'start' | 'center' | 'end';\n  originY: 'top' | 'center' | 'bottom';\n\n  overlayX: 'start' | 'center' | 'end';\n  overlayY: 'top' | 'center' | 'bottom';\n\n  weight?: number;\n  offsetX?: number;\n  offsetY?: number;\n  panelClass?: string | string[];\n}\n\n/** Shallow-extends a stylesheet object with another stylesheet object. */\nfunction extendStyles(\n  destination: CSSStyleDeclaration,\n  source: CSSStyleDeclaration,\n): CSSStyleDeclaration {\n  for (let key in source) {\n    if (source.hasOwnProperty(key)) {\n      destination[key] = source[key];\n    }\n  }\n\n  return destination;\n}\n\n/**\n * Extracts the pixel value as a number from a value, if it's a number\n * or a CSS pixel string (e.g. `1337px`). Otherwise returns null.\n */\nfunction getPixelValue(input: number | string | null | undefined): number | null {\n  if (typeof input !== 'number' && input != null) {\n    const [value, units] = input.split(cssUnitPattern);\n    return !units || units === 'px' ? parseFloat(value) : null;\n  }\n\n  return input || null;\n}\n\n/**\n * Gets a version of an element's bounding `DOMRect` where all the values are rounded down to\n * the nearest pixel. This allows us to account for the cases where there may be sub-pixel\n * deviations in the `DOMRect` returned by the browser (e.g. when zoomed in with a percentage\n * size, see #21350).\n */\nfunction getRoundedBoundingClientRect(clientRect: Dimensions): Dimensions {\n  return {\n    top: Math.floor(clientRect.top),\n    right: Math.floor(clientRect.right),\n    bottom: Math.floor(clientRect.bottom),\n    left: Math.floor(clientRect.left),\n    width: Math.floor(clientRect.width),\n    height: Math.floor(clientRect.height),\n  };\n}\n\n/** Returns whether two `ScrollingVisibility` objects are identical. */\nfunction compareScrollVisibility(a: ScrollingVisibility, b: ScrollingVisibility): boolean {\n  if (a === b) {\n    return true;\n  }\n\n  return (\n    a.isOriginClipped === b.isOriginClipped &&\n    a.isOriginOutsideView === b.isOriginOutsideView &&\n    a.isOverlayClipped === b.isOverlayClipped &&\n    a.isOverlayOutsideView === b.isOverlayOutsideView\n  );\n}\n\nexport const STANDARD_DROPDOWN_BELOW_POSITIONS: ConnectedPosition[] = [\n  {originX: 'start', originY: 'bottom', overlayX: 'start', overlayY: 'top'},\n  {originX: 'start', originY: 'top', overlayX: 'start', overlayY: 'bottom'},\n  {originX: 'end', originY: 'bottom', overlayX: 'end', overlayY: 'top'},\n  {originX: 'end', originY: 'top', overlayX: 'end', overlayY: 'bottom'},\n];\n\nexport const STANDARD_DROPDOWN_ADJACENT_POSITIONS: ConnectedPosition[] = [\n  {originX: 'end', originY: 'top', overlayX: 'start', overlayY: 'top'},\n  {originX: 'end', originY: 'bottom', overlayX: 'start', overlayY: 'bottom'},\n  {originX: 'start', originY: 'top', overlayX: 'end', overlayY: 'top'},\n  {originX: 'start', originY: 'bottom', overlayX: 'end', overlayY: 'bottom'},\n];\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {OverlayRef} from '../overlay-ref';\nimport {PositionStrategy} from './position-strategy';\n\n/** Class to be added to the overlay pane wrapper. */\nconst wrapperClass = 'cdk-global-overlay-wrapper';\n\n/**\n * A strategy for positioning overlays. Using this strategy, an overlay is given an\n * explicit position relative to the browser's viewport. We use flexbox, instead of\n * transforms, in order to avoid issues with subpixel rendering which can cause the\n * element to become blurry.\n */\nexport class GlobalPositionStrategy implements PositionStrategy {\n  /** The overlay to which this strategy is attached. */\n  private _overlayRef: OverlayRef;\n  private _cssPosition = 'static';\n  private _topOffset = '';\n  private _bottomOffset = '';\n  private _alignItems = '';\n  private _xPosition = '';\n  private _xOffset = '';\n  private _width = '';\n  private _height = '';\n  private _isDisposed = false;\n\n  attach(overlayRef: OverlayRef): void {\n    const config = overlayRef.getConfig();\n\n    this._overlayRef = overlayRef;\n\n    if (this._width && !config.width) {\n      overlayRef.updateSize({width: this._width});\n    }\n\n    if (this._height && !config.height) {\n      overlayRef.updateSize({height: this._height});\n    }\n\n    overlayRef.hostElement.classList.add(wrapperClass);\n    this._isDisposed = false;\n  }\n\n  /**\n   * Sets the top position of the overlay. Clears any previously set vertical position.\n   * @param value New top offset.\n   */\n  top(value: string = ''): this {\n    this._bottomOffset = '';\n    this._topOffset = value;\n    this._alignItems = 'flex-start';\n    return this;\n  }\n\n  /**\n   * Sets the left position of the overlay. Clears any previously set horizontal position.\n   * @param value New left offset.\n   */\n  left(value: string = ''): this {\n    this._xOffset = value;\n    this._xPosition = 'left';\n    return this;\n  }\n\n  /**\n   * Sets the bottom position of the overlay. Clears any previously set vertical position.\n   * @param value New bottom offset.\n   */\n  bottom(value: string = ''): this {\n    this._topOffset = '';\n    this._bottomOffset = value;\n    this._alignItems = 'flex-end';\n    return this;\n  }\n\n  /**\n   * Sets the right position of the overlay. Clears any previously set horizontal position.\n   * @param value New right offset.\n   */\n  right(value: string = ''): this {\n    this._xOffset = value;\n    this._xPosition = 'right';\n    return this;\n  }\n\n  /**\n   * Sets the overlay to the start of the viewport, depending on the overlay direction.\n   * This will be to the left in LTR layouts and to the right in RTL.\n   * @param offset Offset from the edge of the screen.\n   */\n  start(value: string = ''): this {\n    this._xOffset = value;\n    this._xPosition = 'start';\n    return this;\n  }\n\n  /**\n   * Sets the overlay to the end of the viewport, depending on the overlay direction.\n   * This will be to the right in LTR layouts and to the left in RTL.\n   * @param offset Offset from the edge of the screen.\n   */\n  end(value: string = ''): this {\n    this._xOffset = value;\n    this._xPosition = 'end';\n    return this;\n  }\n\n  /**\n   * Sets the overlay width and clears any previously set width.\n   * @param value New width for the overlay\n   * @deprecated Pass the `width` through the `OverlayConfig`.\n   * @breaking-change 8.0.0\n   */\n  width(value: string = ''): this {\n    if (this._overlayRef) {\n      this._overlayRef.updateSize({width: value});\n    } else {\n      this._width = value;\n    }\n\n    return this;\n  }\n\n  /**\n   * Sets the overlay height and clears any previously set height.\n   * @param value New height for the overlay\n   * @deprecated Pass the `height` through the `OverlayConfig`.\n   * @breaking-change 8.0.0\n   */\n  height(value: string = ''): this {\n    if (this._overlayRef) {\n      this._overlayRef.updateSize({height: value});\n    } else {\n      this._height = value;\n    }\n\n    return this;\n  }\n\n  /**\n   * Centers the overlay horizontally with an optional offset.\n   * Clears any previously set horizontal position.\n   *\n   * @param offset Overlay offset from the horizontal center.\n   */\n  centerHorizontally(offset: string = ''): this {\n    this.left(offset);\n    this._xPosition = 'center';\n    return this;\n  }\n\n  /**\n   * Centers the overlay vertically with an optional offset.\n   * Clears any previously set vertical position.\n   *\n   * @param offset Overlay offset from the vertical center.\n   */\n  centerVertically(offset: string = ''): this {\n    this.top(offset);\n    this._alignItems = 'center';\n    return this;\n  }\n\n  /**\n   * Apply the position to the element.\n   * @docs-private\n   */\n  apply(): void {\n    // Since the overlay ref applies the strategy asynchronously, it could\n    // have been disposed before it ends up being applied. If that is the\n    // case, we shouldn't do anything.\n    if (!this._overlayRef || !this._overlayRef.hasAttached()) {\n      return;\n    }\n\n    const styles = this._overlayRef.overlayElement.style;\n    const parentStyles = this._overlayRef.hostElement.style;\n    const config = this._overlayRef.getConfig();\n    const {width, height, maxWidth, maxHeight} = config;\n    const shouldBeFlushHorizontally =\n      (width === '100%' || width === '100vw') &&\n      (!maxWidth || maxWidth === '100%' || maxWidth === '100vw');\n    const shouldBeFlushVertically =\n      (height === '100%' || height === '100vh') &&\n      (!maxHeight || maxHeight === '100%' || maxHeight === '100vh');\n    const xPosition = this._xPosition;\n    const xOffset = this._xOffset;\n    const isRtl = this._overlayRef.getConfig().direction === 'rtl';\n    let marginLeft = '';\n    let marginRight = '';\n    let justifyContent = '';\n\n    if (shouldBeFlushHorizontally) {\n      justifyContent = 'flex-start';\n    } else if (xPosition === 'center') {\n      justifyContent = 'center';\n\n      if (isRtl) {\n        marginRight = xOffset;\n      } else {\n        marginLeft = xOffset;\n      }\n    } else if (isRtl) {\n      if (xPosition === 'left' || xPosition === 'end') {\n        justifyContent = 'flex-end';\n        marginLeft = xOffset;\n      } else if (xPosition === 'right' || xPosition === 'start') {\n        justifyContent = 'flex-start';\n        marginRight = xOffset;\n      }\n    } else if (xPosition === 'left' || xPosition === 'start') {\n      justifyContent = 'flex-start';\n      marginLeft = xOffset;\n    } else if (xPosition === 'right' || xPosition === 'end') {\n      justifyContent = 'flex-end';\n      marginRight = xOffset;\n    }\n\n    styles.position = this._cssPosition;\n    styles.marginLeft = shouldBeFlushHorizontally ? '0' : marginLeft;\n    styles.marginTop = shouldBeFlushVertically ? '0' : this._topOffset;\n    styles.marginBottom = this._bottomOffset;\n    styles.marginRight = shouldBeFlushHorizontally ? '0' : marginRight;\n    parentStyles.justifyContent = justifyContent;\n    parentStyles.alignItems = shouldBeFlushVertically ? 'flex-start' : this._alignItems;\n  }\n\n  /**\n   * Cleans up the DOM changes from the position strategy.\n   * @docs-private\n   */\n  dispose(): void {\n    if (this._isDisposed || !this._overlayRef) {\n      return;\n    }\n\n    const styles = this._overlayRef.overlayElement.style;\n    const parent = this._overlayRef.hostElement;\n    const parentStyles = parent.style;\n\n    parent.classList.remove(wrapperClass);\n    parentStyles.justifyContent =\n      parentStyles.alignItems =\n      styles.marginTop =\n      styles.marginBottom =\n      styles.marginLeft =\n      styles.marginRight =\n      styles.position =\n        '';\n\n    this._overlayRef = null!;\n    this._isDisposed = true;\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {Platform} from '../../platform';\nimport {ViewportRuler} from '../../scrolling';\nimport {DOCUMENT} from '@angular/common';\nimport {Injectable, inject} from '@angular/core';\nimport {OverlayContainer} from '../overlay-container';\nimport {\n  FlexibleConnectedPositionStrategy,\n  FlexibleConnectedPositionStrategyOrigin,\n} from './flexible-connected-position-strategy';\nimport {GlobalPositionStrategy} from './global-position-strategy';\n\n/** Builder for overlay position strategy. */\n@Injectable({providedIn: 'root'})\nexport class OverlayPositionBuilder {\n  private _viewportRuler = inject(ViewportRuler);\n  private _document = inject(DOCUMENT);\n  private _platform = inject(Platform);\n  private _overlayContainer = inject(OverlayContainer);\n\n  constructor(...args: unknown[]);\n  constructor() {}\n\n  /**\n   * Creates a global position strategy.\n   */\n  global(): GlobalPositionStrategy {\n    return new GlobalPositionStrategy();\n  }\n\n  /**\n   * Creates a flexible position strategy.\n   * @param origin Origin relative to which to position the overlay.\n   */\n  flexibleConnectedTo(\n    origin: FlexibleConnectedPositionStrategyOrigin,\n  ): FlexibleConnectedPositionStrategy {\n    return new FlexibleConnectedPositionStrategy(\n      origin,\n      this._viewportRuler,\n      this._document,\n      this._platform,\n      this._overlayContainer,\n    );\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {Directionality} from '../bidi';\nimport {DomPortalOutlet} from '../portal';\nimport {DOCUMENT, Location} from '@angular/common';\nimport {\n  ApplicationRef,\n  Injectable,\n  Injector,\n  NgZone,\n  ANIMATION_MODULE_TYPE,\n  EnvironmentInjector,\n  inject,\n  RendererFactory2,\n} from '@angular/core';\nimport {_IdGenerator} from '../a11y';\nimport {_CdkPrivateStyleLoader} from '../private';\nimport {OverlayKeyboardDispatcher} from './dispatchers/overlay-keyboard-dispatcher';\nimport {OverlayOutsideClickDispatcher} from './dispatchers/overlay-outside-click-dispatcher';\nimport {OverlayConfig} from './overlay-config';\nimport {_CdkOverlayStyleLoader, OverlayContainer} from './overlay-container';\nimport {OverlayRef} from './overlay-ref';\nimport {OverlayPositionBuilder} from './position/overlay-position-builder';\nimport {ScrollStrategyOptions} from './scroll/index';\n\n/**\n * Service to create Overlays. Overlays are dynamically added pieces of floating UI, meant to be\n * used as a low-level building block for other components. Dialogs, tooltips, menus,\n * selects, etc. can all be built using overlays. The service should primarily be used by authors\n * of re-usable components rather than developers building end-user applications.\n *\n * An overlay *is* a PortalOutlet, so any kind of Portal can be loaded into one.\n */\n@Injectable({providedIn: 'root'})\nexport class Overlay {\n  scrollStrategies = inject(ScrollStrategyOptions);\n  private _overlayContainer = inject(OverlayContainer);\n  private _positionBuilder = inject(OverlayPositionBuilder);\n  private _keyboardDispatcher = inject(OverlayKeyboardDispatcher);\n  private _injector = inject(Injector);\n  private _ngZone = inject(NgZone);\n  private _document = inject(DOCUMENT);\n  private _directionality = inject(Directionality);\n  private _location = inject(Location);\n  private _outsideClickDispatcher = inject(OverlayOutsideClickDispatcher);\n  private _animationsModuleType = inject(ANIMATION_MODULE_TYPE, {optional: true});\n  private _idGenerator = inject(_IdGenerator);\n  private _renderer = inject(RendererFactory2).createRenderer(null, null);\n\n  private _appRef: ApplicationRef;\n  private _styleLoader = inject(_CdkPrivateStyleLoader);\n\n  constructor(...args: unknown[]);\n  constructor() {}\n\n  /**\n   * Creates an overlay.\n   * @param config Configuration applied to the overlay.\n   * @returns Reference to the created overlay.\n   */\n  create(config?: OverlayConfig): OverlayRef {\n    // This is done in the overlay container as well, but we have it here\n    // since it's common to mock out the overlay container in tests.\n    this._styleLoader.load(_CdkOverlayStyleLoader);\n\n    const host = this._createHostElement();\n    const pane = this._createPaneElement(host);\n    const portalOutlet = this._createPortalOutlet(pane);\n    const overlayConfig = new OverlayConfig(config);\n\n    overlayConfig.direction = overlayConfig.direction || this._directionality.value;\n\n    return new OverlayRef(\n      portalOutlet,\n      host,\n      pane,\n      overlayConfig,\n      this._ngZone,\n      this._keyboardDispatcher,\n      this._document,\n      this._location,\n      this._outsideClickDispatcher,\n      this._animationsModuleType === 'NoopAnimations',\n      this._injector.get(EnvironmentInjector),\n      this._renderer,\n    );\n  }\n\n  /**\n   * Gets a position builder that can be used, via fluent API,\n   * to construct and configure a position strategy.\n   * @returns An overlay position builder.\n   */\n  position(): OverlayPositionBuilder {\n    return this._positionBuilder;\n  }\n\n  /**\n   * Creates the DOM element for an overlay and appends it to the overlay container.\n   * @returns Newly-created pane element\n   */\n  private _createPaneElement(host: HTMLElement): HTMLElement {\n    const pane = this._document.createElement('div');\n\n    pane.id = this._idGenerator.getId('cdk-overlay-');\n    pane.classList.add('cdk-overlay-pane');\n    host.appendChild(pane);\n\n    return pane;\n  }\n\n  /**\n   * Creates the host element that wraps around an overlay\n   * and can be used for advanced positioning.\n   * @returns Newly-create host element.\n   */\n  private _createHostElement(): HTMLElement {\n    const host = this._document.createElement('div');\n    this._overlayContainer.getContainerElement().appendChild(host);\n    return host;\n  }\n\n  /**\n   * Create a DomPortalOutlet into which the overlay content can be loaded.\n   * @param pane The DOM element to turn into a portal outlet.\n   * @returns A portal outlet for the given DOM element.\n   */\n  private _createPortalOutlet(pane: HTMLElement): DomPortalOutlet {\n    // We have to resolve the ApplicationRef later in order to allow people\n    // to use overlay-based providers during app initialization.\n    if (!this._appRef) {\n      this._appRef = this._injector.get<ApplicationRef>(ApplicationRef);\n    }\n\n    return new DomPortalOutlet(pane, null, this._appRef, this._injector, this._document);\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {Direction, Directionality} from '../bidi';\nimport {ESCAPE, hasModifierKey} from '../keycodes';\nimport {TemplatePortal} from '../portal';\nimport {\n  Directive,\n  ElementRef,\n  EventEmitter,\n  InjectionToken,\n  Input,\n  NgZone,\n  OnChanges,\n  OnDestroy,\n  Output,\n  SimpleChanges,\n  TemplateRef,\n  ViewContainerRef,\n  booleanAttribute,\n  inject,\n} from '@angular/core';\nimport {_getEventTarget} from '../platform';\nimport {Subscription} from 'rxjs';\nimport {takeWhile} from 'rxjs/operators';\nimport {Overlay} from './overlay';\nimport {OverlayConfig} from './overlay-config';\nimport {OverlayRef} from './overlay-ref';\nimport {ConnectedOverlayPositionChange} from './position/connected-position';\nimport {\n  ConnectedPosition,\n  FlexibleConnectedPositionStrategy,\n  FlexibleConnectedPositionStrategyOrigin,\n} from './position/flexible-connected-position-strategy';\nimport {RepositionScrollStrategy, ScrollStrategy} from './scroll/index';\n\n/** Default set of positions for the overlay. Follows the behavior of a dropdown. */\nconst defaultPositionList: ConnectedPosition[] = [\n  {\n    originX: 'start',\n    originY: 'bottom',\n    overlayX: 'start',\n    overlayY: 'top',\n  },\n  {\n    originX: 'start',\n    originY: 'top',\n    overlayX: 'start',\n    overlayY: 'bottom',\n  },\n  {\n    originX: 'end',\n    originY: 'top',\n    overlayX: 'end',\n    overlayY: 'bottom',\n  },\n  {\n    originX: 'end',\n    originY: 'bottom',\n    overlayX: 'end',\n    overlayY: 'top',\n  },\n];\n\n/** Injection token that determines the scroll handling while the connected overlay is open. */\nexport const CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY = new InjectionToken<() => ScrollStrategy>(\n  'cdk-connected-overlay-scroll-strategy',\n  {\n    providedIn: 'root',\n    factory: () => {\n      const overlay = inject(Overlay);\n      return () => overlay.scrollStrategies.reposition();\n    },\n  },\n);\n\n/**\n * Directive applied to an element to make it usable as an origin for an Overlay using a\n * ConnectedPositionStrategy.\n */\n@Directive({\n  selector: '[cdk-overlay-origin], [overlay-origin], [cdkOverlayOrigin]',\n  exportAs: 'cdkOverlayOrigin',\n})\nexport class CdkOverlayOrigin {\n  elementRef = inject(ElementRef);\n\n  constructor(...args: unknown[]);\n  constructor() {}\n}\n\n/**\n * Directive to facilitate declarative creation of an\n * Overlay using a FlexibleConnectedPositionStrategy.\n */\n@Directive({\n  selector: '[cdk-connected-overlay], [connected-overlay], [cdkConnectedOverlay]',\n  exportAs: 'cdkConnectedOverlay',\n})\nexport class CdkConnectedOverlay implements OnDestroy, OnChanges {\n  private _overlay = inject(Overlay);\n  private _dir = inject(Directionality, {optional: true});\n\n  private _overlayRef: OverlayRef | undefined;\n  private _templatePortal: TemplatePortal;\n  private _backdropSubscription = Subscription.EMPTY;\n  private _attachSubscription = Subscription.EMPTY;\n  private _detachSubscription = Subscription.EMPTY;\n  private _positionSubscription = Subscription.EMPTY;\n  private _offsetX: number;\n  private _offsetY: number;\n  private _position: FlexibleConnectedPositionStrategy;\n  private _scrollStrategyFactory = inject(CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY);\n  private _disposeOnNavigation = false;\n  private _ngZone = inject(NgZone);\n\n  /** Origin for the connected overlay. */\n  @Input('cdkConnectedOverlayOrigin')\n  origin: CdkOverlayOrigin | FlexibleConnectedPositionStrategyOrigin;\n\n  /** Registered connected position pairs. */\n  @Input('cdkConnectedOverlayPositions') positions: ConnectedPosition[];\n\n  /**\n   * This input overrides the positions input if specified. It lets users pass\n   * in arbitrary positioning strategies.\n   */\n  @Input('cdkConnectedOverlayPositionStrategy') positionStrategy: FlexibleConnectedPositionStrategy;\n\n  /** The offset in pixels for the overlay connection point on the x-axis */\n  @Input('cdkConnectedOverlayOffsetX')\n  get offsetX(): number {\n    return this._offsetX;\n  }\n  set offsetX(offsetX: number) {\n    this._offsetX = offsetX;\n\n    if (this._position) {\n      this._updatePositionStrategy(this._position);\n    }\n  }\n\n  /** The offset in pixels for the overlay connection point on the y-axis */\n  @Input('cdkConnectedOverlayOffsetY')\n  get offsetY() {\n    return this._offsetY;\n  }\n  set offsetY(offsetY: number) {\n    this._offsetY = offsetY;\n\n    if (this._position) {\n      this._updatePositionStrategy(this._position);\n    }\n  }\n\n  /** The width of the overlay panel. */\n  @Input('cdkConnectedOverlayWidth') width: number | string;\n\n  /** The height of the overlay panel. */\n  @Input('cdkConnectedOverlayHeight') height: number | string;\n\n  /** The min width of the overlay panel. */\n  @Input('cdkConnectedOverlayMinWidth') minWidth: number | string;\n\n  /** The min height of the overlay panel. */\n  @Input('cdkConnectedOverlayMinHeight') minHeight: number | string;\n\n  /** The custom class to be set on the backdrop element. */\n  @Input('cdkConnectedOverlayBackdropClass') backdropClass: string | string[];\n\n  /** The custom class to add to the overlay pane element. */\n  @Input('cdkConnectedOverlayPanelClass') panelClass: string | string[];\n\n  /** Margin between the overlay and the viewport edges. */\n  @Input('cdkConnectedOverlayViewportMargin') viewportMargin: number = 0;\n\n  /** Strategy to be used when handling scroll events while the overlay is open. */\n  @Input('cdkConnectedOverlayScrollStrategy') scrollStrategy: ScrollStrategy;\n\n  /** Whether the overlay is open. */\n  @Input('cdkConnectedOverlayOpen') open: boolean = false;\n\n  /** Whether the overlay can be closed by user interaction. */\n  @Input('cdkConnectedOverlayDisableClose') disableClose: boolean = false;\n\n  /** CSS selector which to set the transform origin. */\n  @Input('cdkConnectedOverlayTransformOriginOn') transformOriginSelector: string;\n\n  /** Whether or not the overlay should attach a backdrop. */\n  @Input({alias: 'cdkConnectedOverlayHasBackdrop', transform: booleanAttribute})\n  hasBackdrop: boolean = false;\n\n  /** Whether or not the overlay should be locked when scrolling. */\n  @Input({alias: 'cdkConnectedOverlayLockPosition', transform: booleanAttribute})\n  lockPosition: boolean = false;\n\n  /** Whether the overlay's width and height can be constrained to fit within the viewport. */\n  @Input({alias: 'cdkConnectedOverlayFlexibleDimensions', transform: booleanAttribute})\n  flexibleDimensions: boolean = false;\n\n  /** Whether the overlay can grow after the initial open when flexible positioning is turned on. */\n  @Input({alias: 'cdkConnectedOverlayGrowAfterOpen', transform: booleanAttribute})\n  growAfterOpen: boolean = false;\n\n  /** Whether the overlay can be pushed on-screen if none of the provided positions fit. */\n  @Input({alias: 'cdkConnectedOverlayPush', transform: booleanAttribute}) push: boolean = false;\n\n  /** Whether the overlay should be disposed of when the user goes backwards/forwards in history. */\n  @Input({alias: 'cdkConnectedOverlayDisposeOnNavigation', transform: booleanAttribute})\n  get disposeOnNavigation(): boolean {\n    return this._disposeOnNavigation;\n  }\n  set disposeOnNavigation(value: boolean) {\n    this._disposeOnNavigation = value;\n  }\n\n  /** Event emitted when the backdrop is clicked. */\n  @Output() readonly backdropClick = new EventEmitter<MouseEvent>();\n\n  /** Event emitted when the position has changed. */\n  @Output() readonly positionChange = new EventEmitter<ConnectedOverlayPositionChange>();\n\n  /** Event emitted when the overlay has been attached. */\n  @Output() readonly attach = new EventEmitter<void>();\n\n  /** Event emitted when the overlay has been detached. */\n  @Output() readonly detach = new EventEmitter<void>();\n\n  /** Emits when there are keyboard events that are targeted at the overlay. */\n  @Output() readonly overlayKeydown = new EventEmitter<KeyboardEvent>();\n\n  /** Emits when there are mouse outside click events that are targeted at the overlay. */\n  @Output() readonly overlayOutsideClick = new EventEmitter<MouseEvent>();\n\n  constructor(...args: unknown[]);\n\n  // TODO(jelbourn): inputs for size, scroll behavior, animation, etc.\n\n  constructor() {\n    const templateRef = inject<TemplateRef<any>>(TemplateRef);\n    const viewContainerRef = inject(ViewContainerRef);\n\n    this._templatePortal = new TemplatePortal(templateRef, viewContainerRef);\n    this.scrollStrategy = this._scrollStrategyFactory();\n  }\n\n  /** The associated overlay reference. */\n  get overlayRef(): OverlayRef {\n    return this._overlayRef!;\n  }\n\n  /** The element's layout direction. */\n  get dir(): Direction {\n    return this._dir ? this._dir.value : 'ltr';\n  }\n\n  ngOnDestroy() {\n    this._attachSubscription.unsubscribe();\n    this._detachSubscription.unsubscribe();\n    this._backdropSubscription.unsubscribe();\n    this._positionSubscription.unsubscribe();\n    this._overlayRef?.dispose();\n  }\n\n  ngOnChanges(changes: SimpleChanges) {\n    if (this._position) {\n      this._updatePositionStrategy(this._position);\n      this._overlayRef?.updateSize({\n        width: this.width,\n        minWidth: this.minWidth,\n        height: this.height,\n        minHeight: this.minHeight,\n      });\n\n      if (changes['origin'] && this.open) {\n        this._position.apply();\n      }\n    }\n\n    if (changes['open']) {\n      this.open ? this.attachOverlay() : this.detachOverlay();\n    }\n  }\n\n  /** Creates an overlay */\n  private _createOverlay() {\n    if (!this.positions || !this.positions.length) {\n      this.positions = defaultPositionList;\n    }\n\n    const overlayRef = (this._overlayRef = this._overlay.create(this._buildConfig()));\n    this._attachSubscription = overlayRef.attachments().subscribe(() => this.attach.emit());\n    this._detachSubscription = overlayRef.detachments().subscribe(() => this.detach.emit());\n    overlayRef.keydownEvents().subscribe((event: KeyboardEvent) => {\n      this.overlayKeydown.next(event);\n\n      if (event.keyCode === ESCAPE && !this.disableClose && !hasModifierKey(event)) {\n        event.preventDefault();\n        this.detachOverlay();\n      }\n    });\n\n    this._overlayRef.outsidePointerEvents().subscribe((event: MouseEvent) => {\n      const origin = this._getOriginElement();\n      const target = _getEventTarget(event) as Element | null;\n\n      if (!origin || (origin !== target && !origin.contains(target))) {\n        this.overlayOutsideClick.next(event);\n      }\n    });\n  }\n\n  /** Builds the overlay config based on the directive's inputs */\n  private _buildConfig(): OverlayConfig {\n    const positionStrategy = (this._position =\n      this.positionStrategy || this._createPositionStrategy());\n    const overlayConfig = new OverlayConfig({\n      direction: this._dir || 'ltr',\n      positionStrategy,\n      scrollStrategy: this.scrollStrategy,\n      hasBackdrop: this.hasBackdrop,\n      disposeOnNavigation: this.disposeOnNavigation,\n    });\n\n    if (this.width || this.width === 0) {\n      overlayConfig.width = this.width;\n    }\n\n    if (this.height || this.height === 0) {\n      overlayConfig.height = this.height;\n    }\n\n    if (this.minWidth || this.minWidth === 0) {\n      overlayConfig.minWidth = this.minWidth;\n    }\n\n    if (this.minHeight || this.minHeight === 0) {\n      overlayConfig.minHeight = this.minHeight;\n    }\n\n    if (this.backdropClass) {\n      overlayConfig.backdropClass = this.backdropClass;\n    }\n\n    if (this.panelClass) {\n      overlayConfig.panelClass = this.panelClass;\n    }\n\n    return overlayConfig;\n  }\n\n  /** Updates the state of a position strategy, based on the values of the directive inputs. */\n  private _updatePositionStrategy(positionStrategy: FlexibleConnectedPositionStrategy) {\n    const positions: ConnectedPosition[] = this.positions.map(currentPosition => ({\n      originX: currentPosition.originX,\n      originY: currentPosition.originY,\n      overlayX: currentPosition.overlayX,\n      overlayY: currentPosition.overlayY,\n      offsetX: currentPosition.offsetX || this.offsetX,\n      offsetY: currentPosition.offsetY || this.offsetY,\n      panelClass: currentPosition.panelClass || undefined,\n    }));\n\n    return positionStrategy\n      .setOrigin(this._getOrigin())\n      .withPositions(positions)\n      .withFlexibleDimensions(this.flexibleDimensions)\n      .withPush(this.push)\n      .withGrowAfterOpen(this.growAfterOpen)\n      .withViewportMargin(this.viewportMargin)\n      .withLockedPosition(this.lockPosition)\n      .withTransformOriginOn(this.transformOriginSelector);\n  }\n\n  /** Returns the position strategy of the overlay to be set on the overlay config */\n  private _createPositionStrategy(): FlexibleConnectedPositionStrategy {\n    const strategy = this._overlay.position().flexibleConnectedTo(this._getOrigin());\n    this._updatePositionStrategy(strategy);\n    return strategy;\n  }\n\n  private _getOrigin(): FlexibleConnectedPositionStrategyOrigin {\n    if (this.origin instanceof CdkOverlayOrigin) {\n      return this.origin.elementRef;\n    } else {\n      return this.origin;\n    }\n  }\n\n  private _getOriginElement(): Element | null {\n    if (this.origin instanceof CdkOverlayOrigin) {\n      return this.origin.elementRef.nativeElement;\n    }\n\n    if (this.origin instanceof ElementRef) {\n      return this.origin.nativeElement;\n    }\n\n    if (typeof Element !== 'undefined' && this.origin instanceof Element) {\n      return this.origin;\n    }\n\n    return null;\n  }\n\n  /** Attaches the overlay. */\n  attachOverlay() {\n    if (!this._overlayRef) {\n      this._createOverlay();\n    } else {\n      // Update the overlay size, in case the directive's inputs have changed\n      this._overlayRef.getConfig().hasBackdrop = this.hasBackdrop;\n    }\n\n    if (!this._overlayRef!.hasAttached()) {\n      this._overlayRef!.attach(this._templatePortal);\n    }\n\n    if (this.hasBackdrop) {\n      this._backdropSubscription = this._overlayRef!.backdropClick().subscribe(event => {\n        this.backdropClick.emit(event);\n      });\n    } else {\n      this._backdropSubscription.unsubscribe();\n    }\n\n    this._positionSubscription.unsubscribe();\n\n    // Only subscribe to `positionChanges` if requested, because putting\n    // together all the information for it can be expensive.\n    if (this.positionChange.observers.length > 0) {\n      this._positionSubscription = this._position.positionChanges\n        .pipe(takeWhile(() => this.positionChange.observers.length > 0))\n        .subscribe(position => {\n          this._ngZone.run(() => this.positionChange.emit(position));\n\n          if (this.positionChange.observers.length === 0) {\n            this._positionSubscription.unsubscribe();\n          }\n        });\n    }\n\n    this.open = true;\n  }\n\n  /** Detaches the overlay. */\n  detachOverlay() {\n    this._overlayRef?.detach();\n    this._backdropSubscription.unsubscribe();\n    this._positionSubscription.unsubscribe();\n    this.open = false;\n  }\n}\n\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nexport function CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY_PROVIDER_FACTORY(\n  overlay: Overlay,\n): () => RepositionScrollStrategy {\n  return () => overlay.scrollStrategies.reposition();\n}\n\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nexport const CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY_PROVIDER = {\n  provide: CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY,\n  deps: [Overlay],\n  useFactory: CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY_PROVIDER_FACTORY,\n};\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {BidiModule} from '../bidi';\nimport {PortalModule} from '../portal';\nimport {ScrollingModule} from '../scrolling';\nimport {NgModule} from '@angular/core';\nimport {Overlay} from './overlay';\nimport {\n  CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY_PROVIDER,\n  CdkConnectedOverlay,\n  CdkOverlayOrigin,\n} from './overlay-directives';\n\n@NgModule({\n  imports: [BidiModule, PortalModule, ScrollingModule, CdkConnectedOverlay, CdkOverlayOrigin],\n  exports: [CdkConnectedOverlay, CdkOverlayOrigin, ScrollingModule],\n  providers: [Overlay, CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY_PROVIDER],\n})\nexport class OverlayModule {}\n\n// Re-export needed by the Angular compiler.\n// See: https://github.com/angular/components/issues/30663.\n// Note: These exports need to be stable and shouldn't be renamed unnecessarily because\n// consuming libraries might have references to them in their own partial compilation output.\nexport {\n  CdkScrollableModule as ɵɵCdkScrollableModule,\n  CdkFixedSizeVirtualScroll as ɵɵCdkFixedSizeVirtualScroll,\n  CdkVirtualForOf as ɵɵCdkVirtualForOf,\n  CdkVirtualScrollViewport as ɵɵCdkVirtualScrollViewport,\n  CdkVirtualScrollableWindow as ɵɵCdkVirtualScrollableWindow,\n  CdkVirtualScrollableElement as ɵɵCdkVirtualScrollableElement,\n} from '../scrolling';\nexport {Dir as ɵɵDir} from '../bidi';\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAaA,MAAM,uBAAuB,GAAG,sBAAsB,EAAE;AAExD;;AAEG;MACU,mBAAmB,CAAA;AAOpB,IAAA,cAAA;IANF,mBAAmB,GAAG,EAAC,GAAG,EAAE,EAAE,EAAE,IAAI,EAAE,EAAE,EAAC;AACzC,IAAA,uBAAuB;IACvB,UAAU,GAAG,KAAK;AAClB,IAAA,SAAS;IAEjB,WACU,CAAA,cAA6B,EACrC,QAAa,EAAA;QADL,IAAc,CAAA,cAAA,GAAd,cAAc;AAGtB,QAAA,IAAI,CAAC,SAAS,GAAG,QAAQ;;;AAI3B,IAAA,MAAM;;IAGN,MAAM,GAAA;AACJ,QAAA,IAAI,IAAI,CAAC,aAAa,EAAE,EAAE;AACxB,YAAA,MAAM,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,eAAgB;YAE5C,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC,cAAc,CAAC,yBAAyB,EAAE;;AAG9E,YAAA,IAAI,CAAC,mBAAmB,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,IAAI,EAAE;AACrD,YAAA,IAAI,CAAC,mBAAmB,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,IAAI,EAAE;;;AAInD,YAAA,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,mBAAmB,CAAC,CAAC,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC;AACzE,YAAA,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,mBAAmB,CAAC,CAAC,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC;AACvE,YAAA,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,wBAAwB,CAAC;AAC5C,YAAA,IAAI,CAAC,UAAU,GAAG,IAAI;;;;IAK1B,OAAO,GAAA;AACL,QAAA,IAAI,IAAI,CAAC,UAAU,EAAE;AACnB,YAAA,MAAM,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,eAAgB;AAC5C,YAAA,MAAM,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,IAAK;AACjC,YAAA,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK;AAC5B,YAAA,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK;AAC5B,YAAA,MAAM,0BAA0B,GAAG,SAAS,CAAC,cAAc,IAAI,EAAE;AACjE,YAAA,MAAM,0BAA0B,GAAG,SAAS,CAAC,cAAc,IAAI,EAAE;AAEjE,YAAA,IAAI,CAAC,UAAU,GAAG,KAAK;YAEvB,SAAS,CAAC,IAAI,GAAG,IAAI,CAAC,mBAAmB,CAAC,IAAI;YAC9C,SAAS,CAAC,GAAG,GAAG,IAAI,CAAC,mBAAmB,CAAC,GAAG;AAC5C,YAAA,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,wBAAwB,CAAC;;;;;;YAO/C,IAAI,uBAAuB,EAAE;gBAC3B,SAAS,CAAC,cAAc,GAAG,SAAS,CAAC,cAAc,GAAG,MAAM;;AAG9D,YAAA,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,uBAAuB,CAAC,IAAI,EAAE,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC;YAElF,IAAI,uBAAuB,EAAE;AAC3B,gBAAA,SAAS,CAAC,cAAc,GAAG,0BAA0B;AACrD,gBAAA,SAAS,CAAC,cAAc,GAAG,0BAA0B;;;;IAKnD,aAAa,GAAA;;;;AAInB,QAAA,MAAM,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,eAAgB;AAE5C,QAAA,IAAI,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,wBAAwB,CAAC,IAAI,IAAI,CAAC,UAAU,EAAE;AACxE,YAAA,OAAO,KAAK;;AAGd,QAAA,MAAM,WAAW,GAAG,IAAI,CAAC,SAAS,CAAC,eAAe;QAClD,MAAM,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,eAAe,EAAE;AACtD,QAAA,OAAO,WAAW,CAAC,YAAY,GAAG,QAAQ,CAAC,MAAM,IAAI,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,KAAK;;AAEhG;;AC3ED;;AAEG;SACa,wCAAwC,GAAA;AACtD,IAAA,OAAO,KAAK,CAAC,CAA4C,0CAAA,CAAA,CAAC;AAC5D;;ACVA;;AAEG;MACU,mBAAmB,CAAA;AAMpB,IAAA,iBAAA;AACA,IAAA,OAAA;AACA,IAAA,cAAA;AACA,IAAA,OAAA;IARF,mBAAmB,GAAwB,IAAI;AAC/C,IAAA,WAAW;AACX,IAAA,sBAAsB;AAE9B,IAAA,WAAA,CACU,iBAAmC,EACnC,OAAe,EACf,cAA6B,EAC7B,OAAmC,EAAA;QAHnC,IAAiB,CAAA,iBAAA,GAAjB,iBAAiB;QACjB,IAAO,CAAA,OAAA,GAAP,OAAO;QACP,IAAc,CAAA,cAAA,GAAd,cAAc;QACd,IAAO,CAAA,OAAA,GAAP,OAAO;;;AAIjB,IAAA,MAAM,CAAC,UAAsB,EAAA;AAC3B,QAAA,IAAI,IAAI,CAAC,WAAW,KAAK,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,CAAC,EAAE;YACvE,MAAM,wCAAwC,EAAE;;AAGlD,QAAA,IAAI,CAAC,WAAW,GAAG,UAAU;;;IAI/B,MAAM,GAAA;AACJ,QAAA,IAAI,IAAI,CAAC,mBAAmB,EAAE;YAC5B;;AAGF,QAAA,MAAM,MAAM,GAAG,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,CACpD,MAAM,CAAC,UAAU,IAAG;YAClB,QACE,CAAC,UAAU;AACX,gBAAA,CAAC,IAAI,CAAC,WAAW,CAAC,cAAc,CAAC,QAAQ,CAAC,UAAU,CAAC,aAAa,EAAE,CAAC,aAAa,CAAC;SAEtF,CAAC,CACH;AAED,QAAA,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,GAAG,CAAC,EAAE;YACxE,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,cAAc,CAAC,yBAAyB,EAAE,CAAC,GAAG;YAEjF,IAAI,CAAC,mBAAmB,GAAG,MAAM,CAAC,SAAS,CAAC,MAAK;gBAC/C,MAAM,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC,yBAAyB,EAAE,CAAC,GAAG;AAE1E,gBAAA,IAAI,IAAI,CAAC,GAAG,CAAC,cAAc,GAAG,IAAI,CAAC,sBAAsB,CAAC,GAAG,IAAI,CAAC,OAAQ,CAAC,SAAU,EAAE;oBACrF,IAAI,CAAC,OAAO,EAAE;;qBACT;AACL,oBAAA,IAAI,CAAC,WAAW,CAAC,cAAc,EAAE;;AAErC,aAAC,CAAC;;aACG;YACL,IAAI,CAAC,mBAAmB,GAAG,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC;;;;IAK7D,OAAO,GAAA;AACL,QAAA,IAAI,IAAI,CAAC,mBAAmB,EAAE;AAC5B,YAAA,IAAI,CAAC,mBAAmB,CAAC,WAAW,EAAE;AACtC,YAAA,IAAI,CAAC,mBAAmB,GAAG,IAAI;;;IAInC,MAAM,GAAA;QACJ,IAAI,CAAC,OAAO,EAAE;AACd,QAAA,IAAI,CAAC,WAAW,GAAG,IAAK;;;IAIlB,OAAO,GAAG,MAAK;QACrB,IAAI,CAAC,OAAO,EAAE;AAEd,QAAA,IAAI,IAAI,CAAC,WAAW,CAAC,WAAW,EAAE,EAAE;AAClC,YAAA,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC;;AAErD,KAAC;AACF;;ACzFD;MACa,kBAAkB,CAAA;;AAE7B,IAAA,MAAM;;AAEN,IAAA,OAAO;;AAEP,IAAA,MAAM;AACP;;ACJD;;;;;;AAMG;AACa,SAAA,4BAA4B,CAAC,OAAmB,EAAE,gBAA8B,EAAA;AAC9F,IAAA,OAAO,gBAAgB,CAAC,IAAI,CAAC,eAAe,IAAG;QAC7C,MAAM,YAAY,GAAG,OAAO,CAAC,MAAM,GAAG,eAAe,CAAC,GAAG;QACzD,MAAM,YAAY,GAAG,OAAO,CAAC,GAAG,GAAG,eAAe,CAAC,MAAM;QACzD,MAAM,WAAW,GAAG,OAAO,CAAC,KAAK,GAAG,eAAe,CAAC,IAAI;QACxD,MAAM,YAAY,GAAG,OAAO,CAAC,IAAI,GAAG,eAAe,CAAC,KAAK;AAEzD,QAAA,OAAO,YAAY,IAAI,YAAY,IAAI,WAAW,IAAI,YAAY;AACpE,KAAC,CAAC;AACJ;AAEA;;;;;;AAMG;AACa,SAAA,2BAA2B,CAAC,OAAmB,EAAE,gBAA8B,EAAA;AAC7F,IAAA,OAAO,gBAAgB,CAAC,IAAI,CAAC,mBAAmB,IAAG;QACjD,MAAM,YAAY,GAAG,OAAO,CAAC,GAAG,GAAG,mBAAmB,CAAC,GAAG;QAC1D,MAAM,YAAY,GAAG,OAAO,CAAC,MAAM,GAAG,mBAAmB,CAAC,MAAM;QAChE,MAAM,WAAW,GAAG,OAAO,CAAC,IAAI,GAAG,mBAAmB,CAAC,IAAI;QAC3D,MAAM,YAAY,GAAG,OAAO,CAAC,KAAK,GAAG,mBAAmB,CAAC,KAAK;AAE9D,QAAA,OAAO,YAAY,IAAI,YAAY,IAAI,WAAW,IAAI,YAAY;AACpE,KAAC,CAAC;AACJ;;ACtBA;;AAEG;MACU,wBAAwB,CAAA;AAKzB,IAAA,iBAAA;AACA,IAAA,cAAA;AACA,IAAA,OAAA;AACA,IAAA,OAAA;IAPF,mBAAmB,GAAwB,IAAI;AAC/C,IAAA,WAAW;AAEnB,IAAA,WAAA,CACU,iBAAmC,EACnC,cAA6B,EAC7B,OAAe,EACf,OAAwC,EAAA;QAHxC,IAAiB,CAAA,iBAAA,GAAjB,iBAAiB;QACjB,IAAc,CAAA,cAAA,GAAd,cAAc;QACd,IAAO,CAAA,OAAA,GAAP,OAAO;QACP,IAAO,CAAA,OAAA,GAAP,OAAO;;;AAIjB,IAAA,MAAM,CAAC,UAAsB,EAAA;AAC3B,QAAA,IAAI,IAAI,CAAC,WAAW,KAAK,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,CAAC,EAAE;YACvE,MAAM,wCAAwC,EAAE;;AAGlD,QAAA,IAAI,CAAC,WAAW,GAAG,UAAU;;;IAI/B,MAAM,GAAA;AACJ,QAAA,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE;AAC7B,YAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,cAAc,GAAG,CAAC;AAE/D,YAAA,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,MAAK;AAClF,gBAAA,IAAI,CAAC,WAAW,CAAC,cAAc,EAAE;;gBAGjC,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE;oBAC1C,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,cAAc,CAAC,qBAAqB,EAAE;AAC3E,oBAAA,MAAM,EAAC,KAAK,EAAE,MAAM,EAAC,GAAG,IAAI,CAAC,cAAc,CAAC,eAAe,EAAE;;;oBAI7D,MAAM,WAAW,GAAG,CAAC,EAAC,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAC,CAAC;AAEpF,oBAAA,IAAI,4BAA4B,CAAC,WAAW,EAAE,WAAW,CAAC,EAAE;wBAC1D,IAAI,CAAC,OAAO,EAAE;AACd,wBAAA,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC;;;AAGvD,aAAC,CAAC;;;;IAKN,OAAO,GAAA;AACL,QAAA,IAAI,IAAI,CAAC,mBAAmB,EAAE;AAC5B,YAAA,IAAI,CAAC,mBAAmB,CAAC,WAAW,EAAE;AACtC,YAAA,IAAI,CAAC,mBAAmB,GAAG,IAAI;;;IAInC,MAAM,GAAA;QACJ,IAAI,CAAC,OAAO,EAAE;AACd,QAAA,IAAI,CAAC,WAAW,GAAG,IAAK;;AAE3B;;ACpED;;;;;AAKG;MAEU,qBAAqB,CAAA;AACxB,IAAA,iBAAiB,GAAG,MAAM,CAAC,gBAAgB,CAAC;AAC5C,IAAA,cAAc,GAAG,MAAM,CAAC,aAAa,CAAC;AACtC,IAAA,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC;AAExB,IAAA,SAAS,GAAG,MAAM,CAAC,QAAQ,CAAC;AAGpC,IAAA,WAAA,GAAA;;AAGA,IAAA,IAAI,GAAG,MAAM,IAAI,kBAAkB,EAAE;AAErC;;;AAGG;IACH,KAAK,GAAG,CAAC,MAAkC,KACzC,IAAI,mBAAmB,CAAC,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,cAAc,EAAE,MAAM,CAAC;;AAG5F,IAAA,KAAK,GAAG,MAAM,IAAI,mBAAmB,CAAC,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,SAAS,CAAC;AAE1E;;;;AAIG;IACH,UAAU,GAAG,CAAC,MAAuC,KACnD,IAAI,wBAAwB,CAAC,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,OAAO,EAAE,MAAM,CAAC;uGA7BtF,qBAAqB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA;AAArB,IAAA,OAAA,KAAA,GAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,qBAAqB,cADT,MAAM,EAAA,CAAA;;2FAClB,qBAAqB,EAAA,UAAA,EAAA,CAAA;kBADjC,UAAU;mBAAC,EAAC,UAAU,EAAE,MAAM,EAAC;;;ACbhC;MACa,aAAa,CAAA;;AAExB,IAAA,gBAAgB;;AAGhB,IAAA,cAAc,GAAoB,IAAI,kBAAkB,EAAE;;IAG1D,UAAU,GAAuB,EAAE;;IAGnC,WAAW,GAAa,KAAK;;IAG7B,aAAa,GAAuB,2BAA2B;;AAG/D,IAAA,KAAK;;AAGL,IAAA,MAAM;;AAGN,IAAA,QAAQ;;AAGR,IAAA,SAAS;;AAGT,IAAA,QAAQ;;AAGR,IAAA,SAAS;AAET;;;AAGG;AACH,IAAA,SAAS;AAET;;;;AAIG;IACH,mBAAmB,GAAa,KAAK;AAErC,IAAA,WAAA,CAAY,MAAsB,EAAA;QAChC,IAAI,MAAM,EAAE;;;;YAIV,MAAM,UAAU,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,CACZ;AACzB,YAAA,KAAK,MAAM,GAAG,IAAI,UAAU,EAAE;AAC5B,gBAAA,IAAI,MAAM,CAAC,GAAG,CAAC,KAAK,SAAS,EAAE;;;;;;;oBAO7B,IAAI,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,GAAG,CAAQ;;;;;AAKvC;;ACtDD;MACa,sBAAsB,CAAA;AAcxB,IAAA,OAAA;AAEA,IAAA,OAAA;AAEA,IAAA,UAAA;;AAhBT,IAAA,OAAO;;AAEP,IAAA,OAAO;;AAEP,IAAA,QAAQ;;AAER,IAAA,QAAQ;IAER,WACE,CAAA,MAAgC,EAChC,OAAkC;;IAE3B,OAAgB;;IAEhB,OAAgB;;IAEhB,UAA8B,EAAA;QAJ9B,IAAO,CAAA,OAAA,GAAP,OAAO;QAEP,IAAO,CAAA,OAAA,GAAP,OAAO;QAEP,IAAU,CAAA,UAAA,GAAV,UAAU;AAEjB,QAAA,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC,OAAO;AAC7B,QAAA,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC,OAAO;AAC7B,QAAA,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ;AAChC,QAAA,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ;;AAEnC;AAED;;;;;;;;;;;;;;;;;;;;;;;;AAwBG;MACU,mBAAmB,CAAA;AAC9B,IAAA,eAAe;AACf,IAAA,mBAAmB;AACnB,IAAA,gBAAgB;AAChB,IAAA,oBAAoB;AACrB;AAED;MACa,8BAA8B,CAAA;AAGhC,IAAA,cAAA;AAEA,IAAA,wBAAA;AAJT,IAAA,WAAA;;IAES,cAAsC;;IAEtC,wBAA6C,EAAA;QAF7C,IAAc,CAAA,cAAA,GAAd,cAAc;QAEd,IAAwB,CAAA,wBAAA,GAAxB,wBAAwB;;AAElC;AAED;;;;;AAKG;AACa,SAAA,wBAAwB,CAAC,QAAgB,EAAE,KAA4B,EAAA;AACrF,IAAA,IAAI,KAAK,KAAK,KAAK,IAAI,KAAK,KAAK,QAAQ,IAAI,KAAK,KAAK,QAAQ,EAAE;AAC/D,QAAA,MAAM,KAAK,CACT,CAAA,2BAAA,EAA8B,QAAQ,CAAA,EAAA,EAAK,KAAK,CAAK,GAAA,CAAA;AACnD,YAAA,CAAA,qCAAA,CAAuC,CAC1C;;AAEL;AAEA;;;;;AAKG;AACa,SAAA,0BAA0B,CAAC,QAAgB,EAAE,KAA8B,EAAA;AACzF,IAAA,IAAI,KAAK,KAAK,OAAO,IAAI,KAAK,KAAK,KAAK,IAAI,KAAK,KAAK,QAAQ,EAAE;AAC9D,QAAA,MAAM,KAAK,CACT,CAAA,2BAAA,EAA8B,QAAQ,CAAA,EAAA,EAAK,KAAK,CAAK,GAAA,CAAA;AACnD,YAAA,CAAA,oCAAA,CAAsC,CACzC;;AAEL;;AChHA;;;;AAIG;MAEmB,qBAAqB,CAAA;;IAEzC,iBAAiB,GAAiB,EAAE;AAE1B,IAAA,SAAS,GAAG,MAAM,CAAC,QAAQ,CAAC;AAC5B,IAAA,WAAW;AAIrB,IAAA,WAAA,GAAA;IAEA,WAAW,GAAA;QACT,IAAI,CAAC,MAAM,EAAE;;;AAIf,IAAA,GAAG,CAAC,UAAsB,EAAA;;AAExB,QAAA,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC;AACvB,QAAA,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC;;;AAIzC,IAAA,MAAM,CAAC,UAAsB,EAAA;QAC3B,MAAM,KAAK,GAAG,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,UAAU,CAAC;AAExD,QAAA,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE;YACd,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC;;;QAIzC,IAAI,IAAI,CAAC,iBAAiB,CAAC,MAAM,KAAK,CAAC,EAAE;YACvC,IAAI,CAAC,MAAM,EAAE;;;uGAhCG,qBAAqB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA;AAArB,IAAA,OAAA,KAAA,GAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,qBAAqB,cADlB,MAAM,EAAA,CAAA;;2FACT,qBAAqB,EAAA,UAAA,EAAA,CAAA;kBAD1C,UAAU;mBAAC,EAAC,UAAU,EAAE,MAAM,EAAC;;;ACLhC;;;;AAIG;AAEG,MAAO,yBAA0B,SAAQ,qBAAqB,CAAA;AAC1D,IAAA,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC;AACxB,IAAA,SAAS,GAAG,MAAM,CAAC,gBAAgB,CAAC,CAAC,cAAc,CAAC,IAAI,EAAE,IAAI,CAAC;AAC/D,IAAA,eAAe;;AAGd,IAAA,GAAG,CAAC,UAAsB,EAAA;AACjC,QAAA,KAAK,CAAC,GAAG,CAAC,UAAU,CAAC;;AAGrB,QAAA,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;AACrB,YAAA,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,MAAK;AAClC,gBAAA,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,MAAM,EAAE,SAAS,EAAE,IAAI,CAAC,gBAAgB,CAAC;AACxF,aAAC,CAAC;AAEF,YAAA,IAAI,CAAC,WAAW,GAAG,IAAI;;;;IAKjB,MAAM,GAAA;AACd,QAAA,IAAI,IAAI,CAAC,WAAW,EAAE;AACpB,YAAA,IAAI,CAAC,eAAe,IAAI;AACxB,YAAA,IAAI,CAAC,WAAW,GAAG,KAAK;;;;AAKpB,IAAA,gBAAgB,GAAG,CAAC,KAAoB,KAAI;AAClD,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,iBAAiB;AAEvC,QAAA,KAAK,IAAI,CAAC,GAAG,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;;;;;;;AAO7C,YAAA,IAAI,QAAQ,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE;gBACnD,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,QAAQ,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBAC9D;;;AAGN,KAAC;uGA3CU,yBAAyB,EAAA,IAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA;AAAzB,IAAA,OAAA,KAAA,GAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,yBAAyB,cADb,MAAM,EAAA,CAAA;;2FAClB,yBAAyB,EAAA,UAAA,EAAA,CAAA;kBADrC,UAAU;mBAAC,EAAC,UAAU,EAAE,MAAM,EAAC;;;ACJhC;;;;AAIG;AAEG,MAAO,6BAA8B,SAAQ,qBAAqB,CAAA;AAC9D,IAAA,SAAS,GAAG,MAAM,CAAC,QAAQ,CAAC;AAC5B,IAAA,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC;AACxB,IAAA,SAAS,GAAG,MAAM,CAAC,gBAAgB,CAAC,CAAC,cAAc,CAAC,IAAI,EAAE,IAAI,CAAC;AAE/D,IAAA,oBAAoB;IACpB,iBAAiB,GAAG,KAAK;AACzB,IAAA,uBAAuB;AACvB,IAAA,SAAS;;AAGR,IAAA,GAAG,CAAC,UAAsB,EAAA;AACjC,QAAA,KAAK,CAAC,GAAG,CAAC,UAAU,CAAC;;;;;;;AAQrB,QAAA,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;AACrB,YAAA,MAAM,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI;AAChC,YAAA,MAAM,YAAY,GAAG,EAAC,OAAO,EAAE,IAAI,EAAC;YAEpC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,MAAM;AACpD,gBAAA,qBAAqB,CACnB,IAAI,CAAC,SAAS,EACd,IAAI,EACJ,aAAa,EACb,IAAI,CAAC,oBAAoB,EACzB,YAAY,CACb;AACD,gBAAA,qBAAqB,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,CAAC,cAAc,EAAE,YAAY,CAAC;AACvF,gBAAA,qBAAqB,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,CAAC,cAAc,EAAE,YAAY,CAAC;AAC1F,gBAAA,qBAAqB,CACnB,IAAI,CAAC,SAAS,EACd,IAAI,EACJ,aAAa,EACb,IAAI,CAAC,cAAc,EACnB,YAAY,CACb;AACF,aAAA,CAAC;;;YAIF,IAAI,IAAI,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE;gBACjD,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM;AAC7C,gBAAA,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,SAAS;AAC7B,gBAAA,IAAI,CAAC,iBAAiB,GAAG,IAAI;;AAG/B,YAAA,IAAI,CAAC,WAAW,GAAG,IAAI;;;;IAKjB,MAAM,GAAA;AACd,QAAA,IAAI,IAAI,CAAC,WAAW,EAAE;AACpB,YAAA,IAAI,CAAC,SAAS,EAAE,OAAO,CAAC,OAAO,IAAI,OAAO,EAAE,CAAC;AAC7C,YAAA,IAAI,CAAC,SAAS,GAAG,SAAS;YAC1B,IAAI,IAAI,CAAC,SAAS,CAAC,GAAG,IAAI,IAAI,CAAC,iBAAiB,EAAE;AAChD,gBAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,oBAAoB;AAC5D,gBAAA,IAAI,CAAC,iBAAiB,GAAG,KAAK;;AAEhC,YAAA,IAAI,CAAC,WAAW,GAAG,KAAK;;;;AAKpB,IAAA,oBAAoB,GAAG,CAAC,KAAmB,KAAI;AACrD,QAAA,IAAI,CAAC,uBAAuB,GAAG,eAAe,CAAc,KAAK,CAAC;AACpE,KAAC;;AAGO,IAAA,cAAc,GAAG,CAAC,KAAiB,KAAI;AAC7C,QAAA,MAAM,MAAM,GAAG,eAAe,CAAc,KAAK,CAAC;;;;;;;QAOlD,MAAM,MAAM,GACV,KAAK,CAAC,IAAI,KAAK,OAAO,IAAI,IAAI,CAAC;cAC3B,IAAI,CAAC;cACL,MAAM;;;AAGZ,QAAA,IAAI,CAAC,uBAAuB,GAAG,IAAI;;;;QAKnC,MAAM,QAAQ,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE;;;;;AAM/C,QAAA,KAAK,IAAI,CAAC,GAAG,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;AAC7C,YAAA,MAAM,UAAU,GAAG,QAAQ,CAAC,CAAC,CAAC;AAC9B,YAAA,IAAI,UAAU,CAAC,qBAAqB,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,WAAW,EAAE,EAAE;gBACtF;;;;;AAMF,YAAA,IACE,uBAAuB,CAAC,UAAU,CAAC,cAAc,EAAE,MAAM,CAAC;gBAC1D,uBAAuB,CAAC,UAAU,CAAC,cAAc,EAAE,MAAM,CAAC,EAC1D;gBACA;;AAGF,YAAA,MAAM,oBAAoB,GAAG,UAAU,CAAC,qBAAqB;;AAE7D,YAAA,IAAI,IAAI,CAAC,OAAO,EAAE;AAChB,gBAAA,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,oBAAoB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;;iBACnD;AACL,gBAAA,oBAAoB,CAAC,IAAI,CAAC,KAAK,CAAC;;;AAGtC,KAAC;uGA3HU,6BAA6B,EAAA,IAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA;AAA7B,IAAA,OAAA,KAAA,GAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,6BAA6B,cADjB,MAAM,EAAA,CAAA;;2FAClB,6BAA6B,EAAA,UAAA,EAAA,CAAA;kBADzC,UAAU;mBAAC,EAAC,UAAU,EAAE,MAAM,EAAC;;AA+HhC;AACA,SAAS,uBAAuB,CAAC,MAAmB,EAAE,KAAyB,EAAA;IAC7E,MAAM,kBAAkB,GAAG,OAAO,UAAU,KAAK,WAAW,IAAI,UAAU;IAC1E,IAAI,OAAO,GAAgB,KAAK;IAEhC,OAAO,OAAO,EAAE;AACd,QAAA,IAAI,OAAO,KAAK,MAAM,EAAE;AACtB,YAAA,OAAO,IAAI;;QAGb,OAAO;AACL,YAAA,kBAAkB,IAAI,OAAO,YAAY,UAAU,GAAG,OAAO,CAAC,IAAI,GAAG,OAAO,CAAC,UAAU;;AAG3F,IAAA,OAAO,KAAK;AACd;;MCrIa,sBAAsB,CAAA;uGAAtB,sBAAsB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAtB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,sBAAsB,kIANvB,EAAE,EAAA,QAAA,EAAA,IAAA,EAAA,MAAA,EAAA,CAAA,6oDAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FAMD,sBAAsB,EAAA,UAAA,EAAA,CAAA;kBAPlC,SAAS;AACE,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,EAAE,EACK,eAAA,EAAA,uBAAuB,CAAC,MAAM,EAChC,aAAA,EAAA,iBAAiB,CAAC,IAAI,EAE/B,IAAA,EAAA,EAAC,0BAA0B,EAAE,EAAE,EAAC,EAAA,MAAA,EAAA,CAAA,6oDAAA,CAAA,EAAA;;AAIxC;MAEa,gBAAgB,CAAA;AACjB,IAAA,SAAS,GAAG,MAAM,CAAC,QAAQ,CAAC;AAE5B,IAAA,iBAAiB;AACjB,IAAA,SAAS,GAAG,MAAM,CAAC,QAAQ,CAAC;AAC5B,IAAA,YAAY,GAAG,MAAM,CAAC,sBAAsB,CAAC;AAGvD,IAAA,WAAA,GAAA;IAEA,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,iBAAiB,EAAE,MAAM,EAAE;;AAGlC;;;;;AAKG;IACH,mBAAmB,GAAA;QACjB,IAAI,CAAC,WAAW,EAAE;AAElB,QAAA,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE;YAC3B,IAAI,CAAC,gBAAgB,EAAE;;QAGzB,OAAO,IAAI,CAAC,iBAAiB;;AAG/B;;;AAGG;IACO,gBAAgB,GAAA;QACxB,MAAM,cAAc,GAAG,uBAAuB;;;;QAK9C,IAAI,IAAI,CAAC,SAAS,CAAC,SAAS,IAAI,kBAAkB,EAAE,EAAE;AACpD,YAAA,MAAM,0BAA0B,GAAG,IAAI,CAAC,SAAS,CAAC,gBAAgB,CAChE,CAAI,CAAA,EAAA,cAAc,uBAAuB,GAAG,CAAA,CAAA,EAAI,cAAc,CAAA,iBAAA,CAAmB,CAClF;;;AAID,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,0BAA0B,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAC1D,gBAAA,0BAA0B,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE;;;QAI1C,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC;AACrD,QAAA,SAAS,CAAC,SAAS,CAAC,GAAG,CAAC,cAAc,CAAC;;;;;;;;;;QAWvC,IAAI,kBAAkB,EAAE,EAAE;AACxB,YAAA,SAAS,CAAC,YAAY,CAAC,UAAU,EAAE,MAAM,CAAC;;AACrC,aAAA,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE;AACpC,YAAA,SAAS,CAAC,YAAY,CAAC,UAAU,EAAE,QAAQ,CAAC;;QAG9C,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC;AAC1C,QAAA,IAAI,CAAC,iBAAiB,GAAG,SAAS;;;IAI1B,WAAW,GAAA;AACnB,QAAA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,sBAAsB,CAAC;;uGA5ErC,gBAAgB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA;AAAhB,IAAA,OAAA,KAAA,GAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,gBAAgB,cADJ,MAAM,EAAA,CAAA;;2FAClB,gBAAgB,EAAA,UAAA,EAAA,CAAA;kBAD5B,UAAU;mBAAC,EAAC,UAAU,EAAE,MAAM,EAAC;;;ACpBhC;MACa,WAAW,CAAA;AAQZ,IAAA,SAAA;AACA,IAAA,OAAA;AARD,IAAA,OAAO;AACR,IAAA,aAAa;AACb,IAAA,qBAAqB;AACrB,IAAA,gBAAgB;AAExB,IAAA,WAAA,CACE,QAAkB,EACV,SAAoB,EACpB,OAAe,EACvB,OAAoC,EAAA;QAF5B,IAAS,CAAA,SAAA,GAAT,SAAS;QACT,IAAO,CAAA,OAAA,GAAP,OAAO;QAGf,IAAI,CAAC,OAAO,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC;QAC5C,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,sBAAsB,CAAC;AAClD,QAAA,IAAI,CAAC,aAAa,GAAG,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC;;IAGvE,MAAM,GAAA;AACJ,QAAA,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,MAAK;AAClC,YAAA,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO;AAC5B,YAAA,YAAY,CAAC,IAAI,CAAC,gBAAgB,CAAC;AACnC,YAAA,IAAI,CAAC,qBAAqB,IAAI;AAC9B,YAAA,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,OAAO,EAAE,eAAe,EAAE,IAAI,CAAC,OAAO,CAAC;YAC1F,IAAI,CAAC,gBAAgB,GAAG,UAAU,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,CAAC;;;AAIrD,YAAA,OAAO,CAAC,KAAK,CAAC,aAAa,GAAG,MAAM;AACpC,YAAA,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,8BAA8B,CAAC;AAC1D,SAAC,CAAC;;IAGJ,OAAO,GAAG,MAAK;AACb,QAAA,YAAY,CAAC,IAAI,CAAC,gBAAgB,CAAC;AACnC,QAAA,IAAI,CAAC,aAAa,IAAI;AACtB,QAAA,IAAI,CAAC,qBAAqB,IAAI;AAC9B,QAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,gBAAgB,GAAG,SAAS;AACnF,QAAA,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;AACvB,KAAC;AACF;;ACbD;;;AAGG;MACU,UAAU,CAAA;AA6BX,IAAA,aAAA;AACA,IAAA,KAAA;AACA,IAAA,KAAA;AACA,IAAA,OAAA;AACA,IAAA,OAAA;AACA,IAAA,mBAAA;AACA,IAAA,SAAA;AACA,IAAA,SAAA;AACA,IAAA,uBAAA;AACA,IAAA,mBAAA;AACA,IAAA,SAAA;AACA,IAAA,SAAA;AAvCO,IAAA,cAAc,GAAG,IAAI,OAAO,EAAc;AAC1C,IAAA,YAAY,GAAG,IAAI,OAAO,EAAQ;AAClC,IAAA,YAAY,GAAG,IAAI,OAAO,EAAQ;AAC3C,IAAA,iBAAiB;AACjB,IAAA,eAAe;AACf,IAAA,gBAAgB,GAAqB,YAAY,CAAC,KAAK;IACvD,YAAY,GAAuB,IAAI;AAE/C;;;AAGG;AACK,IAAA,mBAAmB;;AAGlB,IAAA,cAAc,GAAG,IAAI,OAAO,EAAiB;;AAG7C,IAAA,qBAAqB,GAAG,IAAI,OAAO,EAAc;AAElD,IAAA,QAAQ,GAAG,IAAI,OAAO,EAAQ;AAE9B,IAAA,eAAe;;AAGf,IAAA,mBAAmB;IAE3B,WACU,CAAA,aAA2B,EAC3B,KAAkB,EAClB,KAAkB,EAClB,OAAuC,EACvC,OAAe,EACf,mBAA8C,EAC9C,SAAmB,EACnB,SAAmB,EACnB,uBAAsD,EACtD,sBAAsB,KAAK,EAC3B,SAA8B,EAC9B,SAAoB,EAAA;QAXpB,IAAa,CAAA,aAAA,GAAb,aAAa;QACb,IAAK,CAAA,KAAA,GAAL,KAAK;QACL,IAAK,CAAA,KAAA,GAAL,KAAK;QACL,IAAO,CAAA,OAAA,GAAP,OAAO;QACP,IAAO,CAAA,OAAA,GAAP,OAAO;QACP,IAAmB,CAAA,mBAAA,GAAnB,mBAAmB;QACnB,IAAS,CAAA,SAAA,GAAT,SAAS;QACT,IAAS,CAAA,SAAA,GAAT,SAAS;QACT,IAAuB,CAAA,uBAAA,GAAvB,uBAAuB;QACvB,IAAmB,CAAA,mBAAA,GAAnB,mBAAmB;QACnB,IAAS,CAAA,SAAA,GAAT,SAAS;QACT,IAAS,CAAA,SAAA,GAAT,SAAS;AAEjB,QAAA,IAAI,OAAO,CAAC,cAAc,EAAE;AAC1B,YAAA,IAAI,CAAC,eAAe,GAAG,OAAO,CAAC,cAAc;AAC7C,YAAA,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,IAAI,CAAC;;AAGnC,QAAA,IAAI,CAAC,iBAAiB,GAAG,OAAO,CAAC,gBAAgB;;;;QAKjD,IAAI,CAAC,eAAe,GAAG,SAAS,CAAC,MAC/B,WAAW,CACT,MAAK;AACH,YAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE;SACrB,EACD,EAAC,QAAQ,EAAE,IAAI,CAAC,SAAS,EAAC,CAC3B,CACF;;;AAIH,IAAA,IAAI,cAAc,GAAA;QAChB,OAAO,IAAI,CAAC,KAAK;;;AAInB,IAAA,IAAI,eAAe,GAAA;AACjB,QAAA,OAAO,IAAI,CAAC,YAAY,EAAE,OAAO,IAAI,IAAI;;AAG3C;;;;AAIG;AACH,IAAA,IAAI,WAAW,GAAA;QACb,OAAO,IAAI,CAAC,KAAK;;AAOnB;;;;;;AAMG;AACH,IAAA,MAAM,CAAC,MAAmB,EAAA;;;QAGxB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,aAAa,IAAI,IAAI,CAAC,mBAAmB,EAAE;YACzD,IAAI,CAAC,mBAAmB,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC;;QAGlD,MAAM,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,MAAM,CAAC;AAEtD,QAAA,IAAI,IAAI,CAAC,iBAAiB,EAAE;AAC1B,YAAA,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,IAAI,CAAC;;QAGrC,IAAI,CAAC,oBAAoB,EAAE;QAC3B,IAAI,CAAC,kBAAkB,EAAE;QACzB,IAAI,CAAC,uBAAuB,EAAE;AAE9B,QAAA,IAAI,IAAI,CAAC,eAAe,EAAE;AACxB,YAAA,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE;;;;;AAM/B,QAAA,IAAI,CAAC,mBAAmB,EAAE,OAAO,EAAE;;;AAInC,QAAA,IAAI,CAAC,mBAAmB,GAAG,eAAe,CACxC,MAAK;;AAEH,YAAA,IAAI,IAAI,CAAC,WAAW,EAAE,EAAE;gBACtB,IAAI,CAAC,cAAc,EAAE;;SAExB,EACD,EAAC,QAAQ,EAAE,IAAI,CAAC,SAAS,EAAC,CAC3B;;AAGD,QAAA,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC;AAE/B,QAAA,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE;YAC5B,IAAI,CAAC,eAAe,EAAE;;AAGxB,QAAA,IAAI,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE;AAC3B,YAAA,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,IAAI,CAAC;;;AAIhE,QAAA,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE;;AAGxB,QAAA,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,IAAI,CAAC;AAElC,QAAA,IAAI,IAAI,CAAC,OAAO,CAAC,mBAAmB,EAAE;AACpC,YAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,MAAM,IAAI,CAAC,OAAO,EAAE,CAAC;;AAGxE,QAAA,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC,IAAI,CAAC;;;;AAKtC,QAAA,IAAI,OAAO,YAAY,EAAE,SAAS,KAAK,UAAU,EAAE;;;;;;AAMjD,YAAA,YAAY,CAAC,SAAS,CAAC,MAAK;AAC1B,gBAAA,IAAI,IAAI,CAAC,WAAW,EAAE,EAAE;;;;oBAItB,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,MAAM,OAAO,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC;;AAErF,aAAC,CAAC;;AAGJ,QAAA,OAAO,YAAY;;AAGrB;;;AAGG;IACH,MAAM,GAAA;AACJ,QAAA,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,EAAE;YACvB;;QAGF,IAAI,CAAC,cAAc,EAAE;;;;AAKrB,QAAA,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC;QAEhC,IAAI,IAAI,CAAC,iBAAiB,IAAI,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE;AAC3D,YAAA,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE;;AAGjC,QAAA,IAAI,IAAI,CAAC,eAAe,EAAE;AACxB,YAAA,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE;;QAGhC,MAAM,gBAAgB,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE;;AAGpD,QAAA,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE;;AAGxB,QAAA,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,IAAI,CAAC;;;QAIrC,IAAI,CAAC,uBAAuB,EAAE;AAC9B,QAAA,IAAI,CAAC,gBAAgB,CAAC,WAAW,EAAE;AACnC,QAAA,IAAI,CAAC,uBAAuB,CAAC,MAAM,CAAC,IAAI,CAAC;AACzC,QAAA,OAAO,gBAAgB;;;IAIzB,OAAO,GAAA;AACL,QAAA,MAAM,UAAU,GAAG,IAAI,CAAC,WAAW,EAAE;AAErC,QAAA,IAAI,IAAI,CAAC,iBAAiB,EAAE;AAC1B,YAAA,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE;;QAGlC,IAAI,CAAC,sBAAsB,EAAE;AAC7B,QAAA,IAAI,CAAC,YAAY,EAAE,OAAO,EAAE;AAC5B,QAAA,IAAI,CAAC,gBAAgB,CAAC,WAAW,EAAE;AACnC,QAAA,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,IAAI,CAAC;AACrC,QAAA,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE;AAC5B,QAAA,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE;AAC5B,QAAA,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE;AAC9B,QAAA,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE;AAC9B,QAAA,IAAI,CAAC,qBAAqB,CAAC,QAAQ,EAAE;AACrC,QAAA,IAAI,CAAC,uBAAuB,CAAC,MAAM,CAAC,IAAI,CAAC;AACzC,QAAA,IAAI,CAAC,KAAK,EAAE,MAAM,EAAE;AACpB,QAAA,IAAI,CAAC,mBAAmB,EAAE,OAAO,EAAE;AACnC,QAAA,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,YAAY,GAAG,IAAK;QAE9E,IAAI,UAAU,EAAE;AACd,YAAA,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE;;AAG1B,QAAA,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE;AAC5B,QAAA,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE;AAC9B,QAAA,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE;;;IAI1B,WAAW,GAAA;AACT,QAAA,OAAO,IAAI,CAAC,aAAa,CAAC,WAAW,EAAE;;;IAIzC,aAAa,GAAA;QACX,OAAO,IAAI,CAAC,cAAc;;;IAI5B,WAAW,GAAA;QACT,OAAO,IAAI,CAAC,YAAY;;;IAI1B,WAAW,GAAA;QACT,OAAO,IAAI,CAAC,YAAY;;;IAI1B,aAAa,GAAA;QACX,OAAO,IAAI,CAAC,cAAc;;;IAI5B,oBAAoB,GAAA;QAClB,OAAO,IAAI,CAAC,qBAAqB;;;IAInC,SAAS,GAAA;QACP,OAAO,IAAI,CAAC,OAAO;;;IAIrB,cAAc,GAAA;AACZ,QAAA,IAAI,IAAI,CAAC,iBAAiB,EAAE;AAC1B,YAAA,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE;;;;AAKlC,IAAA,sBAAsB,CAAC,QAA0B,EAAA;AAC/C,QAAA,IAAI,QAAQ,KAAK,IAAI,CAAC,iBAAiB,EAAE;YACvC;;AAGF,QAAA,IAAI,IAAI,CAAC,iBAAiB,EAAE;AAC1B,YAAA,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE;;AAGlC,QAAA,IAAI,CAAC,iBAAiB,GAAG,QAAQ;AAEjC,QAAA,IAAI,IAAI,CAAC,WAAW,EAAE,EAAE;AACtB,YAAA,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC;YACrB,IAAI,CAAC,cAAc,EAAE;;;;AAKzB,IAAA,UAAU,CAAC,UAA6B,EAAA;AACtC,QAAA,IAAI,CAAC,OAAO,GAAG,EAAC,GAAG,IAAI,CAAC,OAAO,EAAE,GAAG,UAAU,EAAC;QAC/C,IAAI,CAAC,kBAAkB,EAAE;;;AAI3B,IAAA,YAAY,CAAC,GAA+B,EAAA;AAC1C,QAAA,IAAI,CAAC,OAAO,GAAG,EAAC,GAAG,IAAI,CAAC,OAAO,EAAE,SAAS,EAAE,GAAG,EAAC;QAChD,IAAI,CAAC,uBAAuB,EAAE;;;AAIhC,IAAA,aAAa,CAAC,OAA0B,EAAA;AACtC,QAAA,IAAI,IAAI,CAAC,KAAK,EAAE;YACd,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,EAAE,OAAO,EAAE,IAAI,CAAC;;;;AAKlD,IAAA,gBAAgB,CAAC,OAA0B,EAAA;AACzC,QAAA,IAAI,IAAI,CAAC,KAAK,EAAE;YACd,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,EAAE,OAAO,EAAE,KAAK,CAAC;;;AAInD;;AAEG;IACH,YAAY,GAAA;AACV,QAAA,MAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS;QAExC,IAAI,CAAC,SAAS,EAAE;AACd,YAAA,OAAO,KAAK;;AAGd,QAAA,OAAO,OAAO,SAAS,KAAK,QAAQ,GAAG,SAAS,GAAG,SAAS,CAAC,KAAK;;;AAIpE,IAAA,oBAAoB,CAAC,QAAwB,EAAA;AAC3C,QAAA,IAAI,QAAQ,KAAK,IAAI,CAAC,eAAe,EAAE;YACrC;;QAGF,IAAI,CAAC,sBAAsB,EAAE;AAC7B,QAAA,IAAI,CAAC,eAAe,GAAG,QAAQ;AAE/B,QAAA,IAAI,IAAI,CAAC,WAAW,EAAE,EAAE;AACtB,YAAA,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC;YACrB,QAAQ,CAAC,MAAM,EAAE;;;;IAKb,uBAAuB,GAAA;AAC7B,QAAA,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,KAAK,EAAE,IAAI,CAAC,YAAY,EAAE,CAAC;;;IAI7C,kBAAkB,GAAA;AACxB,QAAA,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE;YACf;;AAGF,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK;QAE9B,KAAK,CAAC,KAAK,GAAG,mBAAmB,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC;QACrD,KAAK,CAAC,MAAM,GAAG,mBAAmB,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;QACvD,KAAK,CAAC,QAAQ,GAAG,mBAAmB,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC;QAC3D,KAAK,CAAC,SAAS,GAAG,mBAAmB,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC;QAC7D,KAAK,CAAC,QAAQ,GAAG,mBAAmB,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC;QAC3D,KAAK,CAAC,SAAS,GAAG,mBAAmB,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC;;;AAIvD,IAAA,oBAAoB,CAAC,aAAsB,EAAA;AACjD,QAAA,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,aAAa,GAAG,aAAa,GAAG,EAAE,GAAG,MAAM;;;IAItD,eAAe,GAAA;QACrB,MAAM,YAAY,GAAG,8BAA8B;AAEnD,QAAA,IAAI,CAAC,YAAY,EAAE,OAAO,EAAE;QAC5B,IAAI,CAAC,YAAY,GAAG,IAAI,WAAW,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,OAAO,EAAE,KAAK,IAAG;AACxF,YAAA,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC;AACjC,SAAC,CAAC;AAEF,QAAA,IAAI,IAAI,CAAC,mBAAmB,EAAE;YAC5B,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,qCAAqC,CAAC;;AAGhF,QAAA,IAAI,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE;AAC9B,YAAA,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,IAAI,CAAC;;;;AAKlF,QAAA,IAAI,CAAC,KAAK,CAAC,aAAc,CAAC,YAAY,CAAC,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC;;QAG7E,IAAI,CAAC,IAAI,CAAC,mBAAmB,IAAI,OAAO,qBAAqB,KAAK,WAAW,EAAE;AAC7E,YAAA,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,MAAK;AAClC,gBAAA,qBAAqB,CAAC,MAAM,IAAI,CAAC,YAAY,EAAE,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;AACrF,aAAC,CAAC;;aACG;YACL,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,YAAY,CAAC;;;AAIzD;;;;;;AAMG;IACK,oBAAoB,GAAA;AAC1B,QAAA,IAAI,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE;YAC1B,IAAI,CAAC,KAAK,CAAC,UAAW,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC;;;;IAKlD,cAAc,GAAA;AACZ,QAAA,IAAI,IAAI,CAAC,mBAAmB,EAAE;AAC5B,YAAA,IAAI,CAAC,YAAY,EAAE,OAAO,EAAE;AAC5B,YAAA,IAAI,CAAC,YAAY,GAAG,IAAI;;aACnB;AACL,YAAA,IAAI,CAAC,YAAY,EAAE,MAAM,EAAE;;;;AAKvB,IAAA,cAAc,CAAC,OAAoB,EAAE,UAA6B,EAAE,KAAc,EAAA;AACxF,QAAA,MAAM,OAAO,GAAG,WAAW,CAAC,UAAU,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAE9D,QAAA,IAAI,OAAO,CAAC,MAAM,EAAE;YAClB,KAAK,GAAG,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,GAAG,OAAO,CAAC;;;;IAK5E,uBAAuB,GAAA;;;;AAI7B,QAAA,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,MAAK;;;;AAIlC,YAAA,MAAM,YAAY,GAAG,IAAI,CAAC;AACvB,iBAAA,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;iBAC3D,SAAS,CAAC,MAAK;;;gBAGd,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE;oBAClE,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE;AACzC,wBAAA,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,KAAK,CAAC;;oBAGjE,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,aAAa,EAAE;wBAC1C,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,KAAK,CAAC,aAAa;AACnD,wBAAA,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;;oBAGrB,YAAY,CAAC,WAAW,EAAE;;AAE9B,aAAC,CAAC;AACN,SAAC,CAAC;;;IAII,sBAAsB,GAAA;AAC5B,QAAA,MAAM,cAAc,GAAG,IAAI,CAAC,eAAe;QAC3C,cAAc,EAAE,OAAO,EAAE;AACzB,QAAA,cAAc,EAAE,MAAM,IAAI;;AAE7B;;ACrfD;AACA;AAEA;AACA,MAAM,gBAAgB,GAAG,6CAA6C;AAEtE;AACA,MAAM,cAAc,GAAG,eAAe;AActC;;;;;;AAMG;MACU,iCAAiC,CAAA;AAkGlC,IAAA,cAAA;AACA,IAAA,SAAA;AACA,IAAA,SAAA;AACA,IAAA,iBAAA;;AAnGF,IAAA,WAAW;;AAGX,IAAA,gBAAgB;;IAGhB,oBAAoB,GAAG,EAAC,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAC;;IAG5C,SAAS,GAAG,KAAK;;IAGjB,QAAQ,GAAG,IAAI;;IAGf,cAAc,GAAG,KAAK;;IAGtB,sBAAsB,GAAG,IAAI;;IAG7B,eAAe,GAAG,KAAK;;AAGvB,IAAA,WAAW;;AAGX,IAAA,YAAY;;AAGZ,IAAA,aAAa;;AAGb,IAAA,cAAc;;IAGd,eAAe,GAAG,CAAC;;IAGnB,YAAY,GAAoB,EAAE;;IAG1C,mBAAmB,GAA6B,EAAE;;AAGlD,IAAA,OAAO;;AAGC,IAAA,KAAK;;AAGL,IAAA,WAAW;AAEnB;;;AAGG;AACK,IAAA,YAAY;;AAGZ,IAAA,aAAa;;AAGb,IAAA,qBAAqB;;AAGZ,IAAA,gBAAgB,GAAG,IAAI,OAAO,EAAkC;;AAGzE,IAAA,mBAAmB,GAAG,YAAY,CAAC,KAAK;;IAGxC,QAAQ,GAAG,CAAC;;IAGZ,QAAQ,GAAG,CAAC;;AAGZ,IAAA,wBAAwB;;IAGxB,oBAAoB,GAAa,EAAE;;AAGnC,IAAA,mBAAmB;;AAG3B,IAAA,eAAe,GAA+C,IAAI,CAAC,gBAAgB;;AAGnF,IAAA,IAAI,SAAS,GAAA;QACX,OAAO,IAAI,CAAC,mBAAmB;;IAGjC,WACE,CAAA,WAAoD,EAC5C,cAA6B,EAC7B,SAAmB,EACnB,SAAmB,EACnB,iBAAmC,EAAA;QAHnC,IAAc,CAAA,cAAA,GAAd,cAAc;QACd,IAAS,CAAA,SAAA,GAAT,SAAS;QACT,IAAS,CAAA,SAAA,GAAT,SAAS;QACT,IAAiB,CAAA,iBAAA,GAAjB,iBAAiB;AAEzB,QAAA,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC;;;AAI7B,IAAA,MAAM,CAAC,UAAsB,EAAA;QAC3B,IACE,IAAI,CAAC,WAAW;YAChB,UAAU,KAAK,IAAI,CAAC,WAAW;aAC9B,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,CAAC,EAC/C;AACA,YAAA,MAAM,KAAK,CAAC,0DAA0D,CAAC;;QAGzE,IAAI,CAAC,kBAAkB,EAAE;QAEzB,UAAU,CAAC,WAAW,CAAC,SAAS,CAAC,GAAG,CAAC,gBAAgB,CAAC;AAEtD,QAAA,IAAI,CAAC,WAAW,GAAG,UAAU;AAC7B,QAAA,IAAI,CAAC,YAAY,GAAG,UAAU,CAAC,WAAW;AAC1C,QAAA,IAAI,CAAC,KAAK,GAAG,UAAU,CAAC,cAAc;AACtC,QAAA,IAAI,CAAC,WAAW,GAAG,KAAK;AACxB,QAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI;AAC5B,QAAA,IAAI,CAAC,aAAa,GAAG,IAAI;AACzB,QAAA,IAAI,CAAC,mBAAmB,CAAC,WAAW,EAAE;AACtC,QAAA,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC,SAAS,CAAC,MAAK;;;;AAIrE,YAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI;YAC5B,IAAI,CAAC,KAAK,EAAE;AACd,SAAC,CAAC;;AAGJ;;;;;;;;;;;;;AAaG;IACH,KAAK,GAAA;;QAEH,IAAI,IAAI,CAAC,WAAW,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE;YACjD;;;;;AAMF,QAAA,IAAI,CAAC,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,aAAa,EAAE;YACxE,IAAI,CAAC,mBAAmB,EAAE;YAC1B;;QAGF,IAAI,CAAC,kBAAkB,EAAE;QACzB,IAAI,CAAC,0BAA0B,EAAE;QACjC,IAAI,CAAC,uBAAuB,EAAE;;;;AAK9B,QAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,wBAAwB,EAAE;AACpD,QAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,cAAc,EAAE;QACxC,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,qBAAqB,EAAE;AACtD,QAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,iBAAiB,CAAC,mBAAmB,EAAE,CAAC,qBAAqB,EAAE;AAE1F,QAAA,MAAM,UAAU,GAAG,IAAI,CAAC,WAAW;AACnC,QAAA,MAAM,WAAW,GAAG,IAAI,CAAC,YAAY;AACrC,QAAA,MAAM,YAAY,GAAG,IAAI,CAAC,aAAa;AACvC,QAAA,MAAM,aAAa,GAAG,IAAI,CAAC,cAAc;;QAGzC,MAAM,YAAY,GAAkB,EAAE;;AAGtC,QAAA,IAAI,QAAsC;;;AAI1C,QAAA,KAAK,IAAI,GAAG,IAAI,IAAI,CAAC,mBAAmB,EAAE;;AAExC,YAAA,IAAI,WAAW,GAAG,IAAI,CAAC,eAAe,CAAC,UAAU,EAAE,aAAa,EAAE,GAAG,CAAC;;;;AAKtE,YAAA,IAAI,YAAY,GAAG,IAAI,CAAC,gBAAgB,CAAC,WAAW,EAAE,WAAW,EAAE,GAAG,CAAC;;AAGvE,YAAA,IAAI,UAAU,GAAG,IAAI,CAAC,cAAc,CAAC,YAAY,EAAE,WAAW,EAAE,YAAY,EAAE,GAAG,CAAC;;AAGlF,YAAA,IAAI,UAAU,CAAC,0BAA0B,EAAE;AACzC,gBAAA,IAAI,CAAC,SAAS,GAAG,KAAK;AACtB,gBAAA,IAAI,CAAC,cAAc,CAAC,GAAG,EAAE,WAAW,CAAC;gBACrC;;;;YAKF,IAAI,IAAI,CAAC,6BAA6B,CAAC,UAAU,EAAE,YAAY,EAAE,YAAY,CAAC,EAAE;;;gBAG9E,YAAY,CAAC,IAAI,CAAC;AAChB,oBAAA,QAAQ,EAAE,GAAG;AACb,oBAAA,MAAM,EAAE,WAAW;oBACnB,WAAW;oBACX,eAAe,EAAE,IAAI,CAAC,yBAAyB,CAAC,WAAW,EAAE,GAAG,CAAC;AAClE,iBAAA,CAAC;gBAEF;;;;;AAMF,YAAA,IAAI,CAAC,QAAQ,IAAI,QAAQ,CAAC,UAAU,CAAC,WAAW,GAAG,UAAU,CAAC,WAAW,EAAE;AACzE,gBAAA,QAAQ,GAAG,EAAC,UAAU,EAAE,YAAY,EAAE,WAAW,EAAE,QAAQ,EAAE,GAAG,EAAE,WAAW,EAAC;;;;;AAMlF,QAAA,IAAI,YAAY,CAAC,MAAM,EAAE;YACvB,IAAI,OAAO,GAAuB,IAAI;AACtC,YAAA,IAAI,SAAS,GAAG,CAAC,CAAC;AAClB,YAAA,KAAK,MAAM,GAAG,IAAI,YAAY,EAAE;gBAC9B,MAAM,KAAK,GACT,GAAG,CAAC,eAAe,CAAC,KAAK,GAAG,GAAG,CAAC,eAAe,CAAC,MAAM,IAAI,GAAG,CAAC,QAAQ,CAAC,MAAM,IAAI,CAAC,CAAC;AACrF,gBAAA,IAAI,KAAK,GAAG,SAAS,EAAE;oBACrB,SAAS,GAAG,KAAK;oBACjB,OAAO,GAAG,GAAG;;;AAIjB,YAAA,IAAI,CAAC,SAAS,GAAG,KAAK;YACtB,IAAI,CAAC,cAAc,CAAC,OAAQ,CAAC,QAAQ,EAAE,OAAQ,CAAC,MAAM,CAAC;YACvD;;;;AAKF,QAAA,IAAI,IAAI,CAAC,QAAQ,EAAE;;AAEjB,YAAA,IAAI,CAAC,SAAS,GAAG,IAAI;YACrB,IAAI,CAAC,cAAc,CAAC,QAAS,CAAC,QAAQ,EAAE,QAAS,CAAC,WAAW,CAAC;YAC9D;;;;QAKF,IAAI,CAAC,cAAc,CAAC,QAAS,CAAC,QAAQ,EAAE,QAAS,CAAC,WAAW,CAAC;;IAGhE,MAAM,GAAA;QACJ,IAAI,CAAC,kBAAkB,EAAE;AACzB,QAAA,IAAI,CAAC,aAAa,GAAG,IAAI;AACzB,QAAA,IAAI,CAAC,mBAAmB,GAAG,IAAI;AAC/B,QAAA,IAAI,CAAC,mBAAmB,CAAC,WAAW,EAAE;;;IAIxC,OAAO,GAAA;AACL,QAAA,IAAI,IAAI,CAAC,WAAW,EAAE;YACpB;;;;AAKF,QAAA,IAAI,IAAI,CAAC,YAAY,EAAE;AACrB,YAAA,YAAY,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE;AACpC,gBAAA,GAAG,EAAE,EAAE;AACP,gBAAA,IAAI,EAAE,EAAE;AACR,gBAAA,KAAK,EAAE,EAAE;AACT,gBAAA,MAAM,EAAE,EAAE;AACV,gBAAA,MAAM,EAAE,EAAE;AACV,gBAAA,KAAK,EAAE,EAAE;AACT,gBAAA,UAAU,EAAE,EAAE;AACd,gBAAA,cAAc,EAAE,EAAE;AACI,aAAA,CAAC;;AAG3B,QAAA,IAAI,IAAI,CAAC,KAAK,EAAE;YACd,IAAI,CAAC,0BAA0B,EAAE;;AAGnC,QAAA,IAAI,IAAI,CAAC,WAAW,EAAE;YACpB,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,SAAS,CAAC,MAAM,CAAC,gBAAgB,CAAC;;QAGjE,IAAI,CAAC,MAAM,EAAE;AACb,QAAA,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE;QAChC,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,YAAY,GAAG,IAAK;AAC5C,QAAA,IAAI,CAAC,WAAW,GAAG,IAAI;;AAGzB;;;;AAIG;IACH,mBAAmB,GAAA;QACjB,IAAI,IAAI,CAAC,WAAW,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE;YACjD;;AAGF,QAAA,MAAM,YAAY,GAAG,IAAI,CAAC,aAAa;QAEvC,IAAI,YAAY,EAAE;AAChB,YAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,cAAc,EAAE;YACxC,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,qBAAqB,EAAE;AACtD,YAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,wBAAwB,EAAE;AACpD,YAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,iBAAiB,CAAC,mBAAmB,EAAE,CAAC,qBAAqB,EAAE;AAE1F,YAAA,MAAM,WAAW,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,cAAc,EAAE,YAAY,CAAC;AAC7F,YAAA,IAAI,CAAC,cAAc,CAAC,YAAY,EAAE,WAAW,CAAC;;aACzC;YACL,IAAI,CAAC,KAAK,EAAE;;;AAIhB;;;;AAIG;AACH,IAAA,wBAAwB,CAAC,WAA4B,EAAA;AACnD,QAAA,IAAI,CAAC,YAAY,GAAG,WAAW;AAC/B,QAAA,OAAO,IAAI;;AAGb;;;AAGG;AACH,IAAA,aAAa,CAAC,SAA8B,EAAA;AAC1C,QAAA,IAAI,CAAC,mBAAmB,GAAG,SAAS;;;AAIpC,QAAA,IAAI,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,aAAc,CAAC,KAAK,CAAC,CAAC,EAAE;AACjD,YAAA,IAAI,CAAC,aAAa,GAAG,IAAI;;QAG3B,IAAI,CAAC,kBAAkB,EAAE;AAEzB,QAAA,OAAO,IAAI;;AAGb;;;AAGG;AACH,IAAA,kBAAkB,CAAC,MAAc,EAAA;AAC/B,QAAA,IAAI,CAAC,eAAe,GAAG,MAAM;AAC7B,QAAA,OAAO,IAAI;;;IAIb,sBAAsB,CAAC,kBAAkB,GAAG,IAAI,EAAA;AAC9C,QAAA,IAAI,CAAC,sBAAsB,GAAG,kBAAkB;AAChD,QAAA,OAAO,IAAI;;;IAIb,iBAAiB,CAAC,aAAa,GAAG,IAAI,EAAA;AACpC,QAAA,IAAI,CAAC,cAAc,GAAG,aAAa;AACnC,QAAA,OAAO,IAAI;;;IAIb,QAAQ,CAAC,OAAO,GAAG,IAAI,EAAA;AACrB,QAAA,IAAI,CAAC,QAAQ,GAAG,OAAO;AACvB,QAAA,OAAO,IAAI;;AAGb;;;;;AAKG;IACH,kBAAkB,CAAC,QAAQ,GAAG,IAAI,EAAA;AAChC,QAAA,IAAI,CAAC,eAAe,GAAG,QAAQ;AAC/B,QAAA,OAAO,IAAI;;AAGb;;;;;;AAMG;AACH,IAAA,SAAS,CAAC,MAA+C,EAAA;AACvD,QAAA,IAAI,CAAC,OAAO,GAAG,MAAM;AACrB,QAAA,OAAO,IAAI;;AAGb;;;AAGG;AACH,IAAA,kBAAkB,CAAC,MAAc,EAAA;AAC/B,QAAA,IAAI,CAAC,QAAQ,GAAG,MAAM;AACtB,QAAA,OAAO,IAAI;;AAGb;;;AAGG;AACH,IAAA,kBAAkB,CAAC,MAAc,EAAA;AAC/B,QAAA,IAAI,CAAC,QAAQ,GAAG,MAAM;AACtB,QAAA,OAAO,IAAI;;AAGb;;;;;;;AAOG;AACH,IAAA,qBAAqB,CAAC,QAAgB,EAAA;AACpC,QAAA,IAAI,CAAC,wBAAwB,GAAG,QAAQ;AACxC,QAAA,OAAO,IAAI;;AAGb;;AAEG;AACK,IAAA,eAAe,CACrB,UAAsB,EACtB,aAAyB,EACzB,GAAsB,EAAA;AAEtB,QAAA,IAAI,CAAS;AACb,QAAA,IAAI,GAAG,CAAC,OAAO,IAAI,QAAQ,EAAE;;;YAG3B,CAAC,GAAG,UAAU,CAAC,IAAI,GAAG,UAAU,CAAC,KAAK,GAAG,CAAC;;aACrC;AACL,YAAA,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,UAAU,CAAC,KAAK,GAAG,UAAU,CAAC,IAAI;AACjE,YAAA,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,UAAU,CAAC,IAAI,GAAG,UAAU,CAAC,KAAK;AAC/D,YAAA,CAAC,GAAG,GAAG,CAAC,OAAO,IAAI,OAAO,GAAG,MAAM,GAAG,IAAI;;;;AAK5C,QAAA,IAAI,aAAa,CAAC,IAAI,GAAG,CAAC,EAAE;AAC1B,YAAA,CAAC,IAAI,aAAa,CAAC,IAAI;;AAGzB,QAAA,IAAI,CAAS;AACb,QAAA,IAAI,GAAG,CAAC,OAAO,IAAI,QAAQ,EAAE;YAC3B,CAAC,GAAG,UAAU,CAAC,GAAG,GAAG,UAAU,CAAC,MAAM,GAAG,CAAC;;aACrC;AACL,YAAA,CAAC,GAAG,GAAG,CAAC,OAAO,IAAI,KAAK,GAAG,UAAU,CAAC,GAAG,GAAG,UAAU,CAAC,MAAM;;;;;;;AAQ/D,QAAA,IAAI,aAAa,CAAC,GAAG,GAAG,CAAC,EAAE;AACzB,YAAA,CAAC,IAAI,aAAa,CAAC,GAAG;;AAGxB,QAAA,OAAO,EAAC,CAAC,EAAE,CAAC,EAAC;;AAGf;;;AAGG;AACK,IAAA,gBAAgB,CACtB,WAAkB,EAClB,WAAuB,EACvB,GAAsB,EAAA;;;AAItB,QAAA,IAAI,aAAqB;AACzB,QAAA,IAAI,GAAG,CAAC,QAAQ,IAAI,QAAQ,EAAE;AAC5B,YAAA,aAAa,GAAG,CAAC,WAAW,CAAC,KAAK,GAAG,CAAC;;AACjC,aAAA,IAAI,GAAG,CAAC,QAAQ,KAAK,OAAO,EAAE;AACnC,YAAA,aAAa,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,WAAW,CAAC,KAAK,GAAG,CAAC;;aACjD;AACL,YAAA,aAAa,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,CAAC,WAAW,CAAC,KAAK;;AAGxD,QAAA,IAAI,aAAqB;AACzB,QAAA,IAAI,GAAG,CAAC,QAAQ,IAAI,QAAQ,EAAE;AAC5B,YAAA,aAAa,GAAG,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC;;aAClC;AACL,YAAA,aAAa,GAAG,GAAG,CAAC,QAAQ,IAAI,KAAK,GAAG,CAAC,GAAG,CAAC,WAAW,CAAC,MAAM;;;QAIjE,OAAO;AACL,YAAA,CAAC,EAAE,WAAW,CAAC,CAAC,GAAG,aAAa;AAChC,YAAA,CAAC,EAAE,WAAW,CAAC,CAAC,GAAG,aAAa;SACjC;;;AAIK,IAAA,cAAc,CACpB,KAAY,EACZ,cAA0B,EAC1B,QAAoB,EACpB,QAA2B,EAAA;;;AAI3B,QAAA,MAAM,OAAO,GAAG,4BAA4B,CAAC,cAAc,CAAC;AAC5D,QAAA,IAAI,EAAC,CAAC,EAAE,CAAC,EAAC,GAAG,KAAK;QAClB,IAAI,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,GAAG,CAAC;QAC5C,IAAI,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,GAAG,CAAC;;QAG5C,IAAI,OAAO,EAAE;YACX,CAAC,IAAI,OAAO;;QAGd,IAAI,OAAO,EAAE;YACX,CAAC,IAAI,OAAO;;;AAId,QAAA,IAAI,YAAY,GAAG,CAAC,GAAG,CAAC;QACxB,IAAI,aAAa,GAAG,CAAC,GAAG,OAAO,CAAC,KAAK,GAAG,QAAQ,CAAC,KAAK;AACtD,QAAA,IAAI,WAAW,GAAG,CAAC,GAAG,CAAC;QACvB,IAAI,cAAc,GAAG,CAAC,GAAG,OAAO,CAAC,MAAM,GAAG,QAAQ,CAAC,MAAM;;AAGzD,QAAA,IAAI,YAAY,GAAG,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,KAAK,EAAE,YAAY,EAAE,aAAa,CAAC;AACtF,QAAA,IAAI,aAAa,GAAG,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,MAAM,EAAE,WAAW,EAAE,cAAc,CAAC;AACxF,QAAA,IAAI,WAAW,GAAG,YAAY,GAAG,aAAa;QAE9C,OAAO;YACL,WAAW;YACX,0BAA0B,EAAE,OAAO,CAAC,KAAK,GAAG,OAAO,CAAC,MAAM,KAAK,WAAW;AAC1E,YAAA,wBAAwB,EAAE,aAAa,KAAK,OAAO,CAAC,MAAM;AAC1D,YAAA,0BAA0B,EAAE,YAAY,IAAI,OAAO,CAAC,KAAK;SAC1D;;AAGH;;;;;AAKG;AACK,IAAA,6BAA6B,CAAC,GAAe,EAAE,KAAY,EAAE,QAAoB,EAAA;AACvF,QAAA,IAAI,IAAI,CAAC,sBAAsB,EAAE;YAC/B,MAAM,eAAe,GAAG,QAAQ,CAAC,MAAM,GAAG,KAAK,CAAC,CAAC;YACjD,MAAM,cAAc,GAAG,QAAQ,CAAC,KAAK,GAAG,KAAK,CAAC,CAAC;AAC/C,YAAA,MAAM,SAAS,GAAG,aAAa,CAAC,IAAI,CAAC,WAAW,CAAC,SAAS,EAAE,CAAC,SAAS,CAAC;AACvE,YAAA,MAAM,QAAQ,GAAG,aAAa,CAAC,IAAI,CAAC,WAAW,CAAC,SAAS,EAAE,CAAC,QAAQ,CAAC;AAErE,YAAA,MAAM,WAAW,GACf,GAAG,CAAC,wBAAwB,KAAK,SAAS,IAAI,IAAI,IAAI,SAAS,IAAI,eAAe,CAAC;AACrF,YAAA,MAAM,aAAa,GACjB,GAAG,CAAC,0BAA0B,KAAK,QAAQ,IAAI,IAAI,IAAI,QAAQ,IAAI,cAAc,CAAC;YAEpF,OAAO,WAAW,IAAI,aAAa;;AAErC,QAAA,OAAO,KAAK;;AAGd;;;;;;;;;;AAUG;AACK,IAAA,oBAAoB,CAC1B,KAAY,EACZ,cAA0B,EAC1B,cAAsC,EAAA;;;;QAKtC,IAAI,IAAI,CAAC,mBAAmB,IAAI,IAAI,CAAC,eAAe,EAAE;YACpD,OAAO;gBACL,CAAC,EAAE,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,mBAAmB,CAAC,CAAC;gBACvC,CAAC,EAAE,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,mBAAmB,CAAC,CAAC;aACxC;;;;AAKH,QAAA,MAAM,OAAO,GAAG,4BAA4B,CAAC,cAAc,CAAC;AAC5D,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,aAAa;;;QAInC,MAAM,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,GAAG,OAAO,CAAC,KAAK,GAAG,QAAQ,CAAC,KAAK,EAAE,CAAC,CAAC;QAC3E,MAAM,cAAc,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,GAAG,OAAO,CAAC,MAAM,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAC;QAC9E,MAAM,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,GAAG,cAAc,CAAC,GAAG,GAAG,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;QAC5E,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,GAAG,cAAc,CAAC,IAAI,GAAG,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;;QAG/E,IAAI,KAAK,GAAG,CAAC;QACb,IAAI,KAAK,GAAG,CAAC;;;;QAKb,IAAI,OAAO,CAAC,KAAK,IAAI,QAAQ,CAAC,KAAK,EAAE;AACnC,YAAA,KAAK,GAAG,YAAY,IAAI,CAAC,aAAa;;aACjC;YACL,KAAK,GAAG,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,eAAe,GAAG,QAAQ,CAAC,IAAI,GAAG,cAAc,CAAC,IAAI,GAAG,KAAK,CAAC,CAAC,GAAG,CAAC;;QAG5F,IAAI,OAAO,CAAC,MAAM,IAAI,QAAQ,CAAC,MAAM,EAAE;AACrC,YAAA,KAAK,GAAG,WAAW,IAAI,CAAC,cAAc;;aACjC;YACL,KAAK,GAAG,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,eAAe,GAAG,QAAQ,CAAC,GAAG,GAAG,cAAc,CAAC,GAAG,GAAG,KAAK,CAAC,CAAC,GAAG,CAAC;;AAG1F,QAAA,IAAI,CAAC,mBAAmB,GAAG,EAAC,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAC;QAE/C,OAAO;AACL,YAAA,CAAC,EAAE,KAAK,CAAC,CAAC,GAAG,KAAK;AAClB,YAAA,CAAC,EAAE,KAAK,CAAC,CAAC,GAAG,KAAK;SACnB;;AAGH;;;;AAIG;IACK,cAAc,CAAC,QAA2B,EAAE,WAAkB,EAAA;AACpE,QAAA,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC;AAClC,QAAA,IAAI,CAAC,wBAAwB,CAAC,WAAW,EAAE,QAAQ,CAAC;AACpD,QAAA,IAAI,CAAC,qBAAqB,CAAC,WAAW,EAAE,QAAQ,CAAC;AAEjD,QAAA,IAAI,QAAQ,CAAC,UAAU,EAAE;AACvB,YAAA,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,UAAU,CAAC;;;;;QAM5C,IAAI,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,MAAM,EAAE;AAC1C,YAAA,MAAM,gBAAgB,GAAG,IAAI,CAAC,oBAAoB,EAAE;;;AAIpD,YAAA,IACE,QAAQ,KAAK,IAAI,CAAC,aAAa;gBAC/B,CAAC,IAAI,CAAC,qBAAqB;gBAC3B,CAAC,uBAAuB,CAAC,IAAI,CAAC,qBAAqB,EAAE,gBAAgB,CAAC,EACtE;gBACA,MAAM,WAAW,GAAG,IAAI,8BAA8B,CAAC,QAAQ,EAAE,gBAAgB,CAAC;AAClF,gBAAA,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,WAAW,CAAC;;AAGzC,YAAA,IAAI,CAAC,qBAAqB,GAAG,gBAAgB;;;AAI/C,QAAA,IAAI,CAAC,aAAa,GAAG,QAAQ;AAC7B,QAAA,IAAI,CAAC,gBAAgB,GAAG,KAAK;;;AAIvB,IAAA,mBAAmB,CAAC,QAA2B,EAAA;AACrD,QAAA,IAAI,CAAC,IAAI,CAAC,wBAAwB,EAAE;YAClC;;AAGF,QAAA,MAAM,QAAQ,GAA4B,IAAI,CAAC,YAAa,CAAC,gBAAgB,CAC3E,IAAI,CAAC,wBAAwB,CAC9B;AACD,QAAA,IAAI,OAAoC;AACxC,QAAA,IAAI,OAAO,GAAgC,QAAQ,CAAC,QAAQ;AAE5D,QAAA,IAAI,QAAQ,CAAC,QAAQ,KAAK,QAAQ,EAAE;YAClC,OAAO,GAAG,QAAQ;;AACb,aAAA,IAAI,IAAI,CAAC,MAAM,EAAE,EAAE;AACxB,YAAA,OAAO,GAAG,QAAQ,CAAC,QAAQ,KAAK,OAAO,GAAG,OAAO,GAAG,MAAM;;aACrD;AACL,YAAA,OAAO,GAAG,QAAQ,CAAC,QAAQ,KAAK,OAAO,GAAG,MAAM,GAAG,OAAO;;AAG5D,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACxC,YAAA,QAAQ,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,eAAe,GAAG,CAAG,EAAA,OAAO,CAAI,CAAA,EAAA,OAAO,EAAE;;;AAI/D;;;;;AAKG;IACK,yBAAyB,CAAC,MAAa,EAAE,QAA2B,EAAA;AAC1E,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,aAAa;AACnC,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,EAAE;AAC3B,QAAA,IAAI,MAAc,EAAE,GAAW,EAAE,MAAc;AAE/C,QAAA,IAAI,QAAQ,CAAC,QAAQ,KAAK,KAAK,EAAE;;AAE/B,YAAA,GAAG,GAAG,MAAM,CAAC,CAAC;YACd,MAAM,GAAG,QAAQ,CAAC,MAAM,GAAG,GAAG,GAAG,IAAI,CAAC,eAAe;;AAChD,aAAA,IAAI,QAAQ,CAAC,QAAQ,KAAK,QAAQ,EAAE;;;;AAIzC,YAAA,MAAM,GAAG,QAAQ,CAAC,MAAM,GAAG,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,eAAe,GAAG,CAAC;YAC9D,MAAM,GAAG,QAAQ,CAAC,MAAM,GAAG,MAAM,GAAG,IAAI,CAAC,eAAe;;aACnD;;;;;YAKL,MAAM,8BAA8B,GAAG,IAAI,CAAC,GAAG,CAC7C,QAAQ,CAAC,MAAM,GAAG,MAAM,CAAC,CAAC,GAAG,QAAQ,CAAC,GAAG,EACzC,MAAM,CAAC,CAAC,CACT;AAED,YAAA,MAAM,cAAc,GAAG,IAAI,CAAC,oBAAoB,CAAC,MAAM;AAEvD,YAAA,MAAM,GAAG,8BAA8B,GAAG,CAAC;AAC3C,YAAA,GAAG,GAAG,MAAM,CAAC,CAAC,GAAG,8BAA8B;AAE/C,YAAA,IAAI,MAAM,GAAG,cAAc,IAAI,CAAC,IAAI,CAAC,gBAAgB,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;gBAC7E,GAAG,GAAG,MAAM,CAAC,CAAC,GAAG,cAAc,GAAG,CAAC;;;;QAKvC,MAAM,4BAA4B,GAChC,CAAC,QAAQ,CAAC,QAAQ,KAAK,OAAO,IAAI,CAAC,KAAK,MAAM,QAAQ,CAAC,QAAQ,KAAK,KAAK,IAAI,KAAK,CAAC;;QAGrF,MAAM,2BAA2B,GAC/B,CAAC,QAAQ,CAAC,QAAQ,KAAK,KAAK,IAAI,CAAC,KAAK,MAAM,QAAQ,CAAC,QAAQ,KAAK,OAAO,IAAI,KAAK,CAAC;AAErF,QAAA,IAAI,KAAa,EAAE,IAAY,EAAE,KAAa;QAE9C,IAAI,2BAA2B,EAAE;AAC/B,YAAA,KAAK,GAAG,QAAQ,CAAC,KAAK,GAAG,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,eAAe,GAAG,CAAC;YAC5D,KAAK,GAAG,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,eAAe;;aAClC,IAAI,4BAA4B,EAAE;AACvC,YAAA,IAAI,GAAG,MAAM,CAAC,CAAC;YACf,KAAK,GAAG,QAAQ,CAAC,KAAK,GAAG,MAAM,CAAC,CAAC;;aAC5B;;;;;YAKL,MAAM,8BAA8B,GAAG,IAAI,CAAC,GAAG,CAC7C,QAAQ,CAAC,KAAK,GAAG,MAAM,CAAC,CAAC,GAAG,QAAQ,CAAC,IAAI,EACzC,MAAM,CAAC,CAAC,CACT;AACD,YAAA,MAAM,aAAa,GAAG,IAAI,CAAC,oBAAoB,CAAC,KAAK;AAErD,YAAA,KAAK,GAAG,8BAA8B,GAAG,CAAC;AAC1C,YAAA,IAAI,GAAG,MAAM,CAAC,CAAC,GAAG,8BAA8B;AAEhD,YAAA,IAAI,KAAK,GAAG,aAAa,IAAI,CAAC,IAAI,CAAC,gBAAgB,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;gBAC3E,IAAI,GAAG,MAAM,CAAC,CAAC,GAAG,aAAa,GAAG,CAAC;;;QAIvC,OAAO,EAAC,GAAG,EAAE,GAAI,EAAE,IAAI,EAAE,IAAK,EAAE,MAAM,EAAE,MAAO,EAAE,KAAK,EAAE,KAAM,EAAE,KAAK,EAAE,MAAM,EAAC;;AAGhF;;;;;;AAMG;IACK,qBAAqB,CAAC,MAAa,EAAE,QAA2B,EAAA;QACtE,MAAM,eAAe,GAAG,IAAI,CAAC,yBAAyB,CAAC,MAAM,EAAE,QAAQ,CAAC;;;QAIxE,IAAI,CAAC,IAAI,CAAC,gBAAgB,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;AAClD,YAAA,eAAe,CAAC,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,eAAe,CAAC,MAAM,EAAE,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC;AAC3F,YAAA,eAAe,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,eAAe,CAAC,KAAK,EAAE,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC;;QAG1F,MAAM,MAAM,GAAG,EAAyB;AAExC,QAAA,IAAI,IAAI,CAAC,iBAAiB,EAAE,EAAE;YAC5B,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC,IAAI,GAAG,GAAG;AAC9B,YAAA,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC,KAAK,GAAG,MAAM,CAAC,SAAS,GAAG,MAAM,CAAC,QAAQ,GAAG,EAAE;YACtE,MAAM,CAAC,KAAK,GAAG,MAAM,CAAC,MAAM,GAAG,MAAM;;aAChC;YACL,MAAM,SAAS,GAAG,IAAI,CAAC,WAAW,CAAC,SAAS,EAAE,CAAC,SAAS;YACxD,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,SAAS,EAAE,CAAC,QAAQ;YAEtD,MAAM,CAAC,MAAM,GAAG,mBAAmB,CAAC,eAAe,CAAC,MAAM,CAAC;YAC3D,MAAM,CAAC,GAAG,GAAG,mBAAmB,CAAC,eAAe,CAAC,GAAG,CAAC;YACrD,MAAM,CAAC,MAAM,GAAG,mBAAmB,CAAC,eAAe,CAAC,MAAM,CAAC;YAC3D,MAAM,CAAC,KAAK,GAAG,mBAAmB,CAAC,eAAe,CAAC,KAAK,CAAC;YACzD,MAAM,CAAC,IAAI,GAAG,mBAAmB,CAAC,eAAe,CAAC,IAAI,CAAC;YACvD,MAAM,CAAC,KAAK,GAAG,mBAAmB,CAAC,eAAe,CAAC,KAAK,CAAC;;AAGzD,YAAA,IAAI,QAAQ,CAAC,QAAQ,KAAK,QAAQ,EAAE;AAClC,gBAAA,MAAM,CAAC,UAAU,GAAG,QAAQ;;iBACvB;AACL,gBAAA,MAAM,CAAC,UAAU,GAAG,QAAQ,CAAC,QAAQ,KAAK,KAAK,GAAG,UAAU,GAAG,YAAY;;AAG7E,YAAA,IAAI,QAAQ,CAAC,QAAQ,KAAK,QAAQ,EAAE;AAClC,gBAAA,MAAM,CAAC,cAAc,GAAG,QAAQ;;iBAC3B;AACL,gBAAA,MAAM,CAAC,cAAc,GAAG,QAAQ,CAAC,QAAQ,KAAK,QAAQ,GAAG,UAAU,GAAG,YAAY;;YAGpF,IAAI,SAAS,EAAE;AACb,gBAAA,MAAM,CAAC,SAAS,GAAG,mBAAmB,CAAC,SAAS,CAAC;;YAGnD,IAAI,QAAQ,EAAE;AACZ,gBAAA,MAAM,CAAC,QAAQ,GAAG,mBAAmB,CAAC,QAAQ,CAAC;;;AAInD,QAAA,IAAI,CAAC,oBAAoB,GAAG,eAAe;QAE3C,YAAY,CAAC,IAAI,CAAC,YAAa,CAAC,KAAK,EAAE,MAAM,CAAC;;;IAIxC,uBAAuB,GAAA;AAC7B,QAAA,YAAY,CAAC,IAAI,CAAC,YAAa,CAAC,KAAK,EAAE;AACrC,YAAA,GAAG,EAAE,GAAG;AACR,YAAA,IAAI,EAAE,GAAG;AACT,YAAA,KAAK,EAAE,GAAG;AACV,YAAA,MAAM,EAAE,GAAG;AACX,YAAA,MAAM,EAAE,EAAE;AACV,YAAA,KAAK,EAAE,EAAE;AACT,YAAA,UAAU,EAAE,EAAE;AACd,YAAA,cAAc,EAAE,EAAE;AACI,SAAA,CAAC;;;IAInB,0BAA0B,GAAA;AAChC,QAAA,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE;AAC7B,YAAA,GAAG,EAAE,EAAE;AACP,YAAA,IAAI,EAAE,EAAE;AACR,YAAA,MAAM,EAAE,EAAE;AACV,YAAA,KAAK,EAAE,EAAE;AACT,YAAA,QAAQ,EAAE,EAAE;AACZ,YAAA,SAAS,EAAE,EAAE;AACS,SAAA,CAAC;;;IAInB,wBAAwB,CAAC,WAAkB,EAAE,QAA2B,EAAA;QAC9E,MAAM,MAAM,GAAG,EAAyB;AACxC,QAAA,MAAM,gBAAgB,GAAG,IAAI,CAAC,iBAAiB,EAAE;AACjD,QAAA,MAAM,qBAAqB,GAAG,IAAI,CAAC,sBAAsB;QACzD,MAAM,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,SAAS,EAAE;QAE3C,IAAI,gBAAgB,EAAE;YACpB,MAAM,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC,yBAAyB,EAAE;AACtE,YAAA,YAAY,CAAC,MAAM,EAAE,IAAI,CAAC,iBAAiB,CAAC,QAAQ,EAAE,WAAW,EAAE,cAAc,CAAC,CAAC;AACnF,YAAA,YAAY,CAAC,MAAM,EAAE,IAAI,CAAC,iBAAiB,CAAC,QAAQ,EAAE,WAAW,EAAE,cAAc,CAAC,CAAC;;aAC9E;AACL,YAAA,MAAM,CAAC,QAAQ,GAAG,QAAQ;;;;;;;QAQ5B,IAAI,eAAe,GAAG,EAAE;QACxB,IAAI,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,GAAG,CAAC;QAC5C,IAAI,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,GAAG,CAAC;QAE5C,IAAI,OAAO,EAAE;AACX,YAAA,eAAe,IAAI,CAAA,WAAA,EAAc,OAAO,CAAA,IAAA,CAAM;;QAGhD,IAAI,OAAO,EAAE;AACX,YAAA,eAAe,IAAI,CAAA,WAAA,EAAc,OAAO,CAAA,GAAA,CAAK;;AAG/C,QAAA,MAAM,CAAC,SAAS,GAAG,eAAe,CAAC,IAAI,EAAE;;;;;;AAOzC,QAAA,IAAI,MAAM,CAAC,SAAS,EAAE;YACpB,IAAI,gBAAgB,EAAE;gBACpB,MAAM,CAAC,SAAS,GAAG,mBAAmB,CAAC,MAAM,CAAC,SAAS,CAAC;;iBACnD,IAAI,qBAAqB,EAAE;AAChC,gBAAA,MAAM,CAAC,SAAS,GAAG,EAAE;;;AAIzB,QAAA,IAAI,MAAM,CAAC,QAAQ,EAAE;YACnB,IAAI,gBAAgB,EAAE;gBACpB,MAAM,CAAC,QAAQ,GAAG,mBAAmB,CAAC,MAAM,CAAC,QAAQ,CAAC;;iBACjD,IAAI,qBAAqB,EAAE;AAChC,gBAAA,MAAM,CAAC,QAAQ,GAAG,EAAE;;;QAIxB,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,MAAM,CAAC;;;AAIhC,IAAA,iBAAiB,CACvB,QAA2B,EAC3B,WAAkB,EAClB,cAAsC,EAAA;;;QAItC,IAAI,MAAM,GAAG,EAAC,GAAG,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAwB;AACzD,QAAA,IAAI,YAAY,GAAG,IAAI,CAAC,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAAC,YAAY,EAAE,QAAQ,CAAC;AAElF,QAAA,IAAI,IAAI,CAAC,SAAS,EAAE;AAClB,YAAA,YAAY,GAAG,IAAI,CAAC,oBAAoB,CAAC,YAAY,EAAE,IAAI,CAAC,YAAY,EAAE,cAAc,CAAC;;;;AAK3F,QAAA,IAAI,QAAQ,CAAC,QAAQ,KAAK,QAAQ,EAAE;;;YAGlC,MAAM,cAAc,GAAG,IAAI,CAAC,SAAS,CAAC,eAAgB,CAAC,YAAY;AACnE,YAAA,MAAM,CAAC,MAAM,GAAG,GAAG,cAAc,IAAI,YAAY,CAAC,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,IAAI;;aAC9E;YACL,MAAM,CAAC,GAAG,GAAG,mBAAmB,CAAC,YAAY,CAAC,CAAC,CAAC;;AAGlD,QAAA,OAAO,MAAM;;;AAIP,IAAA,iBAAiB,CACvB,QAA2B,EAC3B,WAAkB,EAClB,cAAsC,EAAA;;;QAItC,IAAI,MAAM,GAAG,EAAC,IAAI,EAAE,EAAE,EAAE,KAAK,EAAE,EAAE,EAAwB;AACzD,QAAA,IAAI,YAAY,GAAG,IAAI,CAAC,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAAC,YAAY,EAAE,QAAQ,CAAC;AAElF,QAAA,IAAI,IAAI,CAAC,SAAS,EAAE;AAClB,YAAA,YAAY,GAAG,IAAI,CAAC,oBAAoB,CAAC,YAAY,EAAE,IAAI,CAAC,YAAY,EAAE,cAAc,CAAC;;;;;;AAO3F,QAAA,IAAI,uBAAyC;AAE7C,QAAA,IAAI,IAAI,CAAC,MAAM,EAAE,EAAE;AACjB,YAAA,uBAAuB,GAAG,QAAQ,CAAC,QAAQ,KAAK,KAAK,GAAG,MAAM,GAAG,OAAO;;aACnE;AACL,YAAA,uBAAuB,GAAG,QAAQ,CAAC,QAAQ,KAAK,KAAK,GAAG,OAAO,GAAG,MAAM;;;;AAK1E,QAAA,IAAI,uBAAuB,KAAK,OAAO,EAAE;YACvC,MAAM,aAAa,GAAG,IAAI,CAAC,SAAS,CAAC,eAAgB,CAAC,WAAW;AACjE,YAAA,MAAM,CAAC,KAAK,GAAG,GAAG,aAAa,IAAI,YAAY,CAAC,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,IAAI;;aAC3E;YACL,MAAM,CAAC,IAAI,GAAG,mBAAmB,CAAC,YAAY,CAAC,CAAC,CAAC;;AAGnD,QAAA,OAAO,MAAM;;AAGf;;;AAGG;IACK,oBAAoB,GAAA;;AAE1B,QAAA,MAAM,YAAY,GAAG,IAAI,CAAC,cAAc,EAAE;QAC1C,MAAM,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,qBAAqB,EAAE;;;;QAKxD,MAAM,qBAAqB,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,UAAU,IAAG;YAC/D,OAAO,UAAU,CAAC,aAAa,EAAE,CAAC,aAAa,CAAC,qBAAqB,EAAE;AACzE,SAAC,CAAC;QAEF,OAAO;AACL,YAAA,eAAe,EAAE,2BAA2B,CAAC,YAAY,EAAE,qBAAqB,CAAC;AACjF,YAAA,mBAAmB,EAAE,4BAA4B,CAAC,YAAY,EAAE,qBAAqB,CAAC;AACtF,YAAA,gBAAgB,EAAE,2BAA2B,CAAC,aAAa,EAAE,qBAAqB,CAAC;AACnF,YAAA,oBAAoB,EAAE,4BAA4B,CAAC,aAAa,EAAE,qBAAqB,CAAC;SACzF;;;AAIK,IAAA,kBAAkB,CAAC,MAAc,EAAE,GAAG,SAAmB,EAAA;QAC/D,OAAO,SAAS,CAAC,MAAM,CAAC,CAAC,YAAoB,EAAE,eAAuB,KAAI;YACxE,OAAO,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,eAAe,EAAE,CAAC,CAAC;SACnD,EAAE,MAAM,CAAC;;;IAIJ,wBAAwB,GAAA;;;;;;QAM9B,MAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,eAAgB,CAAC,WAAW;QACzD,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,eAAgB,CAAC,YAAY;QAC3D,MAAM,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC,yBAAyB,EAAE;QAEtE,OAAO;AACL,YAAA,GAAG,EAAE,cAAc,CAAC,GAAG,GAAG,IAAI,CAAC,eAAe;AAC9C,YAAA,IAAI,EAAE,cAAc,CAAC,IAAI,GAAG,IAAI,CAAC,eAAe;YAChD,KAAK,EAAE,cAAc,CAAC,IAAI,GAAG,KAAK,GAAG,IAAI,CAAC,eAAe;YACzD,MAAM,EAAE,cAAc,CAAC,GAAG,GAAG,MAAM,GAAG,IAAI,CAAC,eAAe;AAC1D,YAAA,KAAK,EAAE,KAAK,GAAG,CAAC,GAAG,IAAI,CAAC,eAAe;AACvC,YAAA,MAAM,EAAE,MAAM,GAAG,CAAC,GAAG,IAAI,CAAC,eAAe;SAC1C;;;IAIK,MAAM,GAAA;QACZ,OAAO,IAAI,CAAC,WAAW,CAAC,YAAY,EAAE,KAAK,KAAK;;;IAI1C,iBAAiB,GAAA;QACvB,OAAO,CAAC,IAAI,CAAC,sBAAsB,IAAI,IAAI,CAAC,SAAS;;;IAI/C,UAAU,CAAC,QAA2B,EAAE,IAAe,EAAA;AAC7D,QAAA,IAAI,IAAI,KAAK,GAAG,EAAE;;;AAGhB,YAAA,OAAO,QAAQ,CAAC,OAAO,IAAI,IAAI,GAAG,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC,OAAO;;AAGpE,QAAA,OAAO,QAAQ,CAAC,OAAO,IAAI,IAAI,GAAG,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC,OAAO;;;IAI5D,kBAAkB,GAAA;AACxB,QAAA,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,EAAE;AACjD,YAAA,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,MAAM,EAAE;AACpC,gBAAA,MAAM,KAAK,CAAC,uEAAuE,CAAC;;;;AAKtF,YAAA,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,IAAI,IAAG;AACtC,gBAAA,0BAA0B,CAAC,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC;AACnD,gBAAA,wBAAwB,CAAC,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC;AACjD,gBAAA,0BAA0B,CAAC,UAAU,EAAE,IAAI,CAAC,QAAQ,CAAC;AACrD,gBAAA,wBAAwB,CAAC,UAAU,EAAE,IAAI,CAAC,QAAQ,CAAC;AACrD,aAAC,CAAC;;;;AAKE,IAAA,gBAAgB,CAAC,UAA6B,EAAA;AACpD,QAAA,IAAI,IAAI,CAAC,KAAK,EAAE;YACd,WAAW,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,QAAQ,IAAG;AACzC,gBAAA,IAAI,QAAQ,KAAK,EAAE,IAAI,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE;AACzE,oBAAA,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,QAAQ,CAAC;oBACxC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC;;AAEtC,aAAC,CAAC;;;;IAKE,kBAAkB,GAAA;AACxB,QAAA,IAAI,IAAI,CAAC,KAAK,EAAE;AACd,YAAA,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,QAAQ,IAAG;gBAC3C,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC;AACvC,aAAC,CAAC;AACF,YAAA,IAAI,CAAC,oBAAoB,GAAG,EAAE;;;;IAK1B,cAAc,GAAA;AACpB,QAAA,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO;AAE3B,QAAA,IAAI,MAAM,YAAY,UAAU,EAAE;AAChC,YAAA,OAAO,MAAM,CAAC,aAAa,CAAC,qBAAqB,EAAE;;;AAIrD,QAAA,IAAI,MAAM,YAAY,OAAO,EAAE;AAC7B,YAAA,OAAO,MAAM,CAAC,qBAAqB,EAAE;;AAGvC,QAAA,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,IAAI,CAAC;AAC/B,QAAA,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,IAAI,CAAC;;QAGjC,OAAO;YACL,GAAG,EAAE,MAAM,CAAC,CAAC;AACb,YAAA,MAAM,EAAE,MAAM,CAAC,CAAC,GAAG,MAAM;YACzB,IAAI,EAAE,MAAM,CAAC,CAAC;AACd,YAAA,KAAK,EAAE,MAAM,CAAC,CAAC,GAAG,KAAK;YACvB,MAAM;YACN,KAAK;SACN;;AAEJ;AAgED;AACA,SAAS,YAAY,CACnB,WAAgC,EAChC,MAA2B,EAAA;AAE3B,IAAA,KAAK,IAAI,GAAG,IAAI,MAAM,EAAE;AACtB,QAAA,IAAI,MAAM,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE;YAC9B,WAAW,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC;;;AAIlC,IAAA,OAAO,WAAW;AACpB;AAEA;;;AAGG;AACH,SAAS,aAAa,CAAC,KAAyC,EAAA;IAC9D,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,IAAI,IAAI,EAAE;AAC9C,QAAA,MAAM,CAAC,KAAK,EAAE,KAAK,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC,cAAc,CAAC;AAClD,QAAA,OAAO,CAAC,KAAK,IAAI,KAAK,KAAK,IAAI,GAAG,UAAU,CAAC,KAAK,CAAC,GAAG,IAAI;;IAG5D,OAAO,KAAK,IAAI,IAAI;AACtB;AAEA;;;;;AAKG;AACH,SAAS,4BAA4B,CAAC,UAAsB,EAAA;IAC1D,OAAO;QACL,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC;QAC/B,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,KAAK,CAAC;QACnC,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,MAAM,CAAC;QACrC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC;QACjC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,KAAK,CAAC;QACnC,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,MAAM,CAAC;KACtC;AACH;AAEA;AACA,SAAS,uBAAuB,CAAC,CAAsB,EAAE,CAAsB,EAAA;AAC7E,IAAA,IAAI,CAAC,KAAK,CAAC,EAAE;AACX,QAAA,OAAO,IAAI;;AAGb,IAAA,QACE,CAAC,CAAC,eAAe,KAAK,CAAC,CAAC,eAAe;AACvC,QAAA,CAAC,CAAC,mBAAmB,KAAK,CAAC,CAAC,mBAAmB;AAC/C,QAAA,CAAC,CAAC,gBAAgB,KAAK,CAAC,CAAC,gBAAgB;AACzC,QAAA,CAAC,CAAC,oBAAoB,KAAK,CAAC,CAAC,oBAAoB;AAErD;AAEa,MAAA,iCAAiC,GAAwB;AACpE,IAAA,EAAC,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAC;AACzE,IAAA,EAAC,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAC;AACzE,IAAA,EAAC,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAC;AACrE,IAAA,EAAC,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAC;;AAG1D,MAAA,oCAAoC,GAAwB;AACvE,IAAA,EAAC,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAC;AACpE,IAAA,EAAC,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAC;AAC1E,IAAA,EAAC,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAC;AACpE,IAAA,EAAC,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAC;;;ACtyC5E;AACA,MAAM,YAAY,GAAG,4BAA4B;AAEjD;;;;;AAKG;MACU,sBAAsB,CAAA;;AAEzB,IAAA,WAAW;IACX,YAAY,GAAG,QAAQ;IACvB,UAAU,GAAG,EAAE;IACf,aAAa,GAAG,EAAE;IAClB,WAAW,GAAG,EAAE;IAChB,UAAU,GAAG,EAAE;IACf,QAAQ,GAAG,EAAE;IACb,MAAM,GAAG,EAAE;IACX,OAAO,GAAG,EAAE;IACZ,WAAW,GAAG,KAAK;AAE3B,IAAA,MAAM,CAAC,UAAsB,EAAA;AAC3B,QAAA,MAAM,MAAM,GAAG,UAAU,CAAC,SAAS,EAAE;AAErC,QAAA,IAAI,CAAC,WAAW,GAAG,UAAU;QAE7B,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE;YAChC,UAAU,CAAC,UAAU,CAAC,EAAC,KAAK,EAAE,IAAI,CAAC,MAAM,EAAC,CAAC;;QAG7C,IAAI,IAAI,CAAC,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE;YAClC,UAAU,CAAC,UAAU,CAAC,EAAC,MAAM,EAAE,IAAI,CAAC,OAAO,EAAC,CAAC;;QAG/C,UAAU,CAAC,WAAW,CAAC,SAAS,CAAC,GAAG,CAAC,YAAY,CAAC;AAClD,QAAA,IAAI,CAAC,WAAW,GAAG,KAAK;;AAG1B;;;AAGG;IACH,GAAG,CAAC,QAAgB,EAAE,EAAA;AACpB,QAAA,IAAI,CAAC,aAAa,GAAG,EAAE;AACvB,QAAA,IAAI,CAAC,UAAU,GAAG,KAAK;AACvB,QAAA,IAAI,CAAC,WAAW,GAAG,YAAY;AAC/B,QAAA,OAAO,IAAI;;AAGb;;;AAGG;IACH,IAAI,CAAC,QAAgB,EAAE,EAAA;AACrB,QAAA,IAAI,CAAC,QAAQ,GAAG,KAAK;AACrB,QAAA,IAAI,CAAC,UAAU,GAAG,MAAM;AACxB,QAAA,OAAO,IAAI;;AAGb;;;AAGG;IACH,MAAM,CAAC,QAAgB,EAAE,EAAA;AACvB,QAAA,IAAI,CAAC,UAAU,GAAG,EAAE;AACpB,QAAA,IAAI,CAAC,aAAa,GAAG,KAAK;AAC1B,QAAA,IAAI,CAAC,WAAW,GAAG,UAAU;AAC7B,QAAA,OAAO,IAAI;;AAGb;;;AAGG;IACH,KAAK,CAAC,QAAgB,EAAE,EAAA;AACtB,QAAA,IAAI,CAAC,QAAQ,GAAG,KAAK;AACrB,QAAA,IAAI,CAAC,UAAU,GAAG,OAAO;AACzB,QAAA,OAAO,IAAI;;AAGb;;;;AAIG;IACH,KAAK,CAAC,QAAgB,EAAE,EAAA;AACtB,QAAA,IAAI,CAAC,QAAQ,GAAG,KAAK;AACrB,QAAA,IAAI,CAAC,UAAU,GAAG,OAAO;AACzB,QAAA,OAAO,IAAI;;AAGb;;;;AAIG;IACH,GAAG,CAAC,QAAgB,EAAE,EAAA;AACpB,QAAA,IAAI,CAAC,QAAQ,GAAG,KAAK;AACrB,QAAA,IAAI,CAAC,UAAU,GAAG,KAAK;AACvB,QAAA,OAAO,IAAI;;AAGb;;;;;AAKG;IACH,KAAK,CAAC,QAAgB,EAAE,EAAA;AACtB,QAAA,IAAI,IAAI,CAAC,WAAW,EAAE;YACpB,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,EAAC,KAAK,EAAE,KAAK,EAAC,CAAC;;aACtC;AACL,YAAA,IAAI,CAAC,MAAM,GAAG,KAAK;;AAGrB,QAAA,OAAO,IAAI;;AAGb;;;;;AAKG;IACH,MAAM,CAAC,QAAgB,EAAE,EAAA;AACvB,QAAA,IAAI,IAAI,CAAC,WAAW,EAAE;YACpB,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,EAAC,MAAM,EAAE,KAAK,EAAC,CAAC;;aACvC;AACL,YAAA,IAAI,CAAC,OAAO,GAAG,KAAK;;AAGtB,QAAA,OAAO,IAAI;;AAGb;;;;;AAKG;IACH,kBAAkB,CAAC,SAAiB,EAAE,EAAA;AACpC,QAAA,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC;AACjB,QAAA,IAAI,CAAC,UAAU,GAAG,QAAQ;AAC1B,QAAA,OAAO,IAAI;;AAGb;;;;;AAKG;IACH,gBAAgB,CAAC,SAAiB,EAAE,EAAA;AAClC,QAAA,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC;AAChB,QAAA,IAAI,CAAC,WAAW,GAAG,QAAQ;AAC3B,QAAA,OAAO,IAAI;;AAGb;;;AAGG;IACH,KAAK,GAAA;;;;AAIH,QAAA,IAAI,CAAC,IAAI,CAAC,WAAW,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,WAAW,EAAE,EAAE;YACxD;;QAGF,MAAM,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,cAAc,CAAC,KAAK;QACpD,MAAM,YAAY,GAAG,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,KAAK;QACvD,MAAM,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,SAAS,EAAE;QAC3C,MAAM,EAAC,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAC,GAAG,MAAM;QACnD,MAAM,yBAAyB,GAC7B,CAAC,KAAK,KAAK,MAAM,IAAI,KAAK,KAAK,OAAO;aACrC,CAAC,QAAQ,IAAI,QAAQ,KAAK,MAAM,IAAI,QAAQ,KAAK,OAAO,CAAC;QAC5D,MAAM,uBAAuB,GAC3B,CAAC,MAAM,KAAK,MAAM,IAAI,MAAM,KAAK,OAAO;aACvC,CAAC,SAAS,IAAI,SAAS,KAAK,MAAM,IAAI,SAAS,KAAK,OAAO,CAAC;AAC/D,QAAA,MAAM,SAAS,GAAG,IAAI,CAAC,UAAU;AACjC,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ;AAC7B,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,SAAS,EAAE,CAAC,SAAS,KAAK,KAAK;QAC9D,IAAI,UAAU,GAAG,EAAE;QACnB,IAAI,WAAW,GAAG,EAAE;QACpB,IAAI,cAAc,GAAG,EAAE;QAEvB,IAAI,yBAAyB,EAAE;YAC7B,cAAc,GAAG,YAAY;;AACxB,aAAA,IAAI,SAAS,KAAK,QAAQ,EAAE;YACjC,cAAc,GAAG,QAAQ;YAEzB,IAAI,KAAK,EAAE;gBACT,WAAW,GAAG,OAAO;;iBAChB;gBACL,UAAU,GAAG,OAAO;;;aAEjB,IAAI,KAAK,EAAE;YAChB,IAAI,SAAS,KAAK,MAAM,IAAI,SAAS,KAAK,KAAK,EAAE;gBAC/C,cAAc,GAAG,UAAU;gBAC3B,UAAU,GAAG,OAAO;;iBACf,IAAI,SAAS,KAAK,OAAO,IAAI,SAAS,KAAK,OAAO,EAAE;gBACzD,cAAc,GAAG,YAAY;gBAC7B,WAAW,GAAG,OAAO;;;aAElB,IAAI,SAAS,KAAK,MAAM,IAAI,SAAS,KAAK,OAAO,EAAE;YACxD,cAAc,GAAG,YAAY;YAC7B,UAAU,GAAG,OAAO;;aACf,IAAI,SAAS,KAAK,OAAO,IAAI,SAAS,KAAK,KAAK,EAAE;YACvD,cAAc,GAAG,UAAU;YAC3B,WAAW,GAAG,OAAO;;AAGvB,QAAA,MAAM,CAAC,QAAQ,GAAG,IAAI,CAAC,YAAY;AACnC,QAAA,MAAM,CAAC,UAAU,GAAG,yBAAyB,GAAG,GAAG,GAAG,UAAU;AAChE,QAAA,MAAM,CAAC,SAAS,GAAG,uBAAuB,GAAG,GAAG,GAAG,IAAI,CAAC,UAAU;AAClE,QAAA,MAAM,CAAC,YAAY,GAAG,IAAI,CAAC,aAAa;AACxC,QAAA,MAAM,CAAC,WAAW,GAAG,yBAAyB,GAAG,GAAG,GAAG,WAAW;AAClE,QAAA,YAAY,CAAC,cAAc,GAAG,cAAc;AAC5C,QAAA,YAAY,CAAC,UAAU,GAAG,uBAAuB,GAAG,YAAY,GAAG,IAAI,CAAC,WAAW;;AAGrF;;;AAGG;IACH,OAAO,GAAA;QACL,IAAI,IAAI,CAAC,WAAW,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACzC;;QAGF,MAAM,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,cAAc,CAAC,KAAK;AACpD,QAAA,MAAM,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,WAAW;AAC3C,QAAA,MAAM,YAAY,GAAG,MAAM,CAAC,KAAK;AAEjC,QAAA,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,YAAY,CAAC;AACrC,QAAA,YAAY,CAAC,cAAc;AACzB,YAAA,YAAY,CAAC,UAAU;AACvB,gBAAA,MAAM,CAAC,SAAS;AAChB,oBAAA,MAAM,CAAC,YAAY;AACnB,wBAAA,MAAM,CAAC,UAAU;AACjB,4BAAA,MAAM,CAAC,WAAW;AAClB,gCAAA,MAAM,CAAC,QAAQ;AACb,oCAAA,EAAE;AAEN,QAAA,IAAI,CAAC,WAAW,GAAG,IAAK;AACxB,QAAA,IAAI,CAAC,WAAW,GAAG,IAAI;;AAE1B;;ACjPD;MAEa,sBAAsB,CAAA;AACzB,IAAA,cAAc,GAAG,MAAM,CAAC,aAAa,CAAC;AACtC,IAAA,SAAS,GAAG,MAAM,CAAC,QAAQ,CAAC;AAC5B,IAAA,SAAS,GAAG,MAAM,CAAC,QAAQ,CAAC;AAC5B,IAAA,iBAAiB,GAAG,MAAM,CAAC,gBAAgB,CAAC;AAGpD,IAAA,WAAA,GAAA;AAEA;;AAEG;IACH,MAAM,GAAA;QACJ,OAAO,IAAI,sBAAsB,EAAE;;AAGrC;;;AAGG;AACH,IAAA,mBAAmB,CACjB,MAA+C,EAAA;QAE/C,OAAO,IAAI,iCAAiC,CAC1C,MAAM,EACN,IAAI,CAAC,cAAc,EACnB,IAAI,CAAC,SAAS,EACd,IAAI,CAAC,SAAS,EACd,IAAI,CAAC,iBAAiB,CACvB;;uGA7BQ,sBAAsB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA;AAAtB,IAAA,OAAA,KAAA,GAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,sBAAsB,cADV,MAAM,EAAA,CAAA;;2FAClB,sBAAsB,EAAA,UAAA,EAAA,CAAA;kBADlC,UAAU;mBAAC,EAAC,UAAU,EAAE,MAAM,EAAC;;;ACWhC;;;;;;;AAOG;MAEU,OAAO,CAAA;AAClB,IAAA,gBAAgB,GAAG,MAAM,CAAC,qBAAqB,CAAC;AACxC,IAAA,iBAAiB,GAAG,MAAM,CAAC,gBAAgB,CAAC;AAC5C,IAAA,gBAAgB,GAAG,MAAM,CAAC,sBAAsB,CAAC;AACjD,IAAA,mBAAmB,GAAG,MAAM,CAAC,yBAAyB,CAAC;AACvD,IAAA,SAAS,GAAG,MAAM,CAAC,QAAQ,CAAC;AAC5B,IAAA,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC;AACxB,IAAA,SAAS,GAAG,MAAM,CAAC,QAAQ,CAAC;AAC5B,IAAA,eAAe,GAAG,MAAM,CAAC,cAAc,CAAC;AACxC,IAAA,SAAS,GAAG,MAAM,CAAC,QAAQ,CAAC;AAC5B,IAAA,uBAAuB,GAAG,MAAM,CAAC,6BAA6B,CAAC;IAC/D,qBAAqB,GAAG,MAAM,CAAC,qBAAqB,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;AACvE,IAAA,YAAY,GAAG,MAAM,CAAC,YAAY,CAAC;AACnC,IAAA,SAAS,GAAG,MAAM,CAAC,gBAAgB,CAAC,CAAC,cAAc,CAAC,IAAI,EAAE,IAAI,CAAC;AAE/D,IAAA,OAAO;AACP,IAAA,YAAY,GAAG,MAAM,CAAC,sBAAsB,CAAC;AAGrD,IAAA,WAAA,GAAA;AAEA;;;;AAIG;AACH,IAAA,MAAM,CAAC,MAAsB,EAAA;;;AAG3B,QAAA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,sBAAsB,CAAC;AAE9C,QAAA,MAAM,IAAI,GAAG,IAAI,CAAC,kBAAkB,EAAE;QACtC,MAAM,IAAI,GAAG,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC;QAC1C,MAAM,YAAY,GAAG,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC;AACnD,QAAA,MAAM,aAAa,GAAG,IAAI,aAAa,CAAC,MAAM,CAAC;AAE/C,QAAA,aAAa,CAAC,SAAS,GAAG,aAAa,CAAC,SAAS,IAAI,IAAI,CAAC,eAAe,CAAC,KAAK;QAE/E,OAAO,IAAI,UAAU,CACnB,YAAY,EACZ,IAAI,EACJ,IAAI,EACJ,aAAa,EACb,IAAI,CAAC,OAAO,EACZ,IAAI,CAAC,mBAAmB,EACxB,IAAI,CAAC,SAAS,EACd,IAAI,CAAC,SAAS,EACd,IAAI,CAAC,uBAAuB,EAC5B,IAAI,CAAC,qBAAqB,KAAK,gBAAgB,EAC/C,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,mBAAmB,CAAC,EACvC,IAAI,CAAC,SAAS,CACf;;AAGH;;;;AAIG;IACH,QAAQ,GAAA;QACN,OAAO,IAAI,CAAC,gBAAgB;;AAG9B;;;AAGG;AACK,IAAA,kBAAkB,CAAC,IAAiB,EAAA;QAC1C,MAAM,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC;QAEhD,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,cAAc,CAAC;AACjD,QAAA,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,kBAAkB,CAAC;AACtC,QAAA,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;AAEtB,QAAA,OAAO,IAAI;;AAGb;;;;AAIG;IACK,kBAAkB,GAAA;QACxB,MAAM,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC;QAChD,IAAI,CAAC,iBAAiB,CAAC,mBAAmB,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC;AAC9D,QAAA,OAAO,IAAI;;AAGb;;;;AAIG;AACK,IAAA,mBAAmB,CAAC,IAAiB,EAAA;;;AAG3C,QAAA,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YACjB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAiB,cAAc,CAAC;;AAGnE,QAAA,OAAO,IAAI,eAAe,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC;;uGApG3E,OAAO,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA;AAAP,IAAA,OAAA,KAAA,GAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,OAAO,cADK,MAAM,EAAA,CAAA;;2FAClB,OAAO,EAAA,UAAA,EAAA,CAAA;kBADnB,UAAU;mBAAC,EAAC,UAAU,EAAE,MAAM,EAAC;;;ACEhC;AACA,MAAM,mBAAmB,GAAwB;AAC/C,IAAA;AACE,QAAA,OAAO,EAAE,OAAO;AAChB,QAAA,OAAO,EAAE,QAAQ;AACjB,QAAA,QAAQ,EAAE,OAAO;AACjB,QAAA,QAAQ,EAAE,KAAK;AAChB,KAAA;AACD,IAAA;AACE,QAAA,OAAO,EAAE,OAAO;AAChB,QAAA,OAAO,EAAE,KAAK;AACd,QAAA,QAAQ,EAAE,OAAO;AACjB,QAAA,QAAQ,EAAE,QAAQ;AACnB,KAAA;AACD,IAAA;AACE,QAAA,OAAO,EAAE,KAAK;AACd,QAAA,OAAO,EAAE,KAAK;AACd,QAAA,QAAQ,EAAE,KAAK;AACf,QAAA,QAAQ,EAAE,QAAQ;AACnB,KAAA;AACD,IAAA;AACE,QAAA,OAAO,EAAE,KAAK;AACd,QAAA,OAAO,EAAE,QAAQ;AACjB,QAAA,QAAQ,EAAE,KAAK;AACf,QAAA,QAAQ,EAAE,KAAK;AAChB,KAAA;CACF;AAED;AACO,MAAM,qCAAqC,GAAG,IAAI,cAAc,CACrE,uCAAuC,EACvC;AACE,IAAA,UAAU,EAAE,MAAM;IAClB,OAAO,EAAE,MAAK;AACZ,QAAA,MAAM,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC;QAC/B,OAAO,MAAM,OAAO,CAAC,gBAAgB,CAAC,UAAU,EAAE;KACnD;AACF,CAAA,CACF;AAED;;;AAGG;MAKU,gBAAgB,CAAA;AAC3B,IAAA,UAAU,GAAG,MAAM,CAAC,UAAU,CAAC;AAG/B,IAAA,WAAA,GAAA;uGAJW,gBAAgB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAhB,gBAAgB,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,4DAAA,EAAA,QAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAAhB,gBAAgB,EAAA,UAAA,EAAA,CAAA;kBAJ5B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,4DAA4D;AACtE,oBAAA,QAAQ,EAAE,kBAAkB;AAC7B,iBAAA;;AAQD;;;AAGG;MAKU,mBAAmB,CAAA;AACtB,IAAA,QAAQ,GAAG,MAAM,CAAC,OAAO,CAAC;IAC1B,IAAI,GAAG,MAAM,CAAC,cAAc,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;AAE/C,IAAA,WAAW;AACX,IAAA,eAAe;AACf,IAAA,qBAAqB,GAAG,YAAY,CAAC,KAAK;AAC1C,IAAA,mBAAmB,GAAG,YAAY,CAAC,KAAK;AACxC,IAAA,mBAAmB,GAAG,YAAY,CAAC,KAAK;AACxC,IAAA,qBAAqB,GAAG,YAAY,CAAC,KAAK;AAC1C,IAAA,QAAQ;AACR,IAAA,QAAQ;AACR,IAAA,SAAS;AACT,IAAA,sBAAsB,GAAG,MAAM,CAAC,qCAAqC,CAAC;IACtE,oBAAoB,GAAG,KAAK;AAC5B,IAAA,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC;;AAIhC,IAAA,MAAM;;AAGiC,IAAA,SAAS;AAEhD;;;AAGG;AAC2C,IAAA,gBAAgB;;AAG9D,IAAA,IACI,OAAO,GAAA;QACT,OAAO,IAAI,CAAC,QAAQ;;IAEtB,IAAI,OAAO,CAAC,OAAe,EAAA;AACzB,QAAA,IAAI,CAAC,QAAQ,GAAG,OAAO;AAEvB,QAAA,IAAI,IAAI,CAAC,SAAS,EAAE;AAClB,YAAA,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,SAAS,CAAC;;;;AAKhD,IAAA,IACI,OAAO,GAAA;QACT,OAAO,IAAI,CAAC,QAAQ;;IAEtB,IAAI,OAAO,CAAC,OAAe,EAAA;AACzB,QAAA,IAAI,CAAC,QAAQ,GAAG,OAAO;AAEvB,QAAA,IAAI,IAAI,CAAC,SAAS,EAAE;AAClB,YAAA,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,SAAS,CAAC;;;;AAKb,IAAA,KAAK;;AAGJ,IAAA,MAAM;;AAGJ,IAAA,QAAQ;;AAGP,IAAA,SAAS;;AAGL,IAAA,aAAa;;AAGhB,IAAA,UAAU;;IAGN,cAAc,GAAW,CAAC;;AAG1B,IAAA,cAAc;;IAGxB,IAAI,GAAY,KAAK;;IAGb,YAAY,GAAY,KAAK;;AAGxB,IAAA,uBAAuB;;IAItE,WAAW,GAAY,KAAK;;IAI5B,YAAY,GAAY,KAAK;;IAI7B,kBAAkB,GAAY,KAAK;;IAInC,aAAa,GAAY,KAAK;;IAG0C,IAAI,GAAY,KAAK;;AAG7F,IAAA,IACI,mBAAmB,GAAA;QACrB,OAAO,IAAI,CAAC,oBAAoB;;IAElC,IAAI,mBAAmB,CAAC,KAAc,EAAA;AACpC,QAAA,IAAI,CAAC,oBAAoB,GAAG,KAAK;;;AAIhB,IAAA,aAAa,GAAG,IAAI,YAAY,EAAc;;AAG9C,IAAA,cAAc,GAAG,IAAI,YAAY,EAAkC;;AAGnE,IAAA,MAAM,GAAG,IAAI,YAAY,EAAQ;;AAGjC,IAAA,MAAM,GAAG,IAAI,YAAY,EAAQ;;AAGjC,IAAA,cAAc,GAAG,IAAI,YAAY,EAAiB;;AAGlD,IAAA,mBAAmB,GAAG,IAAI,YAAY,EAAc;;AAMvE,IAAA,WAAA,GAAA;AACE,QAAA,MAAM,WAAW,GAAG,MAAM,CAAmB,WAAW,CAAC;AACzD,QAAA,MAAM,gBAAgB,GAAG,MAAM,CAAC,gBAAgB,CAAC;QAEjD,IAAI,CAAC,eAAe,GAAG,IAAI,cAAc,CAAC,WAAW,EAAE,gBAAgB,CAAC;AACxE,QAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,sBAAsB,EAAE;;;AAIrD,IAAA,IAAI,UAAU,GAAA;QACZ,OAAO,IAAI,CAAC,WAAY;;;AAI1B,IAAA,IAAI,GAAG,GAAA;AACL,QAAA,OAAO,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,KAAK;;IAG5C,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,mBAAmB,CAAC,WAAW,EAAE;AACtC,QAAA,IAAI,CAAC,mBAAmB,CAAC,WAAW,EAAE;AACtC,QAAA,IAAI,CAAC,qBAAqB,CAAC,WAAW,EAAE;AACxC,QAAA,IAAI,CAAC,qBAAqB,CAAC,WAAW,EAAE;AACxC,QAAA,IAAI,CAAC,WAAW,EAAE,OAAO,EAAE;;AAG7B,IAAA,WAAW,CAAC,OAAsB,EAAA;AAChC,QAAA,IAAI,IAAI,CAAC,SAAS,EAAE;AAClB,YAAA,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,SAAS,CAAC;AAC5C,YAAA,IAAI,CAAC,WAAW,EAAE,UAAU,CAAC;gBAC3B,KAAK,EAAE,IAAI,CAAC,KAAK;gBACjB,QAAQ,EAAE,IAAI,CAAC,QAAQ;gBACvB,MAAM,EAAE,IAAI,CAAC,MAAM;gBACnB,SAAS,EAAE,IAAI,CAAC,SAAS;AAC1B,aAAA,CAAC;YAEF,IAAI,OAAO,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC,IAAI,EAAE;AAClC,gBAAA,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE;;;AAI1B,QAAA,IAAI,OAAO,CAAC,MAAM,CAAC,EAAE;AACnB,YAAA,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,aAAa,EAAE,GAAG,IAAI,CAAC,aAAa,EAAE;;;;IAKnD,cAAc,GAAA;AACpB,QAAA,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE;AAC7C,YAAA,IAAI,CAAC,SAAS,GAAG,mBAAmB;;AAGtC,QAAA,MAAM,UAAU,IAAI,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC,CAAC;QACjF,IAAI,CAAC,mBAAmB,GAAG,UAAU,CAAC,WAAW,EAAE,CAAC,SAAS,CAAC,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;QACvF,IAAI,CAAC,mBAAmB,GAAG,UAAU,CAAC,WAAW,EAAE,CAAC,SAAS,CAAC,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;QACvF,UAAU,CAAC,aAAa,EAAE,CAAC,SAAS,CAAC,CAAC,KAAoB,KAAI;AAC5D,YAAA,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC;AAE/B,YAAA,IAAI,KAAK,CAAC,OAAO,KAAK,MAAM,IAAI,CAAC,IAAI,CAAC,YAAY,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,EAAE;gBAC5E,KAAK,CAAC,cAAc,EAAE;gBACtB,IAAI,CAAC,aAAa,EAAE;;AAExB,SAAC,CAAC;QAEF,IAAI,CAAC,WAAW,CAAC,oBAAoB,EAAE,CAAC,SAAS,CAAC,CAAC,KAAiB,KAAI;AACtE,YAAA,MAAM,MAAM,GAAG,IAAI,CAAC,iBAAiB,EAAE;AACvC,YAAA,MAAM,MAAM,GAAG,eAAe,CAAC,KAAK,CAAmB;AAEvD,YAAA,IAAI,CAAC,MAAM,KAAK,MAAM,KAAK,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,EAAE;AAC9D,gBAAA,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,KAAK,CAAC;;AAExC,SAAC,CAAC;;;IAII,YAAY,GAAA;AAClB,QAAA,MAAM,gBAAgB,IAAI,IAAI,CAAC,SAAS;YACtC,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,uBAAuB,EAAE,CAAC;AAC1D,QAAA,MAAM,aAAa,GAAG,IAAI,aAAa,CAAC;AACtC,YAAA,SAAS,EAAE,IAAI,CAAC,IAAI,IAAI,KAAK;YAC7B,gBAAgB;YAChB,cAAc,EAAE,IAAI,CAAC,cAAc;YACnC,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,mBAAmB,EAAE,IAAI,CAAC,mBAAmB;AAC9C,SAAA,CAAC;QAEF,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,KAAK,CAAC,EAAE;AAClC,YAAA,aAAa,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK;;QAGlC,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;AACpC,YAAA,aAAa,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM;;QAGpC,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,KAAK,CAAC,EAAE;AACxC,YAAA,aAAa,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ;;QAGxC,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,KAAK,CAAC,EAAE;AAC1C,YAAA,aAAa,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS;;AAG1C,QAAA,IAAI,IAAI,CAAC,aAAa,EAAE;AACtB,YAAA,aAAa,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa;;AAGlD,QAAA,IAAI,IAAI,CAAC,UAAU,EAAE;AACnB,YAAA,aAAa,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU;;AAG5C,QAAA,OAAO,aAAa;;;AAId,IAAA,uBAAuB,CAAC,gBAAmD,EAAA;AACjF,QAAA,MAAM,SAAS,GAAwB,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,eAAe,KAAK;YAC5E,OAAO,EAAE,eAAe,CAAC,OAAO;YAChC,OAAO,EAAE,eAAe,CAAC,OAAO;YAChC,QAAQ,EAAE,eAAe,CAAC,QAAQ;YAClC,QAAQ,EAAE,eAAe,CAAC,QAAQ;AAClC,YAAA,OAAO,EAAE,eAAe,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO;AAChD,YAAA,OAAO,EAAE,eAAe,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO;AAChD,YAAA,UAAU,EAAE,eAAe,CAAC,UAAU,IAAI,SAAS;AACpD,SAAA,CAAC,CAAC;AAEH,QAAA,OAAO;AACJ,aAAA,SAAS,CAAC,IAAI,CAAC,UAAU,EAAE;aAC3B,aAAa,CAAC,SAAS;AACvB,aAAA,sBAAsB,CAAC,IAAI,CAAC,kBAAkB;AAC9C,aAAA,QAAQ,CAAC,IAAI,CAAC,IAAI;AAClB,aAAA,iBAAiB,CAAC,IAAI,CAAC,aAAa;AACpC,aAAA,kBAAkB,CAAC,IAAI,CAAC,cAAc;AACtC,aAAA,kBAAkB,CAAC,IAAI,CAAC,YAAY;AACpC,aAAA,qBAAqB,CAAC,IAAI,CAAC,uBAAuB,CAAC;;;IAIhD,uBAAuB,GAAA;AAC7B,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC,mBAAmB,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;AAChF,QAAA,IAAI,CAAC,uBAAuB,CAAC,QAAQ,CAAC;AACtC,QAAA,OAAO,QAAQ;;IAGT,UAAU,GAAA;AAChB,QAAA,IAAI,IAAI,CAAC,MAAM,YAAY,gBAAgB,EAAE;AAC3C,YAAA,OAAO,IAAI,CAAC,MAAM,CAAC,UAAU;;aACxB;YACL,OAAO,IAAI,CAAC,MAAM;;;IAId,iBAAiB,GAAA;AACvB,QAAA,IAAI,IAAI,CAAC,MAAM,YAAY,gBAAgB,EAAE;AAC3C,YAAA,OAAO,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,aAAa;;AAG7C,QAAA,IAAI,IAAI,CAAC,MAAM,YAAY,UAAU,EAAE;AACrC,YAAA,OAAO,IAAI,CAAC,MAAM,CAAC,aAAa;;QAGlC,IAAI,OAAO,OAAO,KAAK,WAAW,IAAI,IAAI,CAAC,MAAM,YAAY,OAAO,EAAE;YACpE,OAAO,IAAI,CAAC,MAAM;;AAGpB,QAAA,OAAO,IAAI;;;IAIb,aAAa,GAAA;AACX,QAAA,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACrB,IAAI,CAAC,cAAc,EAAE;;aAChB;;YAEL,IAAI,CAAC,WAAW,CAAC,SAAS,EAAE,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW;;QAG7D,IAAI,CAAC,IAAI,CAAC,WAAY,CAAC,WAAW,EAAE,EAAE;YACpC,IAAI,CAAC,WAAY,CAAC,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC;;AAGhD,QAAA,IAAI,IAAI,CAAC,WAAW,EAAE;AACpB,YAAA,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,WAAY,CAAC,aAAa,EAAE,CAAC,SAAS,CAAC,KAAK,IAAG;AAC/E,gBAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC;AAChC,aAAC,CAAC;;aACG;AACL,YAAA,IAAI,CAAC,qBAAqB,CAAC,WAAW,EAAE;;AAG1C,QAAA,IAAI,CAAC,qBAAqB,CAAC,WAAW,EAAE;;;QAIxC,IAAI,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE;AAC5C,YAAA,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,SAAS,CAAC;AACzC,iBAAA,IAAI,CAAC,SAAS,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC;iBAC9D,SAAS,CAAC,QAAQ,IAAG;AACpB,gBAAA,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gBAE1D,IAAI,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;AAC9C,oBAAA,IAAI,CAAC,qBAAqB,CAAC,WAAW,EAAE;;AAE5C,aAAC,CAAC;;AAGN,QAAA,IAAI,CAAC,IAAI,GAAG,IAAI;;;IAIlB,aAAa,GAAA;AACX,QAAA,IAAI,CAAC,WAAW,EAAE,MAAM,EAAE;AAC1B,QAAA,IAAI,CAAC,qBAAqB,CAAC,WAAW,EAAE;AACxC,QAAA,IAAI,CAAC,qBAAqB,CAAC,WAAW,EAAE;AACxC,QAAA,IAAI,CAAC,IAAI,GAAG,KAAK;;uGA/VR,mBAAmB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAnB,mBAAmB,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,qEAAA,EAAA,MAAA,EAAA,EAAA,MAAA,EAAA,CAAA,2BAAA,EAAA,QAAA,CAAA,EAAA,SAAA,EAAA,CAAA,8BAAA,EAAA,WAAA,CAAA,EAAA,gBAAA,EAAA,CAAA,qCAAA,EAAA,kBAAA,CAAA,EAAA,OAAA,EAAA,CAAA,4BAAA,EAAA,SAAA,CAAA,EAAA,OAAA,EAAA,CAAA,4BAAA,EAAA,SAAA,CAAA,EAAA,KAAA,EAAA,CAAA,0BAAA,EAAA,OAAA,CAAA,EAAA,MAAA,EAAA,CAAA,2BAAA,EAAA,QAAA,CAAA,EAAA,QAAA,EAAA,CAAA,6BAAA,EAAA,UAAA,CAAA,EAAA,SAAA,EAAA,CAAA,8BAAA,EAAA,WAAA,CAAA,EAAA,aAAA,EAAA,CAAA,kCAAA,EAAA,eAAA,CAAA,EAAA,UAAA,EAAA,CAAA,+BAAA,EAAA,YAAA,CAAA,EAAA,cAAA,EAAA,CAAA,mCAAA,EAAA,gBAAA,CAAA,EAAA,cAAA,EAAA,CAAA,mCAAA,EAAA,gBAAA,CAAA,EAAA,IAAA,EAAA,CAAA,yBAAA,EAAA,MAAA,CAAA,EAAA,YAAA,EAAA,CAAA,iCAAA,EAAA,cAAA,CAAA,EAAA,uBAAA,EAAA,CAAA,sCAAA,EAAA,yBAAA,CAAA,EAAA,WAAA,EAAA,CAAA,gCAAA,EAAA,aAAA,EA0F8B,gBAAgB,CAAA,EAAA,YAAA,EAAA,CAAA,iCAAA,EAAA,cAAA,EAIf,gBAAgB,CAAA,EAAA,kBAAA,EAAA,CAAA,uCAAA,EAAA,oBAAA,EAIV,gBAAgB,CAAA,EAAA,aAAA,EAAA,CAAA,kCAAA,EAAA,eAAA,EAIrB,gBAAgB,CAAA,EAAA,IAAA,EAAA,CAAA,yBAAA,EAAA,MAAA,EAIzB,gBAAgB,CAAA,EAAA,mBAAA,EAAA,CAAA,wCAAA,EAAA,qBAAA,EAGD,gBAAgB,CAAA,EAAA,EAAA,OAAA,EAAA,EAAA,aAAA,EAAA,eAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,MAAA,EAAA,QAAA,EAAA,MAAA,EAAA,QAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,mBAAA,EAAA,qBAAA,EAAA,EAAA,QAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,aAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FA7GzE,mBAAmB,EAAA,UAAA,EAAA,CAAA;kBAJ/B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,qEAAqE;AAC/E,oBAAA,QAAQ,EAAE,qBAAqB;AAChC,iBAAA;wDAoBC,MAAM,EAAA,CAAA;sBADL,KAAK;uBAAC,2BAA2B;gBAIK,SAAS,EAAA,CAAA;sBAA/C,KAAK;uBAAC,8BAA8B;gBAMS,gBAAgB,EAAA,CAAA;sBAA7D,KAAK;uBAAC,qCAAqC;gBAIxC,OAAO,EAAA,CAAA;sBADV,KAAK;uBAAC,4BAA4B;gBAc/B,OAAO,EAAA,CAAA;sBADV,KAAK;uBAAC,4BAA4B;gBAaA,KAAK,EAAA,CAAA;sBAAvC,KAAK;uBAAC,0BAA0B;gBAGG,MAAM,EAAA,CAAA;sBAAzC,KAAK;uBAAC,2BAA2B;gBAGI,QAAQ,EAAA,CAAA;sBAA7C,KAAK;uBAAC,6BAA6B;gBAGG,SAAS,EAAA,CAAA;sBAA/C,KAAK;uBAAC,8BAA8B;gBAGM,aAAa,EAAA,CAAA;sBAAvD,KAAK;uBAAC,kCAAkC;gBAGD,UAAU,EAAA,CAAA;sBAAjD,KAAK;uBAAC,+BAA+B;gBAGM,cAAc,EAAA,CAAA;sBAAzD,KAAK;uBAAC,mCAAmC;gBAGE,cAAc,EAAA,CAAA;sBAAzD,KAAK;uBAAC,mCAAmC;gBAGR,IAAI,EAAA,CAAA;sBAArC,KAAK;uBAAC,yBAAyB;gBAGU,YAAY,EAAA,CAAA;sBAArD,KAAK;uBAAC,iCAAiC;gBAGO,uBAAuB,EAAA,CAAA;sBAArE,KAAK;uBAAC,sCAAsC;gBAI7C,WAAW,EAAA,CAAA;sBADV,KAAK;AAAC,gBAAA,IAAA,EAAA,CAAA,EAAC,KAAK,EAAE,gCAAgC,EAAE,SAAS,EAAE,gBAAgB,EAAC;gBAK7E,YAAY,EAAA,CAAA;sBADX,KAAK;AAAC,gBAAA,IAAA,EAAA,CAAA,EAAC,KAAK,EAAE,iCAAiC,EAAE,SAAS,EAAE,gBAAgB,EAAC;gBAK9E,kBAAkB,EAAA,CAAA;sBADjB,KAAK;AAAC,gBAAA,IAAA,EAAA,CAAA,EAAC,KAAK,EAAE,uCAAuC,EAAE,SAAS,EAAE,gBAAgB,EAAC;gBAKpF,aAAa,EAAA,CAAA;sBADZ,KAAK;AAAC,gBAAA,IAAA,EAAA,CAAA,EAAC,KAAK,EAAE,kCAAkC,EAAE,SAAS,EAAE,gBAAgB,EAAC;gBAIP,IAAI,EAAA,CAAA;sBAA3E,KAAK;AAAC,gBAAA,IAAA,EAAA,CAAA,EAAC,KAAK,EAAE,yBAAyB,EAAE,SAAS,EAAE,gBAAgB,EAAC;gBAIlE,mBAAmB,EAAA,CAAA;sBADtB,KAAK;AAAC,gBAAA,IAAA,EAAA,CAAA,EAAC,KAAK,EAAE,wCAAwC,EAAE,SAAS,EAAE,gBAAgB,EAAC;gBASlE,aAAa,EAAA,CAAA;sBAA/B;gBAGkB,cAAc,EAAA,CAAA;sBAAhC;gBAGkB,MAAM,EAAA,CAAA;sBAAxB;gBAGkB,MAAM,EAAA,CAAA;sBAAxB;gBAGkB,cAAc,EAAA,CAAA;sBAAhC;gBAGkB,mBAAmB,EAAA,CAAA;sBAArC;;AA8NH;;;;AAIG;AACG,SAAU,sDAAsD,CACpE,OAAgB,EAAA;IAEhB,OAAO,MAAM,OAAO,CAAC,gBAAgB,CAAC,UAAU,EAAE;AACpD;AAEA;;;;AAIG;AACI,MAAM,8CAA8C,GAAG;AAC5D,IAAA,OAAO,EAAE,qCAAqC;IAC9C,IAAI,EAAE,CAAC,OAAO,CAAC;AACf,IAAA,UAAU,EAAE,sDAAsD;CACnE;;MCvcY,aAAa,CAAA;uGAAb,aAAa,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA;AAAb,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,aAAa,EAJd,OAAA,EAAA,CAAA,UAAU,EAAE,YAAY,EAAE,eAAe,EAAE,mBAAmB,EAAE,gBAAgB,CAChF,EAAA,OAAA,EAAA,CAAA,mBAAmB,EAAE,gBAAgB,EAAE,eAAe,CAAA,EAAA,CAAA;AAGrD,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,aAAa,EAFb,SAAA,EAAA,CAAC,OAAO,EAAE,8CAA8C,CAAC,EAF1D,OAAA,EAAA,CAAA,UAAU,EAAE,YAAY,EAAE,eAAe,EACF,eAAe,CAAA,EAAA,CAAA;;2FAGrD,aAAa,EAAA,UAAA,EAAA,CAAA;kBALzB,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;oBACR,OAAO,EAAE,CAAC,UAAU,EAAE,YAAY,EAAE,eAAe,EAAE,mBAAmB,EAAE,gBAAgB,CAAC;AAC3F,oBAAA,OAAO,EAAE,CAAC,mBAAmB,EAAE,gBAAgB,EAAE,eAAe,CAAC;AACjE,oBAAA,SAAS,EAAE,CAAC,OAAO,EAAE,8CAA8C,CAAC;AACrE,iBAAA;;;;;"}