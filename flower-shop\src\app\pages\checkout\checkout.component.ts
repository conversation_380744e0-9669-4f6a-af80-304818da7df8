import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatStepperModule } from '@angular/material/stepper';
import { MatIconModule } from '@angular/material/icon';
import { Observable } from 'rxjs';
import { Cart, CheckoutData } from '../../models';
import { CartService } from '../../services/cart.service';
import { OrderService } from '../../services/order.service';

@Component({
    selector: 'app-checkout',
    imports: [
        CommonModule,
        ReactiveFormsModule,
        MatCardModule,
        MatButtonModule,
        MatFormFieldModule,
        MatInputModule,
        MatSnackBarModule,
        MatStepperModule,
        MatIconModule
    ],
    templateUrl: './checkout.component.html',
    styleUrl: './checkout.component.scss'
})
export class CheckoutComponent implements OnInit {
    cart$: Observable<Cart>;
    checkoutForm: FormGroup;
    isLoading = false;

    constructor(
        private fb: FormBuilder,
        private cartService: CartService,
        private orderService: OrderService,
        private router: Router,
        private snackBar: MatSnackBar
    ) {
        this.cart$ = this.cartService.getCart();

        this.checkoutForm = this.fb.group({
            customerName: ['', [Validators.required, Validators.minLength(2)]],
            email: ['', [Validators.required, Validators.email]],
            address: ['', [Validators.required, Validators.minLength(5)]],
            city: ['', [Validators.required, Validators.minLength(2)]],
            postalCode: ['', [Validators.required, Validators.pattern(/^\d{5}(-\d{4})?$/)]],
            phone: ['', [Validators.pattern(/^\+?[\d\s\-\(\)]+$/)]]
        });
    }

    ngOnInit(): void {
        // Check if cart is empty and redirect if so
        this.cart$.subscribe(cart => {
            if (cart.items.length === 0) {
                this.snackBar.open('Your cart is empty', 'Close', { duration: 3000 });
                this.router.navigate(['/']);
            }
        });
    }

    onSubmit(): void {
        if (this.checkoutForm.valid) {
            this.isLoading = true;
            const checkoutData: CheckoutData = this.checkoutForm.value;

            this.cart$.subscribe(cart => {
                this.orderService.createOrder(cart, checkoutData).subscribe({
                    next: (order) => {
                        this.isLoading = false;
                        this.cartService.clearCart();
                        this.snackBar.open('Order placed successfully!', 'Close', { duration: 5000 });
                        this.router.navigate(['/']);
                    },
                    error: () => {
                        this.isLoading = false;
                        this.snackBar.open('Failed to place order', 'Close', { duration: 3000 });
                    }
                });
            });
        } else {
            this.markFormGroupTouched();
        }
    }

    private markFormGroupTouched(): void {
        Object.keys(this.checkoutForm.controls).forEach(key => {
            const control = this.checkoutForm.get(key);
            control?.markAsTouched();
        });
    }

    goBack(): void {
        this.router.navigate(['/cart']);
    }
}
