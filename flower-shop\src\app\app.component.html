<!-- Navigation - Exact from Example1.html -->
<nav class="petals-nav">
  <div class="nav-container">
    <div class="nav-content">
      <div class="nav-left">
        <button mat-icon-button class="mobile-menu-toggle" (click)="toggleMobileMenu()">
          <mat-icon>{{ mobileMenuOpen ? 'close' : 'menu' }}</mat-icon>
        </button>
        <button mat-button (click)="goToHome()" class="logo-button">
          <mat-icon class="logo-icon">local_florist</mat-icon>
          <span class="logo-text font-display">Petals & Blooms</span>
        </button>
      </div>

      <div class="nav-center" *ngIf="currentUser$ | async as user">
        <button mat-button (click)="goToHome()" class="nav-item" [class.active]="isHomeActive()">
          Home
        </button>
        <button mat-button (click)="goToCatalog()" class="nav-item">
          Catalog
        </button>
        <button mat-button (click)="goToAdmin()" *ngIf="user.role === 'admin'" class="nav-item">
          Dashboard
        </button>
      </div>

      <div class="nav-right">
        <button mat-icon-button (click)="goToCart()" class="cart-button" *ngIf="currentUser$ | async">
          <mat-icon>shopping_cart</mat-icon>
          <span class="cart-badge" *ngIf="(cartItemCount$ | async)! > 0">{{ cartItemCount$ | async }}</span>
        </button>
        
        <button mat-icon-button [matMenuTriggerFor]="userMenuRef" class="user-button" *ngIf="currentUser$ | async as user; else guestButtons">
          <mat-icon>person</mat-icon>
        </button>
        
        <mat-menu #userMenuRef="matMenu" class="user-dropdown">
          <div class="user-info" *ngIf="currentUser$ | async as user">
            <span class="user-name">{{ user.name }}</span>
            <span class="user-email">{{ user.email }}</span>
          </div>
          <mat-divider></mat-divider>
          <button mat-menu-item (click)="logout()">
            <mat-icon>logout</mat-icon>
            <span>Logout</span>
          </button>
        </mat-menu>

        <ng-template #guestButtons>
          <button mat-button (click)="goToLogin()" class="auth-button">
            Login
          </button>
          <button mat-raised-button (click)="goToRegister()" class="register-button btn-primary">
            Sign Up
          </button>
        </ng-template>
      </div>
    </div>
  </div>
</nav>

<!-- Main Content -->
<main class="main-content">
  <router-outlet></router-outlet>
</main>

<!-- Toast Notification -->
<div id="toast" class="toast" [class.show]="showToast">
  <span>{{ toastMessage }}</span>
</div>

<!-- Loading Overlay -->
<div class="loading-overlay" [class.show]="isLoading">
  <div class="loading-spinner"></div>
</div>
