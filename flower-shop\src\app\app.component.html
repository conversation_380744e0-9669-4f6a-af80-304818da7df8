<!-- Navigation Header -->
<mat-toolbar color="primary" class="app-toolbar">
    <button mat-button (click)="goToHome()" class="logo-button">
        <mat-icon>local_florist</mat-icon>
        <span>{{ title }}</span>
    </button>

    <span class="spacer"></span>

    <!-- Navigation buttons -->
    <div class="nav-buttons">
        <button mat-button (click)="goToHome()">
            <mat-icon>home</mat-icon>
            <span class="button-text">Home</span>
        </button>

        <!-- Cart button (only show for logged in users) -->
        <button mat-button (click)="goToCart()" *ngIf="currentUser$ | async">
            <mat-icon [matBadge]="cartItemCount$ | async" matBadgeColor="accent"
                [matBadgeHidden]="(cartItemCount$ | async) === 0">
                shopping_cart
            </mat-icon>
            <span class="button-text">Cart</span>
        </button>

        <!-- User menu -->
        <div *ngIf="currentUser$ | async as user; else loginButton">
            <button mat-button [matMenuTriggerFor]="userMenu">
                <mat-icon>account_circle</mat-icon>
                <span class="button-text">{{ user.name }}</span>
                <mat-icon>arrow_drop_down</mat-icon>
            </button>

            <mat-menu #userMenu="matMenu">
                <button mat-menu-item *ngIf="user.role === 'admin'" (click)="goToAdmin()">
                    <mat-icon>admin_panel_settings</mat-icon>
                    <span>Admin Dashboard</span>
                </button>
                <button mat-menu-item (click)="logout()">
                    <mat-icon>logout</mat-icon>
                    <span>Logout</span>
                </button>
            </mat-menu>
        </div>

        <ng-template #loginButton>
            <button mat-button (click)="goToLogin()">
                <mat-icon>login</mat-icon>
                <span class="button-text">Login</span>
            </button>
        </ng-template>
    </div>
</mat-toolbar>

<!-- Main Content -->
<main class="main-content">
    <router-outlet></router-outlet>
</main>
