import { waitForAsync, ComponentFixture, TestBed } from '@angular/core/testing';<% if(!standalone) { %>
import { MatPaginatorModule } from '@angular/material/paginator';
import { MatSortModule } from '@angular/material/sort';
import { MatTableModule } from '@angular/material/table';<% } %>

import { <%= classify(name) %>Component } from './<%= dasherize(name) %>.component';

describe('<%= classify(name) %>Component', () => {
  let component: <%= classify(name) %>Component;
  let fixture: ComponentFixture<<%= classify(name) %>Component>;<% if(!standalone) { %>

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [<%= classify(name) %>Component],
      imports: [
        MatPaginatorModule,
        MatSortModule,
        MatTableModule,
      ]
    });
  }));<% } %>

  beforeEach(() => {
    fixture = TestBed.createComponent(<%= classify(name) %>Component);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should compile', () => {
    expect(component).toBeTruthy();
  });
});
