import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatTableModule } from '@angular/material/table';
import { MatIconModule } from '@angular/material/icon';
import { MatDialogModule, MatDialog } from '@angular/material/dialog';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatTabsModule } from '@angular/material/tabs';
import { Observable } from 'rxjs';
import { Flower, Order, OrderStatus } from '../../models';
import { FlowerService } from '../../services/flower.service';
import { OrderService } from '../../services/order.service';

@Component({
    selector: 'app-admin',
    imports: [
        CommonModule,
        ReactiveFormsModule,
        MatCardModule,
        MatButtonModule,
        MatFormFieldModule,
        MatInputModule,
        MatSelectModule,
        MatTableModule,
        MatIconModule,
        MatDialogModule,
        MatSnackBarModule,
        MatTabsModule
    ],
    templateUrl: './admin.component.html',
    styleUrl: './admin.component.scss'
})
export class AdminComponent implements OnInit {
    flowers$: Observable<Flower[]>;
    orders$: Observable<Order[]>;
    flowerForm: FormGroup;
    editingFlower: Flower | null = null;

    flowerDisplayedColumns: string[] = ['id', 'name', 'category', 'price', 'stock', 'actions'];
    orderDisplayedColumns: string[] = ['id', 'customerName', 'totalPrice', 'status', 'createdAt', 'actions'];

    orderStatuses = Object.values(OrderStatus);

    constructor(
        private fb: FormBuilder,
        private flowerService: FlowerService,
        private orderService: OrderService,
        private dialog: MatDialog,
        private snackBar: MatSnackBar
    ) {
        this.flowers$ = this.flowerService.getFlowers();
        this.orders$ = this.orderService.getOrders();

        this.flowerForm = this.fb.group({
            name: ['', [Validators.required, Validators.minLength(2)]],
            description: ['', [Validators.required, Validators.minLength(10)]],
            price: ['', [Validators.required, Validators.min(0.01)]],
            category: ['', Validators.required],
            imageUrl: ['', Validators.required],
            stock: ['', [Validators.required, Validators.min(0)]]
        });
    }

    ngOnInit(): void {
        // Component initialization
    }

    onSubmitFlower(): void {
        if (this.flowerForm.valid) {
            const flowerData = this.flowerForm.value;

            if (this.editingFlower) {
                // Update existing flower
                const updatedFlower: Flower = {
                    ...this.editingFlower,
                    ...flowerData
                };

                this.flowerService.updateFlower(updatedFlower.id, flowerData).subscribe({
                    next: () => {
                        this.snackBar.open('Flower updated successfully', 'Close', { duration: 3000 });
                        this.resetForm();
                    },
                    error: () => {
                        this.snackBar.open('Failed to update flower', 'Close', { duration: 3000 });
                    }
                });
            } else {
                // Add new flower
                this.flowerService.addFlower(flowerData).subscribe({
                    next: () => {
                        this.snackBar.open('Flower added successfully', 'Close', { duration: 3000 });
                        this.resetForm();
                    },
                    error: () => {
                        this.snackBar.open('Failed to add flower', 'Close', { duration: 3000 });
                    }
                });
            }
        }
    }

    editFlower(flower: Flower): void {
        this.editingFlower = flower;
        this.flowerForm.patchValue(flower);
    }

    deleteFlower(flowerId: number): void {
        if (confirm('Are you sure you want to delete this flower?')) {
            this.flowerService.deleteFlower(flowerId).subscribe({
                next: () => {
                    this.snackBar.open('Flower deleted successfully', 'Close', { duration: 3000 });
                },
                error: () => {
                    this.snackBar.open('Failed to delete flower', 'Close', { duration: 3000 });
                }
            });
        }
    }

    updateOrderStatus(orderId: number, status: OrderStatus): void {
        this.orderService.updateOrderStatus(orderId, status).subscribe({
            next: () => {
                this.snackBar.open('Order status updated', 'Close', { duration: 3000 });
            },
            error: () => {
                this.snackBar.open('Failed to update order status', 'Close', { duration: 3000 });
            }
        });
    }

    resetForm(): void {
        this.flowerForm.reset();
        this.editingFlower = null;
    }
}
