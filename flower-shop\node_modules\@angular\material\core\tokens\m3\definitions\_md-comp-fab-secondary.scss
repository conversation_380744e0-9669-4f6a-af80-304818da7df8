//
// Design system display name: Material 3
// Design system version: v0.161
//

@use 'sass:map';

@use './md-sys-color';

@use './md-sys-elevation';

@use './md-sys-shape';

@use './md-sys-state';

$_default: (
  'md-sys-color': md-sys-color.values-light(),
  'md-sys-elevation': md-sys-elevation.values(),
  'md-sys-shape': md-sys-shape.values(),
  'md-sys-state': md-sys-state.values(),
);

@function values($deps: $_default, $exclude-hardcoded-values: false) {
  @return (
    'container-color': map.get($deps, 'md-sys-color', 'secondary-container'),
    'container-elevation': map.get($deps, 'md-sys-elevation', 'level3'),
    'container-height': if($exclude-hardcoded-values, null, 56px),
    'container-shadow-color': map.get($deps, 'md-sys-color', 'shadow'),
    'container-shape': map.get($deps, 'md-sys-shape', 'corner-large'),
    'container-width': if($exclude-hardcoded-values, null, 56px),
    'focus-container-elevation': map.get($deps, 'md-sys-elevation', 'level3'),
    'focus-icon-color': map.get($deps, 'md-sys-color', 'on-secondary-container'),
    'focus-state-layer-color':
      map.get($deps, 'md-sys-color', 'on-secondary-container'),
    'focus-state-layer-opacity':
      map.get($deps, 'md-sys-state', 'focus-state-layer-opacity'),
    'hover-container-elevation': map.get($deps, 'md-sys-elevation', 'level4'),
    'hover-icon-color': map.get($deps, 'md-sys-color', 'on-secondary-container'),
    'hover-state-layer-color':
      map.get($deps, 'md-sys-color', 'on-secondary-container'),
    'hover-state-layer-opacity':
      map.get($deps, 'md-sys-state', 'hover-state-layer-opacity'),
    'icon-color': map.get($deps, 'md-sys-color', 'on-secondary-container'),
    'icon-size': if($exclude-hardcoded-values, null, 24px),
    'lowered-container-elevation': map.get($deps, 'md-sys-elevation', 'level1'),
    'lowered-focus-container-elevation':
      map.get($deps, 'md-sys-elevation', 'level1'),
    'lowered-hover-container-elevation':
      map.get($deps, 'md-sys-elevation', 'level2'),
    'lowered-pressed-container-elevation':
      map.get($deps, 'md-sys-elevation', 'level1'),
    'pressed-container-elevation': map.get($deps, 'md-sys-elevation', 'level3'),
    'pressed-icon-color':
      map.get($deps, 'md-sys-color', 'on-secondary-container'),
    'pressed-state-layer-color':
      map.get($deps, 'md-sys-color', 'on-secondary-container'),
    'pressed-state-layer-opacity':
      map.get($deps, 'md-sys-state', 'pressed-state-layer-opacity')
  );
}
