<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Petals & Blooms - Modern Flower Shop</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.4.0/css/all.min.css">
    <link
        href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;600;700&family=Inter:wght@300;400;500;600&display=swap"
        rel="stylesheet">
    <style>
        :root {
            --primary-pink: #f8e8f0;
            --accent-pink: #e91e63;
            --soft-lavender: #f3e5f5;
            --sage-green: #81c784;
            --warm-cream: #fefefe;
            --text-dark: #2d3748;
            --text-light: #718096;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, var(--primary-pink) 0%, var(--soft-lavender) 100%);
            min-height: 100vh;
        }

        .font-display {
            font-family: 'Playfair Display', serif;
        }

        .glass-effect {
            background: rgba(255, 255, 255, 0.25);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.18);
        }

        .card-hover {
            transition: all 0.3s ease;
        }

        .card-hover:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--accent-pink) 0%, #ad1457 100%);
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(233, 30, 99, 0.3);
        }

        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }

            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .loading-spinner {
            border: 2px solid var(--primary-pink);
            border-top: 2px solid var(--accent-pink);
            border-radius: 50%;
            width: 20px;
            height: 20px;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }

        .toast {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            transform: translateX(100%);
            transition: transform 0.3s ease;
        }

        .toast.show {
            transform: translateX(0);
        }

        .sidebar {
            transition: transform 0.3s ease;
        }

        .sidebar.collapsed {
            transform: translateX(-100%);
        }

        @media (max-width: 768px) {
            .sidebar {
                position: fixed;
                left: 0;
                top: 0;
                height: 100vh;
                z-index: 50;
            }
        }

        .product-image {
            aspect-ratio: 1;
            object-fit: cover;
        }

        .cart-badge {
            position: absolute;
            top: -8px;
            right: -8px;
            background: var(--accent-pink);
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
        }
    </style>
</head>

<body>
    <!-- Toast Notification -->
    <div id="toast" class="toast bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg">
        <span id="toast-message">Success!</span>
    </div>

    <!-- Loading Overlay -->
    <div id="loading-overlay" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="loading-spinner"></div>
    </div>

    <!-- Navigation -->
    <nav class="bg-white shadow-lg sticky top-0 z-40">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center">
                    <button id="menu-toggle" class="md:hidden text-gray-600 hover:text-pink-600">
                        <i class="fas fa-bars text-xl"></i>
                    </button>
                    <h1 class="font-display text-2xl font-bold text-pink-600 ml-3 md:ml-0">
                        <i class="fas fa-seedling mr-2"></i>Petals & Blooms
                    </h1>
                </div>

                <div class="hidden md:flex items-center space-x-8">
                    <button id="nav-home" class="nav-item text-gray-700 hover:text-pink-600 font-medium">Home</button>
                    <button id="nav-catalog"
                        class="nav-item text-gray-700 hover:text-pink-600 font-medium">Catalog</button>
                    <div id="admin-nav" class="hidden">
                        <button id="nav-dashboard"
                            class="nav-item text-gray-700 hover:text-pink-600 font-medium">Dashboard</button>
                    </div>
                </div>

                <div class="flex items-center space-x-4">
                    <button id="cart-toggle" class="relative text-gray-600 hover:text-pink-600">
                        <i class="fas fa-shopping-cart text-xl"></i>
                        <span id="cart-count" class="cart-badge hidden">0</span>
                    </button>
                    <button id="user-menu" class="text-gray-600 hover:text-pink-600">
                        <i class="fas fa-user text-xl"></i>
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="flex">
        <!-- Admin Sidebar -->
        <aside id="admin-sidebar" class="sidebar w-64 bg-white shadow-lg h-screen hidden">
            <div class="p-6">
                <h2 class="font-display text-xl font-bold text-gray-800 mb-6">Admin Panel</h2>
                <nav class="space-y-2">
                    <button id="sidebar-dashboard"
                        class="w-full text-left px-4 py-2 text-gray-700 hover:bg-pink-50 hover:text-pink-600 rounded-lg">
                        <i class="fas fa-tachometer-alt mr-3"></i>Dashboard
                    </button>
                    <button id="sidebar-inventory"
                        class="w-full text-left px-4 py-2 text-gray-700 hover:bg-pink-50 hover:text-pink-600 rounded-lg">
                        <i class="fas fa-boxes mr-3"></i>Inventory
                    </button>
                    <button id="sidebar-orders"
                        class="w-full text-left px-4 py-2 text-gray-700 hover:bg-pink-50 hover:text-pink-600 rounded-lg">
                        <i class="fas fa-receipt mr-3"></i>Orders
                    </button>
                    <button id="logout-btn" class="w-full text-left px-4 py-2 text-red-600 hover:bg-red-50 rounded-lg">
                        <i class="fas fa-sign-out-alt mr-3"></i>Logout
                    </button>
                </nav>
            </div>
        </aside>

        <!-- Main Content Area -->
        <main class="flex-1 min-h-screen">
            <!-- Login Page -->
            <div id="login-page" class="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
                <div class="max-w-md w-full space-y-8 bg-white p-8 rounded-xl shadow-lg">
                    <div class="text-center">
                        <h2 class="font-display text-3xl font-bold text-gray-900">Welcome Back</h2>
                        <p class="mt-2 text-gray-600">Sign in to your account</p>
                    </div>
                    <form id="login-form" class="mt-8 space-y-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Email</label>
                            <input type="email" id="login-email"
                                class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-pink-500 focus:border-pink-500"
                                required>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Password</label>
                            <input type="password" id="login-password"
                                class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-pink-500 focus:border-pink-500"
                                required>
                        </div>
                        <div>
                            <button type="submit"
                                class="btn-primary w-full py-2 px-4 text-white font-medium rounded-md">
                                Sign In
                            </button>
                        </div>
                        <div class="text-center">
                            <button type="button" id="show-signup" class="text-pink-600 hover:text-pink-500">
                                Don't have an account? Sign up
                            </button>
                        </div>
                        <div class="text-center text-sm text-gray-500">
                            <p>Demo Login:</p>
                            <p>Client: <EMAIL> / password</p>
                            <p>Admin: <EMAIL> / password</p>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Signup Page -->
            <div id="signup-page"
                class="hidden min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
                <div class="max-w-md w-full space-y-8 bg-white p-8 rounded-xl shadow-lg">
                    <div class="text-center">
                        <h2 class="font-display text-3xl font-bold text-gray-900">Create Account</h2>
                        <p class="mt-2 text-gray-600">Join our flower community</p>
                    </div>
                    <form id="signup-form" class="mt-8 space-y-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Full Name</label>
                            <input type="text" id="signup-name"
                                class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-pink-500 focus:border-pink-500"
                                required>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Email</label>
                            <input type="email" id="signup-email"
                                class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-pink-500 focus:border-pink-500"
                                required>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Password</label>
                            <input type="password" id="signup-password"
                                class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-pink-500 focus:border-pink-500"
                                required>
                        </div>
                        <div>
                            <button type="submit"
                                class="btn-primary w-full py-2 px-4 text-white font-medium rounded-md">
                                Create Account
                            </button>
                        </div>
                        <div class="text-center">
                            <button type="button" id="show-login" class="text-pink-600 hover:text-pink-500">
                                Already have an account? Sign in
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Home Page -->
            <div id="home-page" class="hidden">
                <!-- Hero Section -->
                <section
                    class="relative h-96 bg-gradient-to-r from-pink-200 to-purple-200 flex items-center justify-center">
                    <div class="text-center text-white">
                        <h1 class="font-display text-5xl font-bold mb-4">Beautiful Flowers for Every Occasion</h1>
                        <p class="text-xl mb-8">Discover our curated collection of fresh blooms</p>
                        <button id="shop-now" class="btn-primary px-8 py-3 text-white font-medium rounded-lg">
                            Shop Now <i class="fas fa-arrow-right ml-2"></i>
                        </button>
                    </div>
                </section>

                <!-- Featured Products -->
                <section class="py-16 px-4 max-w-7xl mx-auto">
                    <h2 class="font-display text-3xl font-bold text-center mb-12 text-gray-800">Featured Flowers</h2>
                    <div id="featured-products" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                        <!-- Products will be loaded here -->
                    </div>
                </section>
            </div>

            <!-- Catalog Page -->
            <div id="catalog-page" class="hidden p-6">
                <div class="max-w-7xl mx-auto">
                    <!-- Search and Filters -->
                    <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
                        <div class="flex flex-col md:flex-row gap-4 items-center">
                            <div class="flex-1">
                                <input type="text" id="search-input" placeholder="Search flowers..."
                                    class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-pink-500 focus:border-pink-500">
                            </div>
                            <div class="flex gap-4">
                                <select id="category-filter"
                                    class="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-pink-500 focus:border-pink-500">
                                    <option value="">All Categories</option>
                                    <option value="roses">Roses</option>
                                    <option value="tulips">Tulips</option>
                                    <option value="lilies">Lilies</option>
                                    <option value="orchids">Orchids</option>
                                </select>
                                <input type="range" id="price-filter" min="0" max="200" value="200"
                                    class="px-4 py-2 border border-gray-300 rounded-lg">
                                <span id="price-display" class="text-gray-600 font-medium">$0-$200</span>
                            </div>
                        </div>
                    </div>

                    <!-- Products Grid -->
                    <div id="products-grid" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                        <!-- Products will be loaded here -->
                    </div>
                </div>
            </div>

            <!-- Product Detail Page -->
            <div id="product-detail-page" class="hidden p-6">
                <div class="max-w-4xl mx-auto bg-white rounded-lg shadow-lg overflow-hidden">
                    <div class="md:flex">
                        <div class="md:w-1/2">
                            <img id="product-detail-image" src="" alt="" class="w-full h-96 object-cover">
                        </div>
                        <div class="md:w-1/2 p-8">
                            <h1 id="product-detail-name" class="font-display text-3xl font-bold text-gray-900 mb-4">
                            </h1>
                            <p id="product-detail-category" class="text-pink-600 font-medium mb-4"></p>
                            <p id="product-detail-description" class="text-gray-600 mb-6"></p>
                            <p id="product-detail-price" class="text-3xl font-bold text-pink-600 mb-6"></p>
                            <div class="flex items-center gap-4 mb-6">
                                <label class="font-medium text-gray-700">Quantity:</label>
                                <input type="number" id="product-quantity" value="1" min="1"
                                    class="w-20 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-pink-500 focus:border-pink-500">
                            </div>
                            <div class="flex gap-4">
                                <button id="add-to-cart-btn"
                                    class="btn-primary flex-1 py-3 text-white font-medium rounded-lg">
                                    <i class="fas fa-shopping-cart mr-2"></i>Add to Cart
                                </button>
                                <button id="back-to-catalog"
                                    class="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50">
                                    Back
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Cart Page -->
            <div id="cart-page" class="hidden p-6">
                <div class="max-w-4xl mx-auto">
                    <h1 class="font-display text-3xl font-bold text-gray-900 mb-8">Shopping Cart</h1>
                    <div class="bg-white rounded-lg shadow-lg p-6">
                        <div id="cart-items" class="space-y-4">
                            <!-- Cart items will be loaded here -->
                        </div>
                        <div id="cart-empty" class="text-center py-12 hidden">
                            <i class="fas fa-shopping-cart text-4xl text-gray-400 mb-4"></i>
                            <p class="text-xl text-gray-600 mb-4">Your cart is empty</p>
                            <button id="continue-shopping"
                                class="btn-primary px-6 py-3 text-white font-medium rounded-lg">
                                Continue Shopping
                            </button>
                        </div>
                        <div id="cart-summary" class="border-t pt-6 mt-6">
                            <div class="flex justify-between items-center text-xl font-bold text-gray-900 mb-4">
                                <span>Total: </span>
                                <span id="cart-total">$0.00</span>
                            </div>
                            <button id="checkout-btn" class="btn-primary w-full py-3 text-white font-medium rounded-lg">
                                Proceed to Checkout
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Checkout Page -->
            <div id="checkout-page" class="hidden p-6">
                <div class="max-w-2xl mx-auto">
                    <h1 class="font-display text-3xl font-bold text-gray-900 mb-8">Checkout</h1>
                    <div class="bg-white rounded-lg shadow-lg p-6">
                        <form id="checkout-form" class="space-y-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Full Name</label>
                                <input type="text" id="checkout-name" required
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-pink-500 focus:border-pink-500">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Email</label>
                                <input type="email" id="checkout-email" required
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-pink-500 focus:border-pink-500">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Phone</label>
                                <input type="tel" id="checkout-phone" required
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-pink-500 focus:border-pink-500">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Address</label>
                                <textarea id="checkout-address" rows="3" required
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-pink-500 focus:border-pink-500"></textarea>
                            </div>
                            <div class="border-t pt-6">
                                <div class="flex justify-between items-center text-xl font-bold text-gray-900 mb-6">
                                    <span>Total: </span>
                                    <span id="checkout-total">$0.00</span>
                                </div>
                                <button type="submit" class="btn-primary w-full py-3 text-white font-medium rounded-lg">
                                    Place Order
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Admin Dashboard -->
            <div id="admin-dashboard" class="hidden p-6">
                <div class="max-w-7xl mx-auto">
                    <h1 class="font-display text-3xl font-bold text-gray-900 mb-8">Admin Dashboard</h1>

                    <!-- Stats Cards -->
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                        <div class="bg-white rounded-lg shadow-lg p-6">
                            <div class="flex items-center">
                                <div class="p-3 rounded-full bg-pink-100 text-pink-600">
                                    <i class="fas fa-seedling text-xl"></i>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-gray-600">Total Products</p>
                                    <p id="total-products" class="text-2xl font-bold text-gray-900">0</p>
                                </div>
                            </div>
                        </div>
                        <div class="bg-white rounded-lg shadow-lg p-6">
                            <div class="flex items-center">
                                <div class="p-3 rounded-full bg-green-100 text-green-600">
                                    <i class="fas fa-shopping-cart text-xl"></i>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-gray-600">Total Orders</p>
                                    <p id="total-orders" class="text-2xl font-bold text-gray-900">0</p>
                                </div>
                            </div>
                        </div>
                        <div class="bg-white rounded-lg shadow-lg p-6">
                            <div class="flex items-center">
                                <div class="p-3 rounded-full bg-blue-100 text-blue-600">
                                    <i class="fas fa-dollar-sign text-xl"></i>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-gray-600">Revenue</p>
                                    <p id="total-revenue" class="text-2xl font-bold text-gray-900">$0</p>
                                </div>
                            </div>
                        </div>
                        <div class="bg-white rounded-lg shadow-lg p-6">
                            <div class="flex items-center">
                                <div class="p-3 rounded-full bg-purple-100 text-purple-600">
                                    <i class="fas fa-users text-xl"></i>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-gray-600">Customers</p>
                                    <p id="total-customers" class="text-2xl font-bold text-gray-900">0</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="bg-white rounded-lg shadow-lg p-6">
                        <h2 class="text-xl font-bold text-gray-900 mb-4">Quick Actions</h2>
                        <div class="flex flex-wrap gap-4">
                            <button id="quick-add-product"
                                class="btn-primary px-6 py-3 text-white font-medium rounded-lg">
                                <i class="fas fa-plus mr-2"></i>Add Product
                            </button>
                            <button id="view-inventory"
                                class="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50">
                                <i class="fas fa-boxes mr-2"></i>View Inventory
                            </button>
                            <button id="view-orders"
                                class="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50">
                                <i class="fas fa-receipt mr-2"></i>View Orders
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Admin Inventory -->
            <div id="admin-inventory" class="hidden p-6">
                <div class="max-w-7xl mx-auto">
                    <div class="flex justify-between items-center mb-8">
                        <h1 class="font-display text-3xl font-bold text-gray-900">Inventory Management</h1>
                        <button id="add-product-btn" class="btn-primary px-6 py-3 text-white font-medium rounded-lg">
                            <i class="fas fa-plus mr-2"></i>Add Product
                        </button>
                    </div>

                    <div class="bg-white rounded-lg shadow-lg overflow-hidden">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th
                                        class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Product</th>
                                    <th
                                        class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Category</th>
                                    <th
                                        class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Price</th>
                                    <th
                                        class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Stock</th>
                                    <th
                                        class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Actions</th>
                                </tr>
                            </thead>
                            <tbody id="inventory-table" class="bg-white divide-y divide-gray-200">
                                <!-- Inventory items will be loaded here -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Admin Orders -->
            <div id="admin-orders" class="hidden p-6">
                <div class="max-w-7xl mx-auto">
                    <h1 class="font-display text-3xl font-bold text-gray-900 mb-8">Order Management</h1>

                    <div class="bg-white rounded-lg shadow-lg overflow-hidden">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th
                                        class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Order ID</th>
                                    <th
                                        class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Customer</th>
                                    <th
                                        class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Date</th>
                                    <th
                                        class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Total</th>
                                    <th
                                        class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Status</th>
                                    <th
                                        class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Actions</th>
                                </tr>
                            </thead>
                            <tbody id="orders-table" class="bg-white divide-y divide-gray-200">
                                <!-- Order items will be loaded here -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Add/Edit Product Modal -->
            <div id="product-modal"
                class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
                <div class="bg-white rounded-lg shadow-lg max-w-md w-full mx-4">
                    <div class="p-6">
                        <div class="flex justify-between items-center mb-4">
                            <h2 id="product-modal-title" class="text-xl font-bold text-gray-900">Add Product</h2>
                            <button id="close-product-modal" class="text-gray-500 hover:text-gray-700">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                        <form id="product-form" class="space-y-4">
                            <input type="hidden" id="product-id">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Name</label>
                                <input type="text" id="product-name" required
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-pink-500 focus:border-pink-500">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Description</label>
                                <textarea id="product-description" rows="3" required
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-pink-500 focus:border-pink-500"></textarea>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Category</label>
                                <select id="product-category" required
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-pink-500 focus:border-pink-500">
                                    <option value="">Select Category</option>
                                    <option value="roses">Roses</option>
                                    <option value="tulips">Tulips</option>
                                    <option value="lilies">Lilies</option>
                                    <option value="orchids">Orchids</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Price</label>
                                <input type="number" id="product-price" step="0.01" required
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-pink-500 focus:border-pink-500">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Image URL</label>
                                <input type="url" id="product-image" required
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-pink-500 focus:border-pink-500">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Stock</label>
                                <input type="number" id="product-stock" required
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-pink-500 focus:border-pink-500">
                            </div>
                            <div class="flex justify-end space-x-3 pt-4">
                                <button type="button" id="cancel-product"
                                    class="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50">
                                    Cancel
                                </button>
                                <button type="submit" class="btn-primary px-4 py-2 text-white font-medium rounded-lg">
                                    Save Product
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Confirmation Modal -->
            <div id="confirm-modal"
                class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
                <div class="bg-white rounded-lg shadow-lg max-w-sm w-full mx-4">
                    <div class="p-6">
                        <div class="text-center">
                            <i class="fas fa-exclamation-triangle text-4xl text-red-500 mb-4"></i>
                            <h2 class="text-xl font-bold text-gray-900 mb-2">Confirm Delete</h2>
                            <p class="text-gray-600 mb-6">Are you sure you want to delete this item? This action cannot
                                be undone.</p>
                            <div class="flex justify-center space-x-3">
                                <button id="cancel-delete"
                                    class="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50">
                                    Cancel
                                </button>
                                <button id="confirm-delete"
                                    class="px-4 py-2 bg-red-600 text-white font-medium rounded-lg hover:bg-red-700">
                                    Delete
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script>
        // Global state management
        let currentUser = null;
        let currentPage = 'login';
        let cart = [];
        let products = [
            {
                id: 1,
                name: "Red Rose Bouquet",
                description: "Beautiful dozen red roses perfect for romantic occasions",
                category: "roses",
                price: 49.99,
                image: "https://images.unsplash.com/photo-1518895949257-7621c3c786d7?w=400",
                stock: 25
            },
            {
                id: 2,
                name: "Spring Tulip Mix",
                description: "Colorful tulips bringing spring joy to your home",
                category: "tulips",
                price: 34.99,
                image: "https://images.unsplash.com/photo-1490750967868-88aa4486c946?w=400",
                stock: 30
            },
            {
                id: 3,
                name: "White Lilies",
                description: "Elegant white lilies for special moments",
                category: "lilies",
                price: 39.99,
                image: "https://images.unsplash.com/photo-1584464491033-06628f3a6b7b?w=400",
                stock: 20
            },
            {
                id: 4,
                name: "Purple Orchid",
                description: "Exotic purple orchid for sophisticated spaces",
                category: "orchids",
                price: 59.99,
                image: "https://images.unsplash.com/photo-1452827073306-6e6e661baf57?w=400",
                stock: 15
            },
            {
                id: 5,
                name: "Pink Rose Arrangement",
                description: "Soft pink roses in elegant arrangement",
                category: "roses",
                price: 44.99,
                image: "https://images.unsplash.com/photo-1563241527-3004b7be0ffd?w=400",
                stock: 22
            },
            {
                id: 6,
                name: "Mixed Flower Bouquet",
                description: "Vibrant mix of seasonal flowers",
                category: "mixed",
                price: 54.99,
                image: "https://images.unsplash.com/photo-1487070183336-b863922373d4?w=400",
                stock: 18
            }
        ];

        let orders = [
            {
                id: 1001,
                customer: "John Smith",
                email: "<EMAIL>",
                date: "2024-01-15",
                total: 94.98,
                status: "delivered",
                items: [
                    { productId: 1, quantity: 2, price: 49.99 }
                ]
            },
            {
                id: 1002,
                customer: "Sarah Johnson",
                email: "<EMAIL>",
                date: "2024-01-16",
                total: 39.99,
                status: "processing",
                items: [
                    { productId: 3, quantity: 1, price: 39.99 }
                ]
            }
        ];

        // Utility functions
        function showPage(pageId) {
            // Hide all pages
            const pages = ['login-page', 'signup-page', 'home-page', 'catalog-page', 'product-detail-page', 'cart-page', 'checkout-page', 'admin-dashboard', 'admin-inventory', 'admin-orders'];
            pages.forEach(page => {
                document.getElementById(page).classList.add('hidden');
            });

            // Show selected page
            document.getElementById(pageId).classList.remove('hidden');
            currentPage = pageId;

            // Update navigation
            updateNavigation();
        }

        function updateNavigation() {
            const adminNav = document.getElementById('admin-nav');
            const adminSidebar = document.getElementById('admin-sidebar');

            if (currentUser && currentUser.role === 'admin') {
                adminNav.classList.remove('hidden');
                adminSidebar.classList.remove('hidden');
            } else {
                adminNav.classList.add('hidden');
                adminSidebar.classList.add('hidden');
            }
        }

        function showToast(message, type = 'success') {
            const toast = document.getElementById('toast');
            const toastMessage = document.getElementById('toast-message');

            toastMessage.textContent = message;
            toast.className = `toast ${type === 'success' ? 'bg-green-500' : 'bg-red-500'} text-white px-6 py-3 rounded-lg shadow-lg show`;

            setTimeout(() => {
                toast.classList.remove('show');
            }, 3000);
        }

        function updateCartCount() {
            const cartCount = document.getElementById('cart-count');
            const totalItems = cart.reduce((sum, item) => sum + item.quantity, 0);

            if (totalItems > 0) {
                cartCount.textContent = totalItems;
                cartCount.classList.remove('hidden');
            } else {
                cartCount.classList.add('hidden');
            }
        }

        function formatPrice(price) {
            return `$${price.toFixed(2)}`;
        }

        // Product functions
        function renderProducts(productsToRender = products, containerId = 'products-grid') {
            const container = document.getElementById(containerId);
            container.innerHTML = '';

            productsToRender.forEach(product => {
                const productCard = document.createElement('div');
                productCard.className = 'bg-white rounded-lg shadow-lg overflow-hidden card-hover';
                productCard.innerHTML = `
                    <img src="${product.image}" alt="${product.name}" class="w-full product-image">
                    <div class="p-4">
                        <h3 class="font-display text-lg font-semibold text-gray-900 mb-2">${product.name}</h3>
                        <p class="text-gray-600 text-sm mb-3">${product.description}</p>
                        <div class="flex justify-between items-center">
                            <span class="text-2xl font-bold text-pink-600">${formatPrice(product.price)}</span>
                            <button onclick="viewProduct(${product.id})" class="btn-primary px-4 py-2 text-white text-sm font-medium rounded-lg">
                                View Details
                            </button>
                        </div>
                    </div>
                `;
                container.appendChild(productCard);
            });
        }

        function viewProduct(productId) {
            const product = products.find(p => p.id === productId);
            if (!product) return;

            document.getElementById('product-detail-image').src = product.image;
            document.getElementById('product-detail-name').textContent = product.name;
            document.getElementById('product-detail-category').textContent = product.category.charAt(0).toUpperCase() + product.category.slice(1);
            document.getElementById('product-detail-description').textContent = product.description;
            document.getElementById('product-detail-price').textContent = formatPrice(product.price);
            document.getElementById('product-quantity').value = 1;

            // Store current product ID for add to cart
            document.getElementById('add-to-cart-btn').setAttribute('data-product-id', productId);

            showPage('product-detail-page');
        }

        function addToCart(productId, quantity = 1) {
            const product = products.find(p => p.id === productId);
            if (!product) return;

            const existingItem = cart.find(item => item.productId === productId);

            if (existingItem) {
                existingItem.quantity += quantity;
            } else {
                cart.push({
                    productId: productId,
                    quantity: quantity,
                    price: product.price
                });
            }

            updateCartCount();
            showToast(`${product.name} added to cart!`);
        }

        function removeFromCart(productId) {
            cart = cart.filter(item => item.productId !== productId);
            updateCartCount();
            renderCart();
            showToast('Item removed from cart');
        }

        function updateCartQuantity(productId, quantity) {
            const item = cart.find(item => item.productId === productId);
            if (item) {
                item.quantity = quantity;
                updateCartCount();
                renderCart();
            }
        }

        function renderCart() {
            const cartItems = document.getElementById('cart-items');
            const cartEmpty = document.getElementById('cart-empty');
            const cartSummary = document.getElementById('cart-summary');

            if (cart.length === 0) {
                cartItems.innerHTML = '';
                cartEmpty.classList.remove('hidden');
                cartSummary.classList.add('hidden');
                return;
            }

            cartEmpty.classList.add('hidden');
            cartSummary.classList.remove('hidden');

            cartItems.innerHTML = '';
            let total = 0;

            cart.forEach(item => {
                const product = products.find(p => p.id === item.productId);
                if (!product) return;

                const itemTotal = item.price * item.quantity;
                total += itemTotal;

                const cartItem = document.createElement('div');
                cartItem.className = 'flex items-center space-x-4 p-4 border border-gray-200 rounded-lg';
                cartItem.innerHTML = `
                    <img src="${product.image}" alt="${product.name}" class="w-16 h-16 object-cover rounded-lg">
                    <div class="flex-1">
                        <h3 class="font-semibold text-gray-900">${product.name}</h3>
                        <p class="text-gray-600">${formatPrice(item.price)}</p>
                    </div>
                    <div class="flex items-center space-x-2">
                        <button onclick="updateCartQuantity(${item.productId}, ${item.quantity - 1})" 
                                class="w-8 h-8 flex items-center justify-center border border-gray-300 rounded-full hover:bg-gray-50"
                                ${item.quantity <= 1 ? 'disabled' : ''}>
                            <i class="fas fa-minus text-xs"></i>
                        </button>
                        <span class="w-8 text-center">${item.quantity}</span>
                        <button onclick="updateCartQuantity(${item.productId}, ${item.quantity + 1})" 
                                class="w-8 h-8 flex items-center justify-center border border-gray-300 rounded-full hover:bg-gray-50">
                            <i class="fas fa-plus text-xs"></i>
                        </button>
                    </div>
                    <div class="text-right">
                        <p class="font-semibold text-gray-900">${formatPrice(itemTotal)}</p>
                        <button onclick="removeFromCart(${item.productId})" class="text-red-600 hover:text-red-700 text-sm mt-1">
                            <i class="fas fa-trash mr-1"></i>Remove
                        </button>
                    </div>
                `;
                cartItems.appendChild(cartItem);
            });

            document.getElementById('cart-total').textContent = formatPrice(total);
            document.getElementById('checkout-total').textContent = formatPrice(total);
        }

        // Admin functions
        function renderInventory() {
            const tbody = document.getElementById('inventory-table');
            tbody.innerHTML = '';

            products.forEach(product => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="flex items-center">
                            <img src="${product.image}" alt="${product.name}" class="w-12 h-12 object-cover rounded-lg mr-4">
                            <div>
                                <div class="text-sm font-medium text-gray-900">${product.name}</div>
                                <div class="text-sm text-gray-500">${product.description}</div>
                            </div>
                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${product.category}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${formatPrice(product.price)}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${product.stock}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <button onclick="editProduct(${product.id})" class="text-indigo-600 hover:text-indigo-900 mr-4">
                            <i class="fas fa-edit"></i> Edit
                        </button>
                        <button onclick="confirmDeleteProduct(${product.id})" class="text-red-600 hover:text-red-900">
                            <i class="fas fa-trash"></i> Delete
                        </button>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        function renderOrders() {
            const tbody = document.getElementById('orders-table');
            tbody.innerHTML = '';

            orders.forEach(order => {
                const row = document.createElement('tr');
                const statusClass = order.status === 'delivered' ? 'bg-green-100 text-green-800' :
                    order.status === 'processing' ? 'bg-yellow-100 text-yellow-800' :
                        'bg-gray-100 text-gray-800';

                row.innerHTML = `
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">#${order.id}</td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm font-medium text-gray-900">${order.customer}</div>
                        <div class="text-sm text-gray-500">${order.email}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${order.date}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${formatPrice(order.total)}</td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${statusClass}">
                            ${order.status}
                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <button onclick="viewOrderDetails(${order.id})" class="text-indigo-600 hover:text-indigo-900">
                            <i class="fas fa-eye"></i> View
                        </button>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        function updateDashboardStats() {
            document.getElementById('total-products').textContent = products.length;
            document.getElementById('total-orders').textContent = orders.length;

            const totalRevenue = orders.reduce((sum, order) => sum + order.total, 0);
            document.getElementById('total-revenue').textContent = formatPrice(totalRevenue);

            const uniqueCustomers = new Set(orders.map(order => order.email)).size;
            document.getElementById('total-customers').textContent = uniqueCustomers;
        }

        function openProductModal(productId = null) {
            const modal = document.getElementById('product-modal');
            const title = document.getElementById('product-modal-title');
            const form = document.getElementById('product-form');

            if (productId) {
                const product = products.find(p => p.id === productId);
                title.textContent = 'Edit Product';
                document.getElementById('product-id').value = product.id;
                document.getElementById('product-name').value = product.name;
                document.getElementById('product-description').value = product.description;
                document.getElementById('product-category').value = product.category;
                document.getElementById('product-price').value = product.price;
                document.getElementById('product-image').value = product.image;
                document.getElementById('product-stock').value = product.stock;
            } else {
                title.textContent = 'Add Product';
                form.reset();
                document.getElementById('product-id').value = '';
            }

            modal.classList.remove('hidden');
        }

        function closeProductModal() {
            document.getElementById('product-modal').classList.add('hidden');
        }

        function saveProduct(formData) {
            const productId = formData.get('product-id');
            const productData = {
                name: formData.get('product-name'),
                description: formData.get('product-description'),
                category: formData.get('product-category'),
                price: parseFloat(formData.get('product-price')),
                image: formData.get('product-image'),
                stock: parseInt(formData.get('product-stock'))
            };

            if (productId) {
                // Edit existing product
                const product = products.find(p => p.id === parseInt(productId));
                Object.assign(product, productData);
                showToast('Product updated successfully!');
            } else {
                // Add new product
                const newId = Math.max(...products.map(p => p.id)) + 1;
                products.push({ id: newId, ...productData });
                showToast('Product added successfully!');
            }

            renderInventory();
            updateDashboardStats();
            closeProductModal();
        }

        function editProduct(productId) {
            openProductModal(productId);
        }

        function confirmDeleteProduct(productId) {
            const modal = document.getElementById('confirm-modal');
            modal.classList.remove('hidden');

            document.getElementById('confirm-delete').onclick = () => {
                deleteProduct(productId);
                modal.classList.add('hidden');
            };
        }

        function deleteProduct(productId) {
            products = products.filter(p => p.id !== productId);
            renderInventory();
            updateDashboardStats();
            showToast('Product deleted successfully!');
        }

        // Search and filter functions
        function filterProducts() {
            const searchTerm = document.getElementById('search-input').value.toLowerCase();
            const selectedCategory = document.getElementById('category-filter').value;
            const maxPrice = parseInt(document.getElementById('price-filter').value);

            let filtered = products.filter(product => {
                const matchesSearch = product.name.toLowerCase().includes(searchTerm) ||
                    product.description.toLowerCase().includes(searchTerm);
                const matchesCategory = !selectedCategory || product.category === selectedCategory;
                const matchesPrice = product.price <= maxPrice;
                return matchesSearch && matchesCategory && matchesPrice;
            });

            renderProducts(filtered);
        }

        // Event listeners
        document.addEventListener('DOMContentLoaded', function () {
            // Login form
            document.getElementById('login-form').addEventListener('submit', function (e) {
                e.preventDefault();
                const email = document.getElementById('login-email').value;
                const password = document.getElementById('login-password').value;

                // Simple authentication
                if ((email === '<EMAIL>' || email === '<EMAIL>') && password === 'password') {
                    currentUser = {
                        email: email,
                        role: email === '<EMAIL>' ? 'admin' : 'client'
                    };
                    showToast('Login successful!');
                    showPage('home-page');
                } else {
                    showToast('Invalid credentials', 'error');
                }
            });

            // Signup form
            document.getElementById('signup-form').addEventListener('submit', function (e) {
                e.preventDefault();
                const name = document.getElementById('signup-name').value;
                const email = document.getElementById('signup-email').value;

                currentUser = { email: email, name: name, role: 'client' };
                showToast('Account created successfully!');
                showPage('home-page');
            });

            // Navigation
            document.getElementById('show-signup').addEventListener('click', () => showPage('signup-page'));
            document.getElementById('show-login').addEventListener('click', () => showPage('login-page'));
            document.getElementById('nav-home').addEventListener('click', () => showPage('home-page'));
            document.getElementById('nav-catalog').addEventListener('click', () => {
                showPage('catalog-page');
                renderProducts();
            });
            document.getElementById('nav-dashboard').addEventListener('click', () => {
                showPage('admin-dashboard');
                updateDashboardStats();
            });
            document.getElementById('shop-now').addEventListener('click', () => {
                showPage('catalog-page');
                renderProducts();
            });

            // Cart
            document.getElementById('cart-toggle').addEventListener('click', () => {
                showPage('cart-page');
                renderCart();
            });
            document.getElementById('continue-shopping').addEventListener('click', () => {
                showPage('catalog-page');
                renderProducts();
            });
            document.getElementById('checkout-btn').addEventListener('click', () => showPage('checkout-page'));

            // Product detail
            document.getElementById('add-to-cart-btn').addEventListener('click', function () {
                const productId = parseInt(this.getAttribute('data-product-id'));
                const quantity = parseInt(document.getElementById('product-quantity').value);
                addToCart(productId, quantity);
            });
            document.getElementById('back-to-catalog').addEventListener('click', () => {
                showPage('catalog-page');
                renderProducts();
            });

            // Checkout
            document.getElementById('checkout-form').addEventListener('submit', function (e) {
                e.preventDefault();
                const formData = new FormData(this);

                // Create order
                const newOrder = {
                    id: Math.max(...orders.map(o => o.id)) + 1,
                    customer: formData.get('checkout-name'),
                    email: formData.get('checkout-email'),
                    date: new Date().toISOString().split('T')[0],
                    total: cart.reduce((sum, item) => sum + (item.price * item.quantity), 0),
                    status: 'processing',
                    items: [...cart]
                };

                orders.push(newOrder);
                cart = [];
                updateCartCount();

                showToast('Order placed successfully!');
                showPage('home-page');
            });

            // Admin sidebar
            document.getElementById('sidebar-dashboard').addEventListener('click', () => {
                showPage('admin-dashboard');
                updateDashboardStats();
            });
            document.getElementById('sidebar-inventory').addEventListener('click', () => {
                showPage('admin-inventory');
                renderInventory();
            });
            document.getElementById('sidebar-orders').addEventListener('click', () => {
                showPage('admin-orders');
                renderOrders();
            });

            // Admin quick actions
            document.getElementById('quick-add-product').addEventListener('click', () => openProductModal());
            document.getElementById('view-inventory').addEventListener('click', () => {
                showPage('admin-inventory');
                renderInventory();
            });
            document.getElementById('view-orders').addEventListener('click', () => {
                showPage('admin-orders');
                renderOrders();
            });
            document.getElementById('add-product-btn').addEventListener('click', () => openProductModal());

            // Product modal
            document.getElementById('close-product-modal').addEventListener('click', closeProductModal);
            document.getElementById('cancel-product').addEventListener('click', closeProductModal);
            document.getElementById('product-form').addEventListener('submit', function (e) {
                e.preventDefault();
                const formData = new FormData(this);
                saveProduct(formData);
            });

            // Confirm modal
            document.getElementById('cancel-delete').addEventListener('click', () => {
                document.getElementById('confirm-modal').classList.add('hidden');
            });

            // Search and filters
            document.getElementById('search-input').addEventListener('input', filterProducts);
            document.getElementById('category-filter').addEventListener('change', filterProducts);
            document.getElementById('price-filter').addEventListener('input', function () {
                document.getElementById('price-display').textContent = `$0-$${this.value}`;
                filterProducts();
            });

            // Logout
            document.getElementById('logout-btn').addEventListener('click', () => {
                currentUser = null;
                cart = [];
                updateCartCount();
                showPage('login-page');
                showToast('Logged out successfully!');
            });

            // Mobile menu toggle
            document.getElementById('menu-toggle').addEventListener('click', function () {
                const sidebar = document.getElementById('admin-sidebar');
                sidebar.classList.toggle('collapsed');
            });

            // Load featured products on home page
            const featuredContainer = document.getElementById('featured-products');
            if (featuredContainer) {
                renderProducts(products.slice(0, 3), 'featured-products');
            }

            // Initialize
            showPage('login-page');
        });
    </script>
</body>

</html>