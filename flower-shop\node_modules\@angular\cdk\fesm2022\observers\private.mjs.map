{"version": 3, "file": "private.mjs", "sources": ["../../../../../../k8-fastbuild-ST-46c76129e412/bin/src/cdk/observers/private/shared-resize-observer.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\nimport {inject, Injectable, NgZone, OnDestroy, RendererFactory2} from '@angular/core';\nimport {Observable, Subject} from 'rxjs';\nimport {filter, shareReplay, takeUntil} from 'rxjs/operators';\n\n/**\n * <PERSON><PERSON> that logs \"ResizeObserver loop limit exceeded\" errors.\n * These errors are not shown in the Chrome console, so we log them to ensure developers are aware.\n * @param e The error\n */\nconst loopLimitExceededErrorHandler = (e: unknown) => {\n  if (e instanceof ErrorEvent && e.message === 'ResizeObserver loop limit exceeded') {\n    console.error(\n      `${e.message}. This could indicate a performance issue with your app. See https://github.com/WICG/resize-observer/blob/master/explainer.md#error-handling`,\n    );\n  }\n};\n\n/**\n * A shared ResizeObserver to be used for a particular box type (content-box, border-box, or\n * device-pixel-content-box)\n */\nclass SingleBoxSharedResizeObserver {\n  /** Stream that emits when the shared observer is destroyed. */\n  private _destroyed = new Subject<void>();\n  /** Stream of all events from the ResizeObserver. */\n  private _resizeSubject = new Subject<ResizeObserverEntry[]>();\n  /** ResizeObserver used to observe element resize events. */\n  private _resizeObserver?: ResizeObserver;\n  /** A map of elements to streams of their resize events. */\n  private _elementObservables = new Map<Element, Observable<ResizeObserverEntry[]>>();\n\n  constructor(\n    /** The box type to observe for resizes. */\n    private _box: ResizeObserverBoxOptions,\n  ) {\n    if (typeof ResizeObserver !== 'undefined') {\n      this._resizeObserver = new ResizeObserver(entries => this._resizeSubject.next(entries));\n    }\n  }\n\n  /**\n   * Gets a stream of resize events for the given element.\n   * @param target The element to observe.\n   * @return The stream of resize events for the element.\n   */\n  observe(target: Element): Observable<ResizeObserverEntry[]> {\n    if (!this._elementObservables.has(target)) {\n      this._elementObservables.set(\n        target,\n        new Observable<ResizeObserverEntry[]>(observer => {\n          const subscription = this._resizeSubject.subscribe(observer);\n          this._resizeObserver?.observe(target, {box: this._box});\n          return () => {\n            this._resizeObserver?.unobserve(target);\n            subscription.unsubscribe();\n            this._elementObservables.delete(target);\n          };\n        }).pipe(\n          filter(entries => entries.some(entry => entry.target === target)),\n          // Share a replay of the last event so that subsequent calls to observe the same element\n          // receive initial sizing info like the first one. Also enable ref counting so the\n          // element will be automatically unobserved when there are no more subscriptions.\n          shareReplay({bufferSize: 1, refCount: true}),\n          takeUntil(this._destroyed),\n        ),\n      );\n    }\n    return this._elementObservables.get(target)!;\n  }\n\n  /** Destroys this instance. */\n  destroy() {\n    this._destroyed.next();\n    this._destroyed.complete();\n    this._resizeSubject.complete();\n    this._elementObservables.clear();\n  }\n}\n\n/**\n * Allows observing resize events on multiple elements using a shared set of ResizeObserver.\n * Sharing a ResizeObserver instance is recommended for better performance (see\n * https://github.com/WICG/resize-observer/issues/59).\n *\n * Rather than share a single `ResizeObserver`, this class creates one `ResizeObserver` per type\n * of observed box ('content-box', 'border-box', and 'device-pixel-content-box'). This avoids\n * later calls to `observe` with a different box type from influencing the events dispatched to\n * earlier calls.\n */\n@Injectable({\n  providedIn: 'root',\n})\nexport class SharedResizeObserver implements OnDestroy {\n  private _cleanupErrorListener: (() => void) | undefined;\n\n  /** Map of box type to shared resize observer. */\n  private _observers = new Map<ResizeObserverBoxOptions, SingleBoxSharedResizeObserver>();\n\n  /** The Angular zone. */\n  private _ngZone = inject(NgZone);\n\n  constructor() {\n    if (typeof ResizeObserver !== 'undefined' && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      this._ngZone.runOutsideAngular(() => {\n        const renderer = inject(RendererFactory2).createRenderer(null, null);\n        this._cleanupErrorListener = renderer.listen(\n          'window',\n          'error',\n          loopLimitExceededErrorHandler,\n        );\n      });\n    }\n  }\n\n  ngOnDestroy() {\n    for (const [, observer] of this._observers) {\n      observer.destroy();\n    }\n    this._observers.clear();\n    this._cleanupErrorListener?.();\n  }\n\n  /**\n   * Gets a stream of resize events for the given target element and box type.\n   * @param target The element to observe for resizes.\n   * @param options Options to pass to the `ResizeObserver`\n   * @return The stream of resize events for the element.\n   */\n  observe(target: Element, options?: ResizeObserverOptions): Observable<ResizeObserverEntry[]> {\n    const box = options?.box || 'content-box';\n    if (!this._observers.has(box)) {\n      this._observers.set(box, new SingleBoxSharedResizeObserver(box));\n    }\n    return this._observers.get(box)!.observe(target);\n  }\n}\n"], "names": [], "mappings": ";;;;;AAWA;;;;AAIG;AACH,MAAM,6BAA6B,GAAG,CAAC,CAAU,KAAI;IACnD,IAAI,CAAC,YAAY,UAAU,IAAI,CAAC,CAAC,OAAO,KAAK,oCAAoC,EAAE;QACjF,OAAO,CAAC,KAAK,CACX,CAAA,EAAG,CAAC,CAAC,OAAO,CAA8I,4IAAA,CAAA,CAC3J;;AAEL,CAAC;AAED;;;AAGG;AACH,MAAM,6BAA6B,CAAA;AAYvB,IAAA,IAAA;;AAVF,IAAA,UAAU,GAAG,IAAI,OAAO,EAAQ;;AAEhC,IAAA,cAAc,GAAG,IAAI,OAAO,EAAyB;;AAErD,IAAA,eAAe;;AAEf,IAAA,mBAAmB,GAAG,IAAI,GAAG,EAA8C;AAEnF,IAAA,WAAA;;IAEU,IAA8B,EAAA;QAA9B,IAAI,CAAA,IAAA,GAAJ,IAAI;AAEZ,QAAA,IAAI,OAAO,cAAc,KAAK,WAAW,EAAE;AACzC,YAAA,IAAI,CAAC,eAAe,GAAG,IAAI,cAAc,CAAC,OAAO,IAAI,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;;;AAI3F;;;;AAIG;AACH,IAAA,OAAO,CAAC,MAAe,EAAA;QACrB,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE;AACzC,YAAA,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAC1B,MAAM,EACN,IAAI,UAAU,CAAwB,QAAQ,IAAG;gBAC/C,MAAM,YAAY,GAAG,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,QAAQ,CAAC;AAC5D,gBAAA,IAAI,CAAC,eAAe,EAAE,OAAO,CAAC,MAAM,EAAE,EAAC,GAAG,EAAE,IAAI,CAAC,IAAI,EAAC,CAAC;AACvD,gBAAA,OAAO,MAAK;AACV,oBAAA,IAAI,CAAC,eAAe,EAAE,SAAS,CAAC,MAAM,CAAC;oBACvC,YAAY,CAAC,WAAW,EAAE;AAC1B,oBAAA,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,MAAM,CAAC;AACzC,iBAAC;aACF,CAAC,CAAC,IAAI,CACL,MAAM,CAAC,OAAO,IAAI,OAAO,CAAC,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,MAAM,KAAK,MAAM,CAAC,CAAC;;;;YAIjE,WAAW,CAAC,EAAC,UAAU,EAAE,CAAC,EAAE,QAAQ,EAAE,IAAI,EAAC,CAAC,EAC5C,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,CAC3B,CACF;;QAEH,OAAO,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,MAAM,CAAE;;;IAI9C,OAAO,GAAA;AACL,QAAA,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE;AACtB,QAAA,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE;AAC1B,QAAA,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE;AAC9B,QAAA,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE;;AAEnC;AAED;;;;;;;;;AASG;MAIU,oBAAoB,CAAA;AACvB,IAAA,qBAAqB;;AAGrB,IAAA,UAAU,GAAG,IAAI,GAAG,EAA2D;;AAG/E,IAAA,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC;AAEhC,IAAA,WAAA,GAAA;AACE,QAAA,IAAI,OAAO,cAAc,KAAK,WAAW,KAAK,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,CAAC,EAAE;AAC5F,YAAA,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,MAAK;AAClC,gBAAA,MAAM,QAAQ,GAAG,MAAM,CAAC,gBAAgB,CAAC,CAAC,cAAc,CAAC,IAAI,EAAE,IAAI,CAAC;AACpE,gBAAA,IAAI,CAAC,qBAAqB,GAAG,QAAQ,CAAC,MAAM,CAC1C,QAAQ,EACR,OAAO,EACP,6BAA6B,CAC9B;AACH,aAAC,CAAC;;;IAIN,WAAW,GAAA;QACT,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,IAAI,CAAC,UAAU,EAAE;YAC1C,QAAQ,CAAC,OAAO,EAAE;;AAEpB,QAAA,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE;AACvB,QAAA,IAAI,CAAC,qBAAqB,IAAI;;AAGhC;;;;;AAKG;IACH,OAAO,CAAC,MAAe,EAAE,OAA+B,EAAA;AACtD,QAAA,MAAM,GAAG,GAAG,OAAO,EAAE,GAAG,IAAI,aAAa;QACzC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;AAC7B,YAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,6BAA6B,CAAC,GAAG,CAAC,CAAC;;AAElE,QAAA,OAAO,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,CAAE,CAAC,OAAO,CAAC,MAAM,CAAC;;uGAzCvC,oBAAoB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA;AAApB,IAAA,OAAA,KAAA,GAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,oBAAoB,cAFnB,MAAM,EAAA,CAAA;;2FAEP,oBAAoB,EAAA,UAAA,EAAA,CAAA;kBAHhC,UAAU;AAAC,YAAA,IAAA,EAAA,CAAA;AACV,oBAAA,UAAU,EAAE,MAAM;AACnB,iBAAA;;;;;"}