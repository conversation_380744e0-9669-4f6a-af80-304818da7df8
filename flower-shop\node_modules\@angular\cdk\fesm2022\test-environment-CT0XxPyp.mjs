/** Gets whether the code is currently running in a test environment. */
function _isTestEnvironment() {
    // We can't use `declare const` because it causes conflicts inside Google with the real typings
    // for these symbols and we can't read them off the global object, because they don't appear to
    // be attached there for some runners like Je<PERSON>.
    // (see: https://github.com/angular/components/issues/23365#issuecomment-938146643)
    return (
    // @ts-ignore
    (typeof __karma__ !== 'undefined' && !!__karma__) ||
        // @ts-ignore
        (typeof jasmine !== 'undefined' && !!jasmine) ||
        // @ts-ignore
        (typeof jest !== 'undefined' && !!jest) ||
        // @ts-ignore
        (typeof Mocha !== 'undefined' && !!Mocha));
}

export { _isTestEnvironment as _ };
//# sourceMappingURL=test-environment-CT0XxPyp.mjs.map
