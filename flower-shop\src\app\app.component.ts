import { Component, OnInit } from '@angular/core';
import { Router, RouterOutlet } from '@angular/router';
import { CommonModule } from '@angular/common';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatBadgeModule } from '@angular/material/badge';
import { MatMenuModule } from '@angular/material/menu';
import { Observable } from 'rxjs';
import { AuthService } from './services/auth.service';
import { CartService } from './services/cart.service';
import { User } from './models';

@Component({
    selector: 'app-root',
    imports: [
        CommonModule,
        RouterOutlet,
        MatToolbarModule,
        MatButtonModule,
        MatIconModule,
        MatBadgeModule,
        MatMenuModule
    ],
    templateUrl: './app.component.html',
    styleUrl: './app.component.scss'
})
export class AppComponent implements OnInit {
    title = 'Flower Shop';
    currentUser$: Observable<User | null>;
    cartItemCount$: Observable<number>;

    constructor(
        private authService: AuthService,
        private cartService: CartService,
        private router: Router
    ) {
        this.currentUser$ = this.authService.currentUser$;
        this.cartItemCount$ = this.cartService.getCartItemCount();
    }

    ngOnInit(): void {
        // Navigation logic can be added here if needed
    }

    logout(): void {
        this.authService.logout();
        this.router.navigate(['/']);
    }

    goToCart(): void {
        this.router.navigate(['/cart']);
    }

    goToAdmin(): void {
        this.router.navigate(['/admin']);
    }

    goToLogin(): void {
        this.router.navigate(['/login']);
    }

    goToHome(): void {
        this.router.navigate(['/']);
    }
}
