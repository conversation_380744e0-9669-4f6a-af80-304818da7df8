import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { CommonModule } from '@angular/common';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { MatBadgeModule } from '@angular/material/badge';
import { MatDividerModule } from '@angular/material/divider';
import { RouterOutlet } from '@angular/router';
import { Observable, of } from 'rxjs';

@Component({
    selector: 'app-root',
    imports: [
        CommonModule,
        RouterOutlet,
        MatToolbarModule,
        MatButtonModule,
        MatIconModule,
        MatMenuModule,
        MatBadgeModule,
        MatDividerModule
    ],
    templateUrl: './app.component.html',
    styleUrl: './app.component.scss'
})
export class AppComponent implements OnInit {
    title = 'Petals & Blooms';
    currentUser$: Observable<any>;
    cartItemCount$: Observable<number>;
    mobileMenuOpen = false;
    showToast = false;
    toastMessage = '';
    isLoading = false;

    constructor(
        private router: Router
    ) {
        // Mock data for now
        this.currentUser$ = of(null);
        this.cartItemCount$ = of(0);
    }

    ngOnInit(): void {
        // Component initialization
    }

    isHomeActive(): boolean {
        return this.router.url === '/';
    }

    goToHome(): void {
        this.router.navigate(['/']);
        this.closeMobileMenu();
    }

    goToLogin(): void {
        this.router.navigate(['/login']);
        this.closeMobileMenu();
    }

    goToRegister(): void {
        this.router.navigate(['/register']);
        this.closeMobileMenu();
    }

    goToCart(): void {
        this.router.navigate(['/cart']);
        this.closeMobileMenu();
    }

    goToCatalog(): void {
        this.router.navigate(['/']);
        this.closeMobileMenu();
    }

    goToAdmin(): void {
        this.router.navigate(['/admin']);
        this.closeMobileMenu();
    }

    toggleMobileMenu(): void {
        this.mobileMenuOpen = !this.mobileMenuOpen;
    }

    closeMobileMenu(): void {
        this.mobileMenuOpen = false;
    }

    logout(): void {
        // Mock logout for now
        this.router.navigate(['/']);
        this.closeMobileMenu();
    }

    displayToast(message: string): void {
        this.toastMessage = message;
        this.showToast = true;
        setTimeout(() => {
            this.showToast = false;
        }, 3000);
    }
}
