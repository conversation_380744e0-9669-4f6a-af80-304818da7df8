<div class="admin-container">
    <div class="admin-header">
        <h1>Admin Dashboard</h1>
    </div>

    <mat-tab-group>
        <!-- Flower Management Tab -->
        <mat-tab label="Flower Management">
            <div class="tab-content">
                <!-- Add/Edit Flower Form -->
                <mat-card class="form-card">
                    <mat-card-header>
                        <mat-card-title>
                            {{ editingFlower ? 'Edit Flower' : 'Add New Flower' }}
                        </mat-card-title>
                    </mat-card-header>

                    <mat-card-content>
                        <form [formGroup]="flowerForm" (ngSubmit)="onSubmitFlower()">
                            <div class="form-row">
                                <mat-form-field appearance="outline" class="half-width">
                                    <mat-label>Name</mat-label>
                                    <input matInput formControlName="name" required>
                                    <mat-error *ngIf="flowerForm.get('name')?.hasError('required')">
                                        Name is required
                                    </mat-error>
                                </mat-form-field>

                                <mat-form-field appearance="outline" class="half-width">
                                    <mat-label>Category</mat-label>
                                    <mat-select formControlName="category" required>
                                        <mat-option value="Roses">Roses</mat-option>
                                        <mat-option value="Tulips">Tulips</mat-option>
                                        <mat-option value="Lilies">Lilies</mat-option>
                                        <mat-option value="Orchids">Orchids</mat-option>
                                        <mat-option value="Sunflowers">Sunflowers</mat-option>
                                        <mat-option value="Mixed">Mixed</mat-option>
                                    </mat-select>
                                    <mat-error *ngIf="flowerForm.get('category')?.hasError('required')">
                                        Category is required
                                    </mat-error>
                                </mat-form-field>
                            </div>

                            <div class="form-row">
                                <mat-form-field appearance="outline" class="full-width">
                                    <mat-label>Description</mat-label>
                                    <textarea matInput formControlName="description" rows="3" required></textarea>
                                    <mat-error *ngIf="flowerForm.get('description')?.hasError('required')">
                                        Description is required
                                    </mat-error>
                                </mat-form-field>
                            </div>

                            <div class="form-row">
                                <mat-form-field appearance="outline" class="third-width">
                                    <mat-label>Price ($)</mat-label>
                                    <input matInput type="number" formControlName="price" step="0.01" required>
                                    <mat-error *ngIf="flowerForm.get('price')?.hasError('required')">
                                        Price is required
                                    </mat-error>
                                </mat-form-field>

                                <mat-form-field appearance="outline" class="third-width">
                                    <mat-label>Stock</mat-label>
                                    <input matInput type="number" formControlName="stock" required>
                                    <mat-error *ngIf="flowerForm.get('stock')?.hasError('required')">
                                        Stock is required
                                    </mat-error>
                                </mat-form-field>

                                <mat-form-field appearance="outline" class="third-width">
                                    <mat-label>Image URL</mat-label>
                                    <input matInput formControlName="imageUrl" required>
                                    <mat-error *ngIf="flowerForm.get('imageUrl')?.hasError('required')">
                                        Image URL is required
                                    </mat-error>
                                </mat-form-field>
                            </div>

                            <div class="form-actions">
                                <button mat-stroked-button type="button" (click)="resetForm()" *ngIf="editingFlower">
                                    Cancel
                                </button>
                                <button mat-raised-button color="primary" type="submit" [disabled]="flowerForm.invalid">
                                    {{ editingFlower ? 'Update Flower' : 'Add Flower' }}
                                </button>
                            </div>
                        </form>
                    </mat-card-content>
                </mat-card>

                <!-- Flowers Table -->
                <mat-card class="table-card">
                    <mat-card-header>
                        <mat-card-title>Flower Inventory</mat-card-title>
                    </mat-card-header>

                    <mat-card-content>
                        <div *ngIf="flowers$ | async as flowers">
                            <table mat-table [dataSource]="flowers" class="flowers-table">
                                <ng-container matColumnDef="id">
                                    <th mat-header-cell *matHeaderCellDef>ID</th>
                                    <td mat-cell *matCellDef="let flower">{{ flower.id }}</td>
                                </ng-container>

                                <ng-container matColumnDef="name">
                                    <th mat-header-cell *matHeaderCellDef>Name</th>
                                    <td mat-cell *matCellDef="let flower">{{ flower.name }}</td>
                                </ng-container>

                                <ng-container matColumnDef="category">
                                    <th mat-header-cell *matHeaderCellDef>Category</th>
                                    <td mat-cell *matCellDef="let flower">{{ flower.category }}</td>
                                </ng-container>

                                <ng-container matColumnDef="price">
                                    <th mat-header-cell *matHeaderCellDef>Price</th>
                                    <td mat-cell *matCellDef="let flower">${{ flower.price | number:'1.2-2' }}</td>
                                </ng-container>

                                <ng-container matColumnDef="stock">
                                    <th mat-header-cell *matHeaderCellDef>Stock</th>
                                    <td mat-cell *matCellDef="let flower">{{ flower.stock }}</td>
                                </ng-container>

                                <ng-container matColumnDef="actions">
                                    <th mat-header-cell *matHeaderCellDef>Actions</th>
                                    <td mat-cell *matCellDef="let flower">
                                        <button mat-icon-button color="primary" (click)="editFlower(flower)"
                                            matTooltip="Edit">
                                            <mat-icon>edit</mat-icon>
                                        </button>
                                        <button mat-icon-button color="warn" (click)="deleteFlower(flower.id)"
                                            matTooltip="Delete">
                                            <mat-icon>delete</mat-icon>
                                        </button>
                                    </td>
                                </ng-container>

                                <tr mat-header-row *matHeaderRowDef="flowerDisplayedColumns"></tr>
                                <tr mat-row *matRowDef="let row; columns: flowerDisplayedColumns;"></tr>
                            </table>
                        </div>
                    </mat-card-content>
                </mat-card>
            </div>
        </mat-tab>

        <!-- Order Management Tab -->
        <mat-tab label="Order Management">
            <div class="tab-content">
                <mat-card class="table-card">
                    <mat-card-header>
                        <mat-card-title>Orders</mat-card-title>
                    </mat-card-header>

                    <mat-card-content>
                        <div *ngIf="orders$ | async as orders">
                            <table mat-table [dataSource]="orders" class="orders-table">
                                <ng-container matColumnDef="id">
                                    <th mat-header-cell *matHeaderCellDef>Order ID</th>
                                    <td mat-cell *matCellDef="let order">{{ order.id }}</td>
                                </ng-container>

                                <ng-container matColumnDef="customerName">
                                    <th mat-header-cell *matHeaderCellDef>Customer</th>
                                    <td mat-cell *matCellDef="let order">{{ order.customerName }}</td>
                                </ng-container>

                                <ng-container matColumnDef="totalPrice">
                                    <th mat-header-cell *matHeaderCellDef>Total</th>
                                    <td mat-cell *matCellDef="let order">${{ order.totalPrice | number:'1.2-2' }}</td>
                                </ng-container>

                                <ng-container matColumnDef="status">
                                    <th mat-header-cell *matHeaderCellDef>Status</th>
                                    <td mat-cell *matCellDef="let order">
                                        <mat-select [value]="order.status"
                                            (selectionChange)="updateOrderStatus(order.id, $event.value)">
                                            <mat-option *ngFor="let status of orderStatuses" [value]="status">
                                                {{ status }}
                                            </mat-option>
                                        </mat-select>
                                    </td>
                                </ng-container>

                                <ng-container matColumnDef="createdAt">
                                    <th mat-header-cell *matHeaderCellDef>Date</th>
                                    <td mat-cell *matCellDef="let order">{{ order.createdAt | date:'short' }}</td>
                                </ng-container>

                                <ng-container matColumnDef="actions">
                                    <th mat-header-cell *matHeaderCellDef>Actions</th>
                                    <td mat-cell *matCellDef="let order">
                                        <button mat-icon-button matTooltip="View Details">
                                            <mat-icon>visibility</mat-icon>
                                        </button>
                                    </td>
                                </ng-container>

                                <tr mat-header-row *matHeaderRowDef="orderDisplayedColumns"></tr>
                                <tr mat-row *matRowDef="let row; columns: orderDisplayedColumns;"></tr>
                            </table>
                        </div>
                    </mat-card-content>
                </mat-card>
            </div>
        </mat-tab>
    </mat-tab-group>
</div>
