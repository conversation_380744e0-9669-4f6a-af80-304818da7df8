import * as i0 from '@angular/core';
import { M as MatRippleModule } from './index.d-DG9eDM2-.js';
import { M as MatCommonModule } from './common-module.d-C8xzHJDr.js';
import { M as MatPseudoCheckboxModule } from './pseudo-checkbox-module.d-DL5oxSJM.js';
import { M as MatOption, a as MatOptgroup } from './option.d-BVGX3edu.js';

declare class MatOptionModule {
    static ɵfac: i0.ɵɵFactoryDeclaration<MatOptionModule, never>;
    static ɵmod: i0.ɵɵNgModuleDeclaration<MatOptionModule, never, [typeof MatRippleModule, typeof MatCommonModule, typeof MatPseudoCheckboxModule, typeof MatOption, typeof MatOptgroup], [typeof MatOption, typeof MatOptgroup]>;
    static ɵinj: i0.ɵɵInjectorDeclaration<MatOptionModule>;
}

export { MatOptionModule as M };
