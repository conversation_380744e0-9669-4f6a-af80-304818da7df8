{"version": 3, "file": "public-api-BoO5eSq-.mjs", "sources": ["../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/grid-list/tile-coordinator.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/grid-list/public-api.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n/**\n * Interface describing a tile.\n * @docs-private\n */\nexport interface Tile {\n  /** Amount of rows that the tile takes up. */\n  rowspan: number;\n  /** Amount of columns that the tile takes up. */\n  colspan: number;\n}\n\n/**\n * Class for determining, from a list of tiles, the (row, col) position of each of those tiles\n * in the grid. This is necessary (rather than just rendering the tiles in normal document flow)\n * because the tiles can have a rowspan.\n *\n * The positioning algorithm greedily places each tile as soon as it encounters a gap in the grid\n * large enough to accommodate it so that the tiles still render in the same order in which they\n * are given.\n *\n * The basis of the algorithm is the use of an array to track the already placed tiles. Each\n * element of the array corresponds to a column, and the value indicates how many cells in that\n * column are already occupied; zero indicates an empty cell. Moving \"down\" to the next row\n * decrements each value in the tracking array (indicating that the column is one cell closer to\n * being free).\n *\n * @docs-private\n */\nexport class TileCoordinator {\n  /** Tracking array (see class description). */\n  tracker: number[];\n\n  /** Index at which the search for the next gap will start. */\n  columnIndex: number = 0;\n\n  /** The current row index. */\n  rowIndex: number = 0;\n\n  /** Gets the total number of rows occupied by tiles */\n  get rowCount(): number {\n    return this.rowIndex + 1;\n  }\n\n  /**\n   * Gets the total span of rows occupied by tiles.\n   * Ex: A list with 1 row that contains a tile with rowspan 2 will have a total rowspan of 2.\n   */\n  get rowspan() {\n    const lastRowMax = Math.max(...this.tracker);\n    // if any of the tiles has a rowspan that pushes it beyond the total row count,\n    // add the difference to the rowcount\n    return lastRowMax > 1 ? this.rowCount + lastRowMax - 1 : this.rowCount;\n  }\n\n  /** The computed (row, col) position of each tile (the output). */\n  positions: TilePosition[];\n\n  /**\n   * Updates the tile positions.\n   * @param numColumns Amount of columns in the grid.\n   * @param tiles Tiles to be positioned.\n   */\n  update(numColumns: number, tiles: Tile[]) {\n    this.columnIndex = 0;\n    this.rowIndex = 0;\n\n    this.tracker = new Array(numColumns);\n    this.tracker.fill(0, 0, this.tracker.length);\n    this.positions = tiles.map(tile => this._trackTile(tile));\n  }\n\n  /** Calculates the row and col position of a tile. */\n  private _trackTile(tile: Tile): TilePosition {\n    // Find a gap large enough for this tile.\n    const gapStartIndex = this._findMatchingGap(tile.colspan);\n\n    // Place tile in the resulting gap.\n    this._markTilePosition(gapStartIndex, tile);\n\n    // The next time we look for a gap, the search will start at columnIndex, which should be\n    // immediately after the tile that has just been placed.\n    this.columnIndex = gapStartIndex + tile.colspan;\n\n    return new TilePosition(this.rowIndex, gapStartIndex);\n  }\n\n  /** Finds the next available space large enough to fit the tile. */\n  private _findMatchingGap(tileCols: number): number {\n    if (tileCols > this.tracker.length && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw Error(\n        `mat-grid-list: tile with colspan ${tileCols} is wider than ` +\n          `grid with cols=\"${this.tracker.length}\".`,\n      );\n    }\n\n    // Start index is inclusive, end index is exclusive.\n    let gapStartIndex = -1;\n    let gapEndIndex = -1;\n\n    // Look for a gap large enough to fit the given tile. Empty spaces are marked with a zero.\n    do {\n      // If we've reached the end of the row, go to the next row.\n      if (this.columnIndex + tileCols > this.tracker.length) {\n        this._nextRow();\n        gapStartIndex = this.tracker.indexOf(0, this.columnIndex);\n        gapEndIndex = this._findGapEndIndex(gapStartIndex);\n        continue;\n      }\n\n      gapStartIndex = this.tracker.indexOf(0, this.columnIndex);\n\n      // If there are no more empty spaces in this row at all, move on to the next row.\n      if (gapStartIndex == -1) {\n        this._nextRow();\n        gapStartIndex = this.tracker.indexOf(0, this.columnIndex);\n        gapEndIndex = this._findGapEndIndex(gapStartIndex);\n        continue;\n      }\n\n      gapEndIndex = this._findGapEndIndex(gapStartIndex);\n\n      // If a gap large enough isn't found, we want to start looking immediately after the current\n      // gap on the next iteration.\n      this.columnIndex = gapStartIndex + 1;\n\n      // Continue iterating until we find a gap wide enough for this tile. Since gapEndIndex is\n      // exclusive, gapEndIndex is 0 means we didn't find a gap and should continue.\n    } while (gapEndIndex - gapStartIndex < tileCols || gapEndIndex == 0);\n\n    // If we still didn't manage to find a gap, ensure that the index is\n    // at least zero so the tile doesn't get pulled out of the grid.\n    return Math.max(gapStartIndex, 0);\n  }\n\n  /** Move \"down\" to the next row. */\n  private _nextRow(): void {\n    this.columnIndex = 0;\n    this.rowIndex++;\n\n    // Decrement all spaces by one to reflect moving down one row.\n    for (let i = 0; i < this.tracker.length; i++) {\n      this.tracker[i] = Math.max(0, this.tracker[i] - 1);\n    }\n  }\n\n  /**\n   * Finds the end index (exclusive) of a gap given the index from which to start looking.\n   * The gap ends when a non-zero value is found.\n   */\n  private _findGapEndIndex(gapStartIndex: number): number {\n    for (let i = gapStartIndex + 1; i < this.tracker.length; i++) {\n      if (this.tracker[i] != 0) {\n        return i;\n      }\n    }\n\n    // The gap ends with the end of the row.\n    return this.tracker.length;\n  }\n\n  /** Update the tile tracker to account for the given tile in the given space. */\n  private _markTilePosition(start: number, tile: Tile): void {\n    for (let i = 0; i < tile.colspan; i++) {\n      this.tracker[start + i] = tile.rowspan;\n    }\n  }\n}\n\n/**\n * Simple data structure for tile position (row, col).\n * @docs-private\n */\nexport class TilePosition {\n  constructor(\n    public row: number,\n    public col: number,\n  ) {}\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\nimport {TileCoordinator} from './tile-coordinator';\n\nexport * from './grid-list-module';\nexport * from './grid-list';\nexport * from './grid-tile';\n\n// Privately exported for the grid-list harness.\nexport const ɵTileCoordinator = TileCoordinator;\n"], "names": [], "mappings": "AAmBA;;;;;;;;;;;;;;;;AAgBG;MACU,eAAe,CAAA;;AAE1B,IAAA,OAAO;;IAGP,WAAW,GAAW,CAAC;;IAGvB,QAAQ,GAAW,CAAC;;AAGpB,IAAA,IAAI,QAAQ,GAAA;AACV,QAAA,OAAO,IAAI,CAAC,QAAQ,GAAG,CAAC;;AAG1B;;;AAGG;AACH,IAAA,IAAI,OAAO,GAAA;QACT,MAAM,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;;;AAG5C,QAAA,OAAO,UAAU,GAAG,CAAC,GAAG,IAAI,CAAC,QAAQ,GAAG,UAAU,GAAG,CAAC,GAAG,IAAI,CAAC,QAAQ;;;AAIxE,IAAA,SAAS;AAET;;;;AAIG;IACH,MAAM,CAAC,UAAkB,EAAE,KAAa,EAAA;AACtC,QAAA,IAAI,CAAC,WAAW,GAAG,CAAC;AACpB,QAAA,IAAI,CAAC,QAAQ,GAAG,CAAC;QAEjB,IAAI,CAAC,OAAO,GAAG,IAAI,KAAK,CAAC,UAAU,CAAC;AACpC,QAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;AAC5C,QAAA,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC,GAAG,CAAC,IAAI,IAAI,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;;;AAInD,IAAA,UAAU,CAAC,IAAU,EAAA;;QAE3B,MAAM,aAAa,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,OAAO,CAAC;;AAGzD,QAAA,IAAI,CAAC,iBAAiB,CAAC,aAAa,EAAE,IAAI,CAAC;;;QAI3C,IAAI,CAAC,WAAW,GAAG,aAAa,GAAG,IAAI,CAAC,OAAO;QAE/C,OAAO,IAAI,YAAY,CAAC,IAAI,CAAC,QAAQ,EAAE,aAAa,CAAC;;;AAI/C,IAAA,gBAAgB,CAAC,QAAgB,EAAA;AACvC,QAAA,IAAI,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,KAAK,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,CAAC,EAAE;AACrF,YAAA,MAAM,KAAK,CACT,CAAoC,iCAAA,EAAA,QAAQ,CAAiB,eAAA,CAAA;AAC3D,gBAAA,CAAA,gBAAA,EAAmB,IAAI,CAAC,OAAO,CAAC,MAAM,CAAA,EAAA,CAAI,CAC7C;;;AAIH,QAAA,IAAI,aAAa,GAAG,CAAC,CAAC;AACtB,QAAA,IAAI,WAAW,GAAG,CAAC,CAAC;;AAGpB,QAAA,GAAG;;AAED,YAAA,IAAI,IAAI,CAAC,WAAW,GAAG,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;gBACrD,IAAI,CAAC,QAAQ,EAAE;AACf,gBAAA,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC,WAAW,CAAC;AACzD,gBAAA,WAAW,GAAG,IAAI,CAAC,gBAAgB,CAAC,aAAa,CAAC;gBAClD;;AAGF,YAAA,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC,WAAW,CAAC;;AAGzD,YAAA,IAAI,aAAa,IAAI,CAAC,CAAC,EAAE;gBACvB,IAAI,CAAC,QAAQ,EAAE;AACf,gBAAA,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC,WAAW,CAAC;AACzD,gBAAA,WAAW,GAAG,IAAI,CAAC,gBAAgB,CAAC,aAAa,CAAC;gBAClD;;AAGF,YAAA,WAAW,GAAG,IAAI,CAAC,gBAAgB,CAAC,aAAa,CAAC;;;AAIlD,YAAA,IAAI,CAAC,WAAW,GAAG,aAAa,GAAG,CAAC;;;SAIrC,QAAQ,WAAW,GAAG,aAAa,GAAG,QAAQ,IAAI,WAAW,IAAI,CAAC;;;QAInE,OAAO,IAAI,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC,CAAC;;;IAI3B,QAAQ,GAAA;AACd,QAAA,IAAI,CAAC,WAAW,GAAG,CAAC;QACpB,IAAI,CAAC,QAAQ,EAAE;;AAGf,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC5C,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;;;AAItD;;;AAGG;AACK,IAAA,gBAAgB,CAAC,aAAqB,EAAA;AAC5C,QAAA,KAAK,IAAI,CAAC,GAAG,aAAa,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC5D,IAAI,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE;AACxB,gBAAA,OAAO,CAAC;;;;AAKZ,QAAA,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM;;;IAIpB,iBAAiB,CAAC,KAAa,EAAE,IAAU,EAAA;AACjD,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC,EAAE,EAAE;YACrC,IAAI,CAAC,OAAO,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO;;;AAG3C;AAED;;;AAGG;MACU,YAAY,CAAA;AAEd,IAAA,GAAA;AACA,IAAA,GAAA;IAFT,WACS,CAAA,GAAW,EACX,GAAW,EAAA;QADX,IAAG,CAAA,GAAA,GAAH,GAAG;QACH,IAAG,CAAA,GAAA,GAAH,GAAG;;AAEb;;AC5KD;AACO,MAAM,gBAAgB,GAAG;;;;"}