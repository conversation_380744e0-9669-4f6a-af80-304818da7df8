@use 'sass:map';
@use 'sass:meta';
@use '../core/tokens/m2/mat/paginator' as tokens-mat-paginator;
@use '../core/style/sass-utils';
@use '../core/typography/typography';
@use '../core/theming/theming';
@use '../core/theming/inspection';
@use '../core/theming/validation';
@use '../core/tokens/token-utils';

@mixin base($theme) {
  @if inspection.get-theme-version($theme) == 1 {
    @include _theme-from-tokens(inspection.get-theme-tokens($theme, base));
  } @else {
  }
}

@mixin color($theme) {
  @if inspection.get-theme-version($theme) == 1 {
    @include _theme-from-tokens(inspection.get-theme-tokens($theme, color));
  } @else {
    @include sass-utils.current-selector-or-root() {
      @include token-utils.create-token-values(
        tokens-mat-paginator.$prefix,
        tokens-mat-paginator.get-color-tokens($theme)
      );
    }
  }
}

@mixin typography($theme) {
  @if inspection.get-theme-version($theme) == 1 {
    @include _theme-from-tokens(inspection.get-theme-tokens($theme, typography));
  } @else {
    @include sass-utils.current-selector-or-root() {
      @include token-utils.create-token-values(
        tokens-mat-paginator.$prefix,
        tokens-mat-paginator.get-typography-tokens($theme)
      );
    }
  }
}

@mixin density($theme) {
  $density-scale: inspection.get-theme-density($theme);
  $form-field-density: if(
    (meta.type-of($density-scale) == 'number' and $density-scale >= -4) or
      ($density-scale == maximum),
    -4,
    $density-scale
  );

  @if inspection.get-theme-version($theme) == 1 {
    @include _theme-from-tokens(inspection.get-theme-tokens($theme, density));
  } @else {
    @include sass-utils.current-selector-or-root() {
      @include token-utils.create-token-values(
        tokens-mat-paginator.$prefix,
        tokens-mat-paginator.get-density-tokens($theme)
      );
    }
  }
}

/// Defines the tokens that will be available in the `overrides` mixin and for docs extraction.
@function _define-overrides() {
  @return (
    (
      namespace: tokens-mat-paginator.$prefix,
      tokens: tokens-mat-paginator.get-token-slots(),
    ),
  );
}

@mixin overrides($tokens: ()) {
  @include token-utils.batch-create-token-values($tokens, _define-overrides()...);
}

@mixin theme($theme) {
  @include theming.private-check-duplicate-theme-styles($theme, 'mat-paginator') {
    @if inspection.get-theme-version($theme) == 1 {
      @include _theme-from-tokens(inspection.get-theme-tokens($theme));
    } @else {
      @include base($theme);
      @if inspection.theme-has($theme, color) {
        @include color($theme);
      }
      @if inspection.theme-has($theme, density) {
        @include density($theme);
      }
      @if inspection.theme-has($theme, typography) {
        @include typography($theme);
      }
    }
  }
}

@mixin _theme-from-tokens($tokens) {
  @include validation.selector-defined(
    'Calls to Angular Material theme mixins with an M3 theme must be wrapped in a selector'
  );
  @if ($tokens != ()) {
    @include token-utils.create-token-values(
      tokens-mat-paginator.$prefix,
      map.get($tokens, tokens-mat-paginator.$prefix)
    );
  }
}
