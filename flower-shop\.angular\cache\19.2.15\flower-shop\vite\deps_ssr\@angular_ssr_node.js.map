{"version": 3, "sources": ["../../../../../../node_modules/@angular/ssr/fesm2022/node.mjs"], "sourcesContent": ["import { renderApplication, renderModule, ɵSERVER_CONTEXT as _SERVER_CONTEXT } from '@angular/platform-server';\nimport * as fs from 'node:fs';\nimport { dirname, join, normalize, resolve } from 'node:path';\nimport { URL as URL$1, fileURLToPath } from 'node:url';\nimport { ɵInlineCriticalCssProcessor as _InlineCriticalCssProcessor, AngularAppEngine } from '@angular/ssr';\nimport { readFile } from 'node:fs/promises';\nimport { argv } from 'node:process';\nclass CommonEngineInlineCriticalCssProcessor {\n  resourceCache = new Map();\n  async process(html, outputPath) {\n    const beasties = new _InlineCriticalCssProcessor(async path => {\n      let resourceContent = this.resourceCache.get(path);\n      if (resourceContent === undefined) {\n        resourceContent = await readFile(path, 'utf-8');\n        this.resourceCache.set(path, resourceContent);\n      }\n      return resourceContent;\n    }, outputPath);\n    return beasties.process(html);\n  }\n}\nconst PERFORMANCE_MARK_PREFIX = '🅰️';\nfunction printPerformanceLogs() {\n  let maxWordLength = 0;\n  const benchmarks = [];\n  for (const {\n    name,\n    duration\n  } of performance.getEntriesByType('measure')) {\n    if (!name.startsWith(PERFORMANCE_MARK_PREFIX)) {\n      continue;\n    }\n    // `🅰️:Retrieve SSG Page` -> `Retrieve SSG Page:`\n    const step = name.slice(PERFORMANCE_MARK_PREFIX.length + 1) + ':';\n    if (step.length > maxWordLength) {\n      maxWordLength = step.length;\n    }\n    benchmarks.push([step, `${duration.toFixed(1)}ms`]);\n    performance.clearMeasures(name);\n  }\n  /* eslint-disable no-console */\n  console.log('********** Performance results **********');\n  for (const [step, value] of benchmarks) {\n    const spaces = maxWordLength - step.length + 5;\n    console.log(step + ' '.repeat(spaces) + value);\n  }\n  console.log('*****************************************');\n  /* eslint-enable no-console */\n}\nasync function runMethodAndMeasurePerf(label, asyncMethod) {\n  const labelName = `${PERFORMANCE_MARK_PREFIX}:${label}`;\n  const startLabel = `start:${labelName}`;\n  const endLabel = `end:${labelName}`;\n  try {\n    performance.mark(startLabel);\n    return await asyncMethod();\n  } finally {\n    performance.mark(endLabel);\n    performance.measure(labelName, startLabel, endLabel);\n    performance.clearMarks(startLabel);\n    performance.clearMarks(endLabel);\n  }\n}\nfunction noopRunMethodAndMeasurePerf(label, asyncMethod) {\n  return asyncMethod();\n}\nconst SSG_MARKER_REGEXP = /ng-server-context=[\"']\\w*\\|?ssg\\|?\\w*[\"']/;\n/**\n * A common engine to use to server render an application.\n */\nclass CommonEngine {\n  options;\n  templateCache = new Map();\n  inlineCriticalCssProcessor = new CommonEngineInlineCriticalCssProcessor();\n  pageIsSSG = new Map();\n  constructor(options) {\n    this.options = options;\n  }\n  /**\n   * Render an HTML document for a specific URL with specified\n   * render options\n   */\n  async render(opts) {\n    const enablePerformanceProfiler = this.options?.enablePerformanceProfiler;\n    const runMethod = enablePerformanceProfiler ? runMethodAndMeasurePerf : noopRunMethodAndMeasurePerf;\n    let html = await runMethod('Retrieve SSG Page', () => this.retrieveSSGPage(opts));\n    if (html === undefined) {\n      html = await runMethod('Render Page', () => this.renderApplication(opts));\n      if (opts.inlineCriticalCss !== false) {\n        const content = await runMethod('Inline Critical CSS', () =>\n        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n        this.inlineCriticalCss(html, opts));\n        html = content;\n      }\n    }\n    if (enablePerformanceProfiler) {\n      printPerformanceLogs();\n    }\n    return html;\n  }\n  inlineCriticalCss(html, opts) {\n    const outputPath = opts.publicPath ?? (opts.documentFilePath ? dirname(opts.documentFilePath) : '');\n    return this.inlineCriticalCssProcessor.process(html, outputPath);\n  }\n  async retrieveSSGPage(opts) {\n    const {\n      publicPath,\n      documentFilePath,\n      url\n    } = opts;\n    if (!publicPath || !documentFilePath || url === undefined) {\n      return undefined;\n    }\n    const {\n      pathname\n    } = new URL$1(url, 'resolve://');\n    // Do not use `resolve` here as otherwise it can lead to path traversal vulnerability.\n    // See: https://portswigger.net/web-security/file-path-traversal\n    const pagePath = join(publicPath, pathname, 'index.html');\n    if (this.pageIsSSG.get(pagePath)) {\n      // Serve pre-rendered page.\n      return fs.promises.readFile(pagePath, 'utf-8');\n    }\n    if (!pagePath.startsWith(normalize(publicPath))) {\n      // Potential path traversal detected.\n      return undefined;\n    }\n    if (pagePath === resolve(documentFilePath) || !(await exists(pagePath))) {\n      // View matches with prerender path or file does not exist.\n      this.pageIsSSG.set(pagePath, false);\n      return undefined;\n    }\n    // Static file exists.\n    const content = await fs.promises.readFile(pagePath, 'utf-8');\n    const isSSG = SSG_MARKER_REGEXP.test(content);\n    this.pageIsSSG.set(pagePath, isSSG);\n    return isSSG ? content : undefined;\n  }\n  async renderApplication(opts) {\n    const moduleOrFactory = this.options?.bootstrap ?? opts.bootstrap;\n    if (!moduleOrFactory) {\n      throw new Error('A module or bootstrap option must be provided.');\n    }\n    const extraProviders = [{\n      provide: _SERVER_CONTEXT,\n      useValue: 'ssr'\n    }, ...(opts.providers ?? []), ...(this.options?.providers ?? [])];\n    let document = opts.document;\n    if (!document && opts.documentFilePath) {\n      document = await this.getDocument(opts.documentFilePath);\n    }\n    const commonRenderingOptions = {\n      url: opts.url,\n      document\n    };\n    return isBootstrapFn(moduleOrFactory) ? renderApplication(moduleOrFactory, {\n      platformProviders: extraProviders,\n      ...commonRenderingOptions\n    }) : renderModule(moduleOrFactory, {\n      extraProviders,\n      ...commonRenderingOptions\n    });\n  }\n  /** Retrieve the document from the cache or the filesystem */\n  async getDocument(filePath) {\n    let doc = this.templateCache.get(filePath);\n    if (!doc) {\n      doc = await fs.promises.readFile(filePath, 'utf-8');\n      this.templateCache.set(filePath, doc);\n    }\n    return doc;\n  }\n}\nasync function exists(path) {\n  try {\n    await fs.promises.access(path, fs.constants.F_OK);\n    return true;\n  } catch {\n    return false;\n  }\n}\nfunction isBootstrapFn(value) {\n  // We can differentiate between a module and a bootstrap function by reading compiler-generated `ɵmod` static property:\n  return typeof value === 'function' && !('ɵmod' in value);\n}\n\n/**\n * A set containing all the pseudo-headers defined in the HTTP/2 specification.\n *\n * This set can be used to filter out pseudo-headers from a list of headers,\n * as they are not allowed to be set directly using the `Node.js` Undici API or\n * the web `Headers` API.\n */\nconst HTTP2_PSEUDO_HEADERS = new Set([':method', ':scheme', ':authority', ':path', ':status']);\n/**\n * Converts a Node.js `IncomingMessage` or `Http2ServerRequest` into a\n * Web Standard `Request` object.\n *\n * This function adapts the Node.js request objects to a format that can\n * be used by web platform APIs.\n *\n * @param nodeRequest - The Node.js request object (`IncomingMessage` or `Http2ServerRequest`) to convert.\n * @returns A Web Standard `Request` object.\n * @developerPreview\n */\nfunction createWebRequestFromNodeRequest(nodeRequest) {\n  const {\n    headers,\n    method = 'GET'\n  } = nodeRequest;\n  const withBody = method !== 'GET' && method !== 'HEAD';\n  return new Request(createRequestUrl(nodeRequest), {\n    method,\n    headers: createRequestHeaders(headers),\n    body: withBody ? nodeRequest : undefined,\n    duplex: withBody ? 'half' : undefined\n  });\n}\n/**\n * Creates a `Headers` object from Node.js `IncomingHttpHeaders`.\n *\n * @param nodeHeaders - The Node.js `IncomingHttpHeaders` object to convert.\n * @returns A `Headers` object containing the converted headers.\n */\nfunction createRequestHeaders(nodeHeaders) {\n  const headers = new Headers();\n  for (const [name, value] of Object.entries(nodeHeaders)) {\n    if (HTTP2_PSEUDO_HEADERS.has(name)) {\n      continue;\n    }\n    if (typeof value === 'string') {\n      headers.append(name, value);\n    } else if (Array.isArray(value)) {\n      for (const item of value) {\n        headers.append(name, item);\n      }\n    }\n  }\n  return headers;\n}\n/**\n * Creates a `URL` object from a Node.js `IncomingMessage`, taking into account the protocol, host, and port.\n *\n * @param nodeRequest - The Node.js `IncomingMessage` or `Http2ServerRequest` object to extract URL information from.\n * @returns A `URL` object representing the request URL.\n */\nfunction createRequestUrl(nodeRequest) {\n  const {\n    headers,\n    socket,\n    url = '',\n    originalUrl\n  } = nodeRequest;\n  const protocol = getFirstHeaderValue(headers['x-forwarded-proto']) ?? ('encrypted' in socket && socket.encrypted ? 'https' : 'http');\n  const hostname = getFirstHeaderValue(headers['x-forwarded-host']) ?? headers.host ?? headers[':authority'];\n  if (Array.isArray(hostname)) {\n    throw new Error('host value cannot be an array.');\n  }\n  let hostnameWithPort = hostname;\n  if (!hostname?.includes(':')) {\n    const port = getFirstHeaderValue(headers['x-forwarded-port']);\n    if (port) {\n      hostnameWithPort += `:${port}`;\n    }\n  }\n  return new URL(originalUrl ?? url, `${protocol}://${hostnameWithPort}`);\n}\n/**\n * Extracts the first value from a multi-value header string.\n *\n * @param value - A string or an array of strings representing the header values.\n *                           If it's a string, values are expected to be comma-separated.\n * @returns The first trimmed value from the multi-value header, or `undefined` if the input is invalid or empty.\n *\n * @example\n * ```typescript\n * getFirstHeaderValue(\"value1, value2, value3\"); // \"value1\"\n * getFirstHeaderValue([\"value1\", \"value2\"]); // \"value1\"\n * getFirstHeaderValue(undefined); // undefined\n * ```\n */\nfunction getFirstHeaderValue(value) {\n  return value?.toString().split(',', 1)[0]?.trim();\n}\n\n/**\n * Angular server application engine.\n * Manages Angular server applications (including localized ones), handles rendering requests,\n * and optionally transforms index HTML before rendering.\n *\n * @remarks This class should be instantiated once and used as a singleton across the server-side\n * application to ensure consistent handling of rendering requests and resource management.\n *\n * @developerPreview\n */\nclass AngularNodeAppEngine {\n  angularAppEngine = new AngularAppEngine();\n  /**\n   * Handles an incoming HTTP request by serving prerendered content, performing server-side rendering,\n   * or delivering a static file for client-side rendered routes based on the `RenderMode` setting.\n   *\n   * This method adapts Node.js's `IncomingMessage` or `Http2ServerRequest`\n   * to a format compatible with the `AngularAppEngine` and delegates the handling logic to it.\n   *\n   * @param request - The incoming HTTP request (`IncomingMessage` or `Http2ServerRequest`).\n   * @param requestContext - Optional context for rendering, such as metadata associated with the request.\n   * @returns A promise that resolves to the resulting HTTP response object, or `null` if no matching Angular route is found.\n   *\n   * @remarks A request to `https://www.example.com/page/index.html` will serve or render the Angular route\n   * corresponding to `https://www.example.com/page`.\n   */\n  async handle(request, requestContext) {\n    const webRequest = createWebRequestFromNodeRequest(request);\n    return this.angularAppEngine.handle(webRequest, requestContext);\n  }\n}\n\n/**\n * Attaches metadata to the handler function to mark it as a special handler for Node.js environments.\n *\n * @typeParam T - The type of the handler function.\n * @param handler - The handler function to be defined and annotated.\n * @returns The same handler function passed as an argument, with metadata attached.\n *\n * @example\n * Usage in an Express application:\n * ```ts\n * const app = express();\n * export default createNodeRequestHandler(app);\n * ```\n *\n * @example\n * Usage in a Hono application:\n * ```ts\n * const app = new Hono();\n * export default createNodeRequestHandler(async (req, res, next) => {\n *   try {\n *     const webRes = await app.fetch(createWebRequestFromNodeRequest(req));\n *     if (webRes) {\n *       await writeResponseToNodeResponse(webRes, res);\n *     } else {\n *       next();\n *     }\n *   } catch (error) {\n *     next(error);\n *   }\n * }));\n * ```\n *\n * @example\n * Usage in a Fastify application:\n * ```ts\n * const app = Fastify();\n * export default createNodeRequestHandler(async (req, res) => {\n *   await app.ready();\n *   app.server.emit('request', req, res);\n *   res.send('Hello from Fastify with Node Next Handler!');\n * }));\n * ```\n * @developerPreview\n */\nfunction createNodeRequestHandler(handler) {\n  handler['__ng_node_request_handler__'] = true;\n  return handler;\n}\n\n/**\n * Streams a web-standard `Response` into a Node.js `ServerResponse`\n * or `Http2ServerResponse`.\n *\n * This function adapts the web `Response` object to write its content\n * to a Node.js response object, handling both HTTP/1.1 and HTTP/2.\n *\n * @param source - The web-standard `Response` object to stream from.\n * @param destination - The Node.js response object (`ServerResponse` or `Http2ServerResponse`) to stream into.\n * @returns A promise that resolves once the streaming operation is complete.\n * @developerPreview\n */\nasync function writeResponseToNodeResponse(source, destination) {\n  const {\n    status,\n    headers,\n    body\n  } = source;\n  destination.statusCode = status;\n  let cookieHeaderSet = false;\n  for (const [name, value] of headers.entries()) {\n    if (name === 'set-cookie') {\n      if (cookieHeaderSet) {\n        continue;\n      }\n      // Sets the 'set-cookie' header only once to ensure it is correctly applied.\n      // Concatenating 'set-cookie' values can lead to incorrect behavior, so we use a single value from `headers.getSetCookie()`.\n      destination.setHeader(name, headers.getSetCookie());\n      cookieHeaderSet = true;\n    } else {\n      destination.setHeader(name, value);\n    }\n  }\n  if (!body) {\n    destination.end();\n    return;\n  }\n  try {\n    const reader = body.getReader();\n    destination.on('close', () => {\n      reader.cancel().catch(error => {\n        // eslint-disable-next-line no-console\n        console.error(`An error occurred while writing the response body for: ${destination.req.url}.`, error);\n      });\n    });\n    // eslint-disable-next-line no-constant-condition\n    while (true) {\n      const {\n        done,\n        value\n      } = await reader.read();\n      if (done) {\n        destination.end();\n        break;\n      }\n      const canContinue = destination.write(value);\n      if (canContinue === false) {\n        // Explicitly check for `false`, as AWS may return `undefined` even though this is not valid.\n        // See: https://github.com/CodeGenieApp/serverless-express/issues/683\n        await new Promise(resolve => destination.once('drain', resolve));\n      }\n    }\n  } catch {\n    destination.end('Internal server error.');\n  }\n}\n\n/**\n * Determines whether the provided URL represents the main entry point module.\n *\n * This function checks if the provided URL corresponds to the main ESM module being executed directly.\n * It's useful for conditionally executing code that should only run when a module is the entry point,\n * such as starting a server or initializing an application.\n *\n * It performs two key checks:\n * 1. Verifies if the URL starts with 'file:', ensuring it is a local file.\n * 2. Compares the URL's resolved file path with the first command-line argument (`process.argv[1]`),\n *    which points to the file being executed.\n *\n * @param url The URL of the module to check. This should typically be `import.meta.url`.\n * @returns `true` if the provided URL represents the main entry point, otherwise `false`.\n * @developerPreview\n */\nfunction isMainModule(url) {\n  return url.startsWith('file:') && argv[1] === fileURLToPath(url);\n}\nexport { AngularNodeAppEngine, CommonEngine, createNodeRequestHandler, createWebRequestFromNodeRequest, isMainModule, writeResponseToNodeResponse };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AACA,YAAY,QAAQ;AACpB,SAAS,SAAS,MAAM,WAAW,eAAe;AAClD,SAAS,OAAO,OAAO,qBAAqB;AAE5C,SAAS,gBAAgB;AACzB,SAAS,YAAY;AACrB,IAAM,yCAAN,MAA6C;AAAA,EAC3C,gBAAgB,oBAAI,IAAI;AAAA,EAClB,QAAQ,MAAM,YAAY;AAAA;AAC9B,YAAM,WAAW,IAAI,2BAA4B,CAAM,SAAQ;AAC7D,YAAI,kBAAkB,KAAK,cAAc,IAAI,IAAI;AACjD,YAAI,oBAAoB,QAAW;AACjC,4BAAkB,MAAM,SAAS,MAAM,OAAO;AAC9C,eAAK,cAAc,IAAI,MAAM,eAAe;AAAA,QAC9C;AACA,eAAO;AAAA,MACT,IAAG,UAAU;AACb,aAAO,SAAS,QAAQ,IAAI;AAAA,IAC9B;AAAA;AACF;AACA,IAAM,0BAA0B;AAChC,SAAS,uBAAuB;AAC9B,MAAI,gBAAgB;AACpB,QAAM,aAAa,CAAC;AACpB,aAAW;AAAA,IACT;AAAA,IACA;AAAA,EACF,KAAK,YAAY,iBAAiB,SAAS,GAAG;AAC5C,QAAI,CAAC,KAAK,WAAW,uBAAuB,GAAG;AAC7C;AAAA,IACF;AAEA,UAAM,OAAO,KAAK,MAAM,wBAAwB,SAAS,CAAC,IAAI;AAC9D,QAAI,KAAK,SAAS,eAAe;AAC/B,sBAAgB,KAAK;AAAA,IACvB;AACA,eAAW,KAAK,CAAC,MAAM,GAAG,SAAS,QAAQ,CAAC,CAAC,IAAI,CAAC;AAClD,gBAAY,cAAc,IAAI;AAAA,EAChC;AAEA,UAAQ,IAAI,2CAA2C;AACvD,aAAW,CAAC,MAAM,KAAK,KAAK,YAAY;AACtC,UAAM,SAAS,gBAAgB,KAAK,SAAS;AAC7C,YAAQ,IAAI,OAAO,IAAI,OAAO,MAAM,IAAI,KAAK;AAAA,EAC/C;AACA,UAAQ,IAAI,2CAA2C;AAEzD;AACA,SAAe,wBAAwB,OAAO,aAAa;AAAA;AACzD,UAAM,YAAY,GAAG,uBAAuB,IAAI,KAAK;AACrD,UAAM,aAAa,SAAS,SAAS;AACrC,UAAM,WAAW,OAAO,SAAS;AACjC,QAAI;AACF,kBAAY,KAAK,UAAU;AAC3B,aAAO,MAAM,YAAY;AAAA,IAC3B,UAAE;AACA,kBAAY,KAAK,QAAQ;AACzB,kBAAY,QAAQ,WAAW,YAAY,QAAQ;AACnD,kBAAY,WAAW,UAAU;AACjC,kBAAY,WAAW,QAAQ;AAAA,IACjC;AAAA,EACF;AAAA;AACA,SAAS,4BAA4B,OAAO,aAAa;AACvD,SAAO,YAAY;AACrB;AACA,IAAM,oBAAoB;AAI1B,IAAM,eAAN,MAAmB;AAAA,EACjB;AAAA,EACA,gBAAgB,oBAAI,IAAI;AAAA,EACxB,6BAA6B,IAAI,uCAAuC;AAAA,EACxE,YAAY,oBAAI,IAAI;AAAA,EACpB,YAAY,SAAS;AACnB,SAAK,UAAU;AAAA,EACjB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKM,OAAO,MAAM;AAAA;AACjB,YAAM,4BAA4B,KAAK,SAAS;AAChD,YAAM,YAAY,4BAA4B,0BAA0B;AACxE,UAAI,OAAO,MAAM,UAAU,qBAAqB,MAAM,KAAK,gBAAgB,IAAI,CAAC;AAChF,UAAI,SAAS,QAAW;AACtB,eAAO,MAAM,UAAU,eAAe,MAAM,KAAK,kBAAkB,IAAI,CAAC;AACxE,YAAI,KAAK,sBAAsB,OAAO;AACpC,gBAAM,UAAU,MAAM,UAAU,uBAAuB;AAAA;AAAA,YAEvD,KAAK,kBAAkB,MAAM,IAAI;AAAA,WAAC;AAClC,iBAAO;AAAA,QACT;AAAA,MACF;AACA,UAAI,2BAA2B;AAC7B,6BAAqB;AAAA,MACvB;AACA,aAAO;AAAA,IACT;AAAA;AAAA,EACA,kBAAkB,MAAM,MAAM;AAC5B,UAAM,aAAa,KAAK,eAAe,KAAK,mBAAmB,QAAQ,KAAK,gBAAgB,IAAI;AAChG,WAAO,KAAK,2BAA2B,QAAQ,MAAM,UAAU;AAAA,EACjE;AAAA,EACM,gBAAgB,MAAM;AAAA;AAC1B,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI;AACJ,UAAI,CAAC,cAAc,CAAC,oBAAoB,QAAQ,QAAW;AACzD,eAAO;AAAA,MACT;AACA,YAAM;AAAA,QACJ;AAAA,MACF,IAAI,IAAI,MAAM,KAAK,YAAY;AAG/B,YAAM,WAAW,KAAK,YAAY,UAAU,YAAY;AACxD,UAAI,KAAK,UAAU,IAAI,QAAQ,GAAG;AAEhC,eAAU,YAAS,SAAS,UAAU,OAAO;AAAA,MAC/C;AACA,UAAI,CAAC,SAAS,WAAW,UAAU,UAAU,CAAC,GAAG;AAE/C,eAAO;AAAA,MACT;AACA,UAAI,aAAa,QAAQ,gBAAgB,KAAK,EAAE,MAAM,OAAO,QAAQ,IAAI;AAEvE,aAAK,UAAU,IAAI,UAAU,KAAK;AAClC,eAAO;AAAA,MACT;AAEA,YAAM,UAAU,MAAS,YAAS,SAAS,UAAU,OAAO;AAC5D,YAAM,QAAQ,kBAAkB,KAAK,OAAO;AAC5C,WAAK,UAAU,IAAI,UAAU,KAAK;AAClC,aAAO,QAAQ,UAAU;AAAA,IAC3B;AAAA;AAAA,EACM,kBAAkB,MAAM;AAAA;AAC5B,YAAM,kBAAkB,KAAK,SAAS,aAAa,KAAK;AACxD,UAAI,CAAC,iBAAiB;AACpB,cAAM,IAAI,MAAM,gDAAgD;AAAA,MAClE;AACA,YAAM,iBAAiB,CAAC;AAAA,QACtB,SAAS;AAAA,QACT,UAAU;AAAA,MACZ,GAAG,GAAI,KAAK,aAAa,CAAC,GAAI,GAAI,KAAK,SAAS,aAAa,CAAC,CAAE;AAChE,UAAI,WAAW,KAAK;AACpB,UAAI,CAAC,YAAY,KAAK,kBAAkB;AACtC,mBAAW,MAAM,KAAK,YAAY,KAAK,gBAAgB;AAAA,MACzD;AACA,YAAM,yBAAyB;AAAA,QAC7B,KAAK,KAAK;AAAA,QACV;AAAA,MACF;AACA,aAAO,cAAc,eAAe,IAAI,kBAAkB,iBAAiB;AAAA,QACzE,mBAAmB;AAAA,SAChB,uBACJ,IAAI,aAAa,iBAAiB;AAAA,QACjC;AAAA,SACG,uBACJ;AAAA,IACH;AAAA;AAAA;AAAA,EAEM,YAAY,UAAU;AAAA;AAC1B,UAAI,MAAM,KAAK,cAAc,IAAI,QAAQ;AACzC,UAAI,CAAC,KAAK;AACR,cAAM,MAAS,YAAS,SAAS,UAAU,OAAO;AAClD,aAAK,cAAc,IAAI,UAAU,GAAG;AAAA,MACtC;AACA,aAAO;AAAA,IACT;AAAA;AACF;AACA,SAAe,OAAO,MAAM;AAAA;AAC1B,QAAI;AACF,YAAS,YAAS,OAAO,MAAS,aAAU,IAAI;AAChD,aAAO;AAAA,IACT,QAAQ;AACN,aAAO;AAAA,IACT;AAAA,EACF;AAAA;AACA,SAAS,cAAc,OAAO;AAE5B,SAAO,OAAO,UAAU,cAAc,EAAE,UAAU;AACpD;AASA,IAAM,uBAAuB,oBAAI,IAAI,CAAC,WAAW,WAAW,cAAc,SAAS,SAAS,CAAC;AAY7F,SAAS,gCAAgC,aAAa;AACpD,QAAM;AAAA,IACJ;AAAA,IACA,SAAS;AAAA,EACX,IAAI;AACJ,QAAM,WAAW,WAAW,SAAS,WAAW;AAChD,SAAO,IAAI,QAAQ,iBAAiB,WAAW,GAAG;AAAA,IAChD;AAAA,IACA,SAAS,qBAAqB,OAAO;AAAA,IACrC,MAAM,WAAW,cAAc;AAAA,IAC/B,QAAQ,WAAW,SAAS;AAAA,EAC9B,CAAC;AACH;AAOA,SAAS,qBAAqB,aAAa;AACzC,QAAM,UAAU,IAAI,QAAQ;AAC5B,aAAW,CAAC,MAAM,KAAK,KAAK,OAAO,QAAQ,WAAW,GAAG;AACvD,QAAI,qBAAqB,IAAI,IAAI,GAAG;AAClC;AAAA,IACF;AACA,QAAI,OAAO,UAAU,UAAU;AAC7B,cAAQ,OAAO,MAAM,KAAK;AAAA,IAC5B,WAAW,MAAM,QAAQ,KAAK,GAAG;AAC/B,iBAAW,QAAQ,OAAO;AACxB,gBAAQ,OAAO,MAAM,IAAI;AAAA,MAC3B;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AAOA,SAAS,iBAAiB,aAAa;AACrC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA,MAAM;AAAA,IACN;AAAA,EACF,IAAI;AACJ,QAAM,WAAW,oBAAoB,QAAQ,mBAAmB,CAAC,MAAM,eAAe,UAAU,OAAO,YAAY,UAAU;AAC7H,QAAM,WAAW,oBAAoB,QAAQ,kBAAkB,CAAC,KAAK,QAAQ,QAAQ,QAAQ,YAAY;AACzG,MAAI,MAAM,QAAQ,QAAQ,GAAG;AAC3B,UAAM,IAAI,MAAM,gCAAgC;AAAA,EAClD;AACA,MAAI,mBAAmB;AACvB,MAAI,CAAC,UAAU,SAAS,GAAG,GAAG;AAC5B,UAAM,OAAO,oBAAoB,QAAQ,kBAAkB,CAAC;AAC5D,QAAI,MAAM;AACR,0BAAoB,IAAI,IAAI;AAAA,IAC9B;AAAA,EACF;AACA,SAAO,IAAI,IAAI,eAAe,KAAK,GAAG,QAAQ,MAAM,gBAAgB,EAAE;AACxE;AAeA,SAAS,oBAAoB,OAAO;AAClC,SAAO,OAAO,SAAS,EAAE,MAAM,KAAK,CAAC,EAAE,CAAC,GAAG,KAAK;AAClD;AAYA,IAAM,uBAAN,MAA2B;AAAA,EACzB,mBAAmB,IAAI,iBAAiB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAelC,OAAO,SAAS,gBAAgB;AAAA;AACpC,YAAM,aAAa,gCAAgC,OAAO;AAC1D,aAAO,KAAK,iBAAiB,OAAO,YAAY,cAAc;AAAA,IAChE;AAAA;AACF;AA8CA,SAAS,yBAAyB,SAAS;AACzC,UAAQ,6BAA6B,IAAI;AACzC,SAAO;AACT;AAcA,SAAe,4BAA4B,QAAQ,aAAa;AAAA;AAC9D,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,gBAAY,aAAa;AACzB,QAAI,kBAAkB;AACtB,eAAW,CAAC,MAAM,KAAK,KAAK,QAAQ,QAAQ,GAAG;AAC7C,UAAI,SAAS,cAAc;AACzB,YAAI,iBAAiB;AACnB;AAAA,QACF;AAGA,oBAAY,UAAU,MAAM,QAAQ,aAAa,CAAC;AAClD,0BAAkB;AAAA,MACpB,OAAO;AACL,oBAAY,UAAU,MAAM,KAAK;AAAA,MACnC;AAAA,IACF;AACA,QAAI,CAAC,MAAM;AACT,kBAAY,IAAI;AAChB;AAAA,IACF;AACA,QAAI;AACF,YAAM,SAAS,KAAK,UAAU;AAC9B,kBAAY,GAAG,SAAS,MAAM;AAC5B,eAAO,OAAO,EAAE,MAAM,WAAS;AAE7B,kBAAQ,MAAM,0DAA0D,YAAY,IAAI,GAAG,KAAK,KAAK;AAAA,QACvG,CAAC;AAAA,MACH,CAAC;AAED,aAAO,MAAM;AACX,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,QACF,IAAI,MAAM,OAAO,KAAK;AACtB,YAAI,MAAM;AACR,sBAAY,IAAI;AAChB;AAAA,QACF;AACA,cAAM,cAAc,YAAY,MAAM,KAAK;AAC3C,YAAI,gBAAgB,OAAO;AAGzB,gBAAM,IAAI,QAAQ,CAAAA,aAAW,YAAY,KAAK,SAASA,QAAO,CAAC;AAAA,QACjE;AAAA,MACF;AAAA,IACF,QAAQ;AACN,kBAAY,IAAI,wBAAwB;AAAA,IAC1C;AAAA,EACF;AAAA;AAkBA,SAAS,aAAa,KAAK;AACzB,SAAO,IAAI,WAAW,OAAO,KAAK,KAAK,CAAC,MAAM,cAAc,GAAG;AACjE;", "names": ["resolve"]}