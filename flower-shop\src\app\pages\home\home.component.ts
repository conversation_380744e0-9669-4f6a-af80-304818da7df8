import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatChipsModule } from '@angular/material/chips';
import { MatIconModule } from '@angular/material/icon';
import { MatBadgeModule } from '@angular/material/badge';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { Flower } from '../../models';
import { FlowerService } from '../../services/flower.service';
import { CartService } from '../../services/cart.service';
import { AuthService } from '../../services/auth.service';

@Component({
    selector: 'app-home',
    imports: [
        CommonModule,
        FormsModule,
        MatCardModule,
        MatButtonModule,
        MatFormFieldModule,
        MatInputModule,
        MatSelectModule,
        MatChipsModule,
        MatIconModule,
        MatBadgeModule,
        MatSnackBarModule
    ],
    templateUrl: './home.component.html',
    styleUrl: './home.component.scss'
})
export class HomeComponent implements OnInit {
    flowers$: Observable<Flower[]>;
    categories$: Observable<string[]>;
    searchTerm = '';
    selectedCategory = '';
    minPrice = 0;
    maxPrice = 100;
    isLoggedIn = false;

    constructor(
        private flowerService: FlowerService,
        private cartService: CartService,
        private authService: AuthService,
        private snackBar: MatSnackBar
    ) {
        this.flowers$ = this.flowerService.getFlowers();
        this.categories$ = this.flowerService.getCategories();
    }

    ngOnInit(): void {
        this.authService.currentUser$.subscribe(user => {
            this.isLoggedIn = !!user;
        });
        this.applyFilters();
    }

    applyFilters(): void {
        this.flowers$ = this.flowerService.getFlowers().pipe(
            map(flowers => {
                let filteredFlowers = flowers;

                // Apply search filter
                if (this.searchTerm) {
                    filteredFlowers = filteredFlowers.filter(flower =>
                        flower.name.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
                        flower.description.toLowerCase().includes(this.searchTerm.toLowerCase())
                    );
                }

                // Apply category filter
                if (this.selectedCategory) {
                    filteredFlowers = filteredFlowers.filter(flower =>
                        flower.category === this.selectedCategory
                    );
                }

                // Apply price filter
                filteredFlowers = filteredFlowers.filter(flower =>
                    flower.price >= this.minPrice && flower.price <= this.maxPrice
                );

                return filteredFlowers;
            })
        );
    }

    onSearchChange(): void {
        this.applyFilters();
    }

    onCategoryChange(): void {
        this.applyFilters();
    }

    onPriceChange(): void {
        this.applyFilters();
    }

    clearFilters(): void {
        this.searchTerm = '';
        this.selectedCategory = '';
        this.minPrice = 0;
        this.maxPrice = 100;
        this.applyFilters();
    }

    addToCart(flower: Flower): void {
        if (!this.isLoggedIn) {
            this.snackBar.open('Please login to add items to cart', 'Close', { duration: 3000 });
            return;
        }

        this.cartService.addToCart(flower, 1);
        this.snackBar.open(`${flower.name} added to cart!`, 'Close', { duration: 2000 });
    }

    isInCart(flower: Flower): Observable<boolean> {
        return this.cartService.isInCart(flower.id);
    }

    getItemQuantity(flower: Flower): Observable<number> {
        return this.cartService.getItemQuantity(flower.id);
    }
}
