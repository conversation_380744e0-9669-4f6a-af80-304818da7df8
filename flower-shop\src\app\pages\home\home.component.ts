import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatChipsModule } from '@angular/material/chips';
import { MatIconModule } from '@angular/material/icon';
import { MatBadgeModule } from '@angular/material/badge';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { Observable, combineLatest } from 'rxjs';
import { map, startWith } from 'rxjs/operators';
import { Flower } from '../../models';
import { FlowerService } from '../../services/flower.service';
import { CartService } from '../../services/cart.service';

@Component({
    selector: 'app-home',
    imports: [
        CommonModule,
        FormsModule,
        MatCardModule,
        MatButtonModule,
        MatFormFieldModule,
        MatInputModule,
        MatSelectModule,
        MatChipsModule,
        MatIconModule,
        MatBadgeModule,
        MatSnackBarModule
    ],
    templateUrl: './home.component.html',
    styleUrl: './home.component.scss'
})
export class HomeComponent implements OnInit {
    flowers$: Observable<Flower[]>;
    categories$: Observable<string[]>;
    searchTerm = '';
    selectedCategory = '';
    minPrice = 0;
    maxPrice = 100;

    constructor(
        private flowerService: FlowerService,
        private cartService: CartService,
        private snackBar: MatSnackBar
    ) {
        this.flowers$ = this.flowerService.getFlowers();
        this.categories$ = this.flowerService.getCategories();
    }

    ngOnInit(): void {
        this.applyFilters();
    }

    applyFilters(): void {
        this.flowers$ = combineLatest([
            this.flowerService.getFlowers(),
            // Create observables for filter changes
            new Observable(observer => {
                observer.next({ searchTerm: this.searchTerm, category: this.selectedCategory, minPrice: this.minPrice, maxPrice: this.maxPrice });
            }).pipe(startWith({ searchTerm: this.searchTerm, category: this.selectedCategory, minPrice: this.minPrice, maxPrice: this.maxPrice }))
        ]).pipe(
            map(([flowers, filters]) => {
                let filteredFlowers = flowers;

                // Apply search filter
                if (filters.searchTerm) {
                    filteredFlowers = filteredFlowers.filter(flower =>
                        flower.name.toLowerCase().includes(filters.searchTerm.toLowerCase()) ||
                        flower.description.toLowerCase().includes(filters.searchTerm.toLowerCase())
                    );
                }

                // Apply category filter
                if (filters.category) {
                    filteredFlowers = filteredFlowers.filter(flower =>
                        flower.category === filters.category
                    );
                }

                // Apply price filter
                filteredFlowers = filteredFlowers.filter(flower =>
                    flower.price >= filters.minPrice && flower.price <= filters.maxPrice
                );

                return filteredFlowers;
            })
        );
    }

    onSearchChange(): void {
        this.applyFilters();
    }

    onCategoryChange(): void {
        this.applyFilters();
    }

    onPriceChange(): void {
        this.applyFilters();
    }

    clearFilters(): void {
        this.searchTerm = '';
        this.selectedCategory = '';
        this.minPrice = 0;
        this.maxPrice = 100;
        this.applyFilters();
    }

    addToCart(flower: Flower): void {
        this.cartService.addToCart(flower, 1);
        this.snackBar.open(`${flower.name} added to cart!`, 'Close', { duration: 2000 });
    }

    isInCart(flower: Flower): Observable<boolean> {
        return this.cartService.isInCart(flower.id);
    }

    getItemQuantity(flower: Flower): Observable<number> {
        return this.cartService.getItemQuantity(flower.id);
    }
}
