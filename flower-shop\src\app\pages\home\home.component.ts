import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { FormsModule } from '@angular/forms';
import { Observable } from 'rxjs';
import { FlowerService } from '../../services/flower.service';
import { CartService } from '../../services/cart.service';
import { AuthService } from '../../services/auth.service';
import { Flower, User } from '../../models';

@Component({
    selector: 'app-home',
    imports: [
        CommonModule,
        MatCardModule,
        MatButtonModule,
        MatIconModule,
        MatInputModule,
        MatFormFieldModule,
        FormsModule
    ],
    templateUrl: './home.component.html',
    styleUrl: './home.component.scss'
})
export class HomeComponent implements OnInit {
    flowers$: Observable<Flower[]>;
    currentUser$: Observable<User | null>;
    searchQuery = '';
    selectedCategory = '';
    categories: string[] = [];

    constructor(
        private flowerService: FlowerService,
        private cartService: CartService,
        private authService: AuthService
    ) {
        this.flowers$ = this.flowerService.getAllFlowers();
        this.currentUser$ = this.authService.currentUser$;
    }

    ngOnInit(): void {
        this.loadCategories();
    }

    loadCategories(): void {
        this.flowerService.getCategories().subscribe(categories => {
            this.categories = categories;
        });
    }

    onSearch(): void {
        if (this.searchQuery.trim()) {
            this.flowers$ = this.flowerService.searchFlowers(this.searchQuery);
        } else {
            this.flowers$ = this.flowerService.getAllFlowers();
        }
    }

    onCategoryFilter(category: string): void {
        this.selectedCategory = category;
        if (category) {
            this.flowers$ = this.flowerService.getFlowersByCategory(category);
        } else {
            this.flowers$ = this.flowerService.getAllFlowers();
        }
    }

    addToCart(flower: Flower): void {
        this.cartService.addToCart(flower, 1);
        // You could add a toast notification here
    }

    clearFilters(): void {
        this.searchQuery = '';
        this.selectedCategory = '';
        this.flowers$ = this.flowerService.getAllFlowers();
    }
}
