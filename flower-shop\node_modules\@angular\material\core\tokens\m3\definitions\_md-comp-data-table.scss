//
// Design system display name: Material 3
// Design system version: v0.161
//

@use 'sass:map';

@use './md-sys-color';

@use './md-sys-shape';

@use './md-sys-state';

@use './md-sys-typescale';

$_default: (
  'md-sys-color': md-sys-color.values-light(),
  'md-sys-shape': md-sys-shape.values(),
  'md-sys-state': md-sys-state.values(),
  'md-sys-typescale': md-sys-typescale.values(),
);

@function values($deps: $_default, $exclude-hardcoded-values: false) {
  @return (
    'container-shape': map.get($deps, 'md-sys-shape', 'corner-extra-small'),
    'footer-container-color': map.get($deps, 'md-sys-color', 'surface'),
    'footer-container-height': if($exclude-hardcoded-values, null, 52px),
    'footer-supporting-text-color':
      map.get($deps, 'md-sys-color', 'on-surface-variant'),
    'footer-supporting-text-font':
      map.get($deps, 'md-sys-typescale', 'body-medium-font'),
    'footer-supporting-text-line-height':
      map.get($deps, 'md-sys-typescale', 'body-medium-line-height'),
    'footer-supporting-text-size':
      map.get($deps, 'md-sys-typescale', 'body-medium-size'),
    'footer-supporting-text-tracking':
      map.get($deps, 'md-sys-typescale', 'body-medium-tracking'),
    'footer-supporting-text-weight':
      map.get($deps, 'md-sys-typescale', 'body-medium-weight'),
    'header-container-color': map.get($deps, 'md-sys-color', 'surface'),
    'header-container-height': if($exclude-hardcoded-values, null, 56px),
    'header-headline-color':
      map.get($deps, 'md-sys-color', 'on-surface-variant'),
    'header-headline-font':
      map.get($deps, 'md-sys-typescale', 'title-small-font'),
    'header-headline-line-height':
      map.get($deps, 'md-sys-typescale', 'title-small-line-height'),
    'header-headline-size':
      map.get($deps, 'md-sys-typescale', 'title-small-size'),
    'header-headline-tracking':
      map.get($deps, 'md-sys-typescale', 'title-small-tracking'),
    'header-headline-weight':
      map.get($deps, 'md-sys-typescale', 'title-small-weight'),
    'header-hover-headline-color': map.get($deps, 'md-sys-color', 'on-surface'),
    'header-hover-sorting-icon-button-color':
      map.get($deps, 'md-sys-color', 'on-surface'),
    'outline-color': map.get($deps, 'md-sys-color', 'outline-variant'),
    'outline-width': if($exclude-hardcoded-values, null, 1px),
    'row-item-container-height': if($exclude-hardcoded-values, null, 52px),
    'row-item-disabled-label-text-color':
      map.get($deps, 'md-sys-color', 'on-surface'),
    'row-item-disabled-label-text-opacity':
      if($exclude-hardcoded-values, null, 0.38),
    'row-item-label-text-color': map.get($deps, 'md-sys-color', 'on-surface'),
    'row-item-label-text-font':
      map.get($deps, 'md-sys-typescale', 'body-medium-font'),
    'row-item-label-text-line-height':
      map.get($deps, 'md-sys-typescale', 'body-medium-line-height'),
    'row-item-label-text-size':
      map.get($deps, 'md-sys-typescale', 'body-medium-size'),
    'row-item-label-text-tracking':
      map.get($deps, 'md-sys-typescale', 'body-medium-tracking'),
    'row-item-label-text-weight':
      map.get($deps, 'md-sys-typescale', 'body-medium-weight'),
    'row-item-outline-color': map.get($deps, 'md-sys-color', 'outline-variant'),
    'row-item-outline-width': if($exclude-hardcoded-values, null, 1px),
    'row-item-selected-container-color':
      map.get($deps, 'md-sys-color', 'surface-variant'),
    'row-item-selected-hover-state-layer-color':
      map.get($deps, 'md-sys-color', 'on-surface'),
    'row-item-selected-hover-state-layer-opacity':
      map.get($deps, 'md-sys-state', 'hover-state-layer-opacity'),
    'row-item-unselected-container-color':
      map.get($deps, 'md-sys-color', 'surface'),
    'row-item-unselected-hover-state-layer-color':
      map.get($deps, 'md-sys-color', 'on-surface'),
    'row-item-unselected-hover-state-layer-opacity':
      map.get($deps, 'md-sys-state', 'hover-state-layer-opacity')
  );
}
