{"version": 3, "file": "testing.mjs", "sources": ["../../../../../k8-fastbuild-ST-46c76129e412/bin/src/cdk/testing/change-detection.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/cdk/testing/component-harness.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/cdk/testing/harness-environment.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/cdk/testing/test-element.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/cdk/testing/test-element-errors.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/cdk/testing/text-filtering.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {BehaviorSubject, Subscription} from 'rxjs';\n\n/**\n * The status of the test harness auto change detection. If not diabled test harnesses will\n * automatically trigger change detection after every action (such as a click) and before every read\n * (such as getting the text of an element).\n */\nexport interface AutoChangeDetectionStatus {\n  /** Whether auto change detection is disabled. */\n  isDisabled: boolean;\n  /**\n   * An optional callback, if present it indicates that change detection should be run immediately,\n   * while handling the status change. The callback should then be called as soon as change\n   * detection is done.\n   */\n  onDetectChangesNow?: () => void;\n}\n\n/** Subject used to dispatch and listen for changes to the auto change detection status . */\nconst autoChangeDetectionSubject = new BehaviorSubject<AutoChangeDetectionStatus>({\n  isDisabled: false,\n});\n\n/** The current subscription to `autoChangeDetectionSubject`. */\nlet autoChangeDetectionSubscription: Subscription | null;\n\n/**\n * The default handler for auto change detection status changes. This handler will be used if the\n * specific environment does not install its own.\n * @param status The new auto change detection status.\n */\nfunction defaultAutoChangeDetectionHandler(status: AutoChangeDetectionStatus) {\n  status.onDetectChangesNow?.();\n}\n\n/**\n * Allows a test `HarnessEnvironment` to install its own handler for auto change detection status\n * changes.\n * @param handler The handler for the auto change detection status.\n */\nexport function handleAutoChangeDetectionStatus(\n  handler: (status: AutoChangeDetectionStatus) => void,\n) {\n  stopHandlingAutoChangeDetectionStatus();\n  autoChangeDetectionSubscription = autoChangeDetectionSubject.subscribe(handler);\n}\n\n/** Allows a `HarnessEnvironment` to stop handling auto change detection status changes. */\nexport function stopHandlingAutoChangeDetectionStatus() {\n  autoChangeDetectionSubscription?.unsubscribe();\n  autoChangeDetectionSubscription = null;\n}\n\n/**\n * Batches together triggering of change detection over the duration of the given function.\n * @param fn The function to call with batched change detection.\n * @param triggerBeforeAndAfter Optionally trigger change detection once before and after the batch\n *   operation. If false, change detection will not be triggered.\n * @return The result of the given function.\n */\nasync function batchChangeDetection<T>(fn: () => Promise<T>, triggerBeforeAndAfter: boolean) {\n  // If change detection batching is already in progress, just run the function.\n  if (autoChangeDetectionSubject.getValue().isDisabled) {\n    return await fn();\n  }\n\n  // If nothing is handling change detection batching, install the default handler.\n  if (!autoChangeDetectionSubscription) {\n    handleAutoChangeDetectionStatus(defaultAutoChangeDetectionHandler);\n  }\n\n  if (triggerBeforeAndAfter) {\n    await new Promise(resolve =>\n      autoChangeDetectionSubject.next({\n        isDisabled: true,\n        onDetectChangesNow: resolve as () => void,\n      }),\n    );\n    // The function passed in may throw (e.g. if the user wants to make an expectation of an error\n    // being thrown. If this happens, we need to make sure we still re-enable change detection, so\n    // we wrap it in a `finally` block.\n    try {\n      return await fn();\n    } finally {\n      await new Promise(resolve =>\n        autoChangeDetectionSubject.next({\n          isDisabled: false,\n          onDetectChangesNow: resolve as () => void,\n        }),\n      );\n    }\n  } else {\n    autoChangeDetectionSubject.next({isDisabled: true});\n    // The function passed in may throw (e.g. if the user wants to make an expectation of an error\n    // being thrown. If this happens, we need to make sure we still re-enable change detection, so\n    // we wrap it in a `finally` block.\n    try {\n      return await fn();\n    } finally {\n      autoChangeDetectionSubject.next({isDisabled: false});\n    }\n  }\n}\n\n/**\n * Disables the harness system's auto change detection for the duration of the given function.\n * @param fn The function to disable auto change detection for.\n * @return The result of the given function.\n */\nexport async function manualChangeDetection<T>(fn: () => Promise<T>) {\n  return batchChangeDetection(fn, false);\n}\n\n/**\n * Resolves the given list of async values in parallel (i.e. via Promise.all) while batching change\n * detection over the entire operation such that change detection occurs exactly once before\n * resolving the values and once after.\n * @param values A getter for the async values to resolve in parallel with batched change detection.\n * @return The resolved values.\n */\nexport function parallel<T1, T2, T3, T4, T5>(\n  values: () => [\n    T1 | PromiseLike<T1>,\n    T2 | PromiseLike<T2>,\n    T3 | PromiseLike<T3>,\n    T4 | PromiseLike<T4>,\n    T5 | PromiseLike<T5>,\n  ],\n): Promise<[T1, T2, T3, T4, T5]>;\n\n/**\n * Resolves the given list of async values in parallel (i.e. via Promise.all) while batching change\n * detection over the entire operation such that change detection occurs exactly once before\n * resolving the values and once after.\n * @param values A getter for the async values to resolve in parallel with batched change detection.\n * @return The resolved values.\n */\nexport function parallel<T1, T2, T3, T4>(\n  values: () => [\n    T1 | PromiseLike<T1>,\n    T2 | PromiseLike<T2>,\n    T3 | PromiseLike<T3>,\n    T4 | PromiseLike<T4>,\n  ],\n): Promise<[T1, T2, T3, T4]>;\n\n/**\n * Resolves the given list of async values in parallel (i.e. via Promise.all) while batching change\n * detection over the entire operation such that change detection occurs exactly once before\n * resolving the values and once after.\n * @param values A getter for the async values to resolve in parallel with batched change detection.\n * @return The resolved values.\n */\nexport function parallel<T1, T2, T3>(\n  values: () => [T1 | PromiseLike<T1>, T2 | PromiseLike<T2>, T3 | PromiseLike<T3>],\n): Promise<[T1, T2, T3]>;\n\n/**\n * Resolves the given list of async values in parallel (i.e. via Promise.all) while batching change\n * detection over the entire operation such that change detection occurs exactly once before\n * resolving the values and once after.\n * @param values A getter for the async values to resolve in parallel with batched change detection.\n * @return The resolved values.\n */\nexport function parallel<T1, T2>(\n  values: () => [T1 | PromiseLike<T1>, T2 | PromiseLike<T2>],\n): Promise<[T1, T2]>;\n\n/**\n * Resolves the given list of async values in parallel (i.e. via Promise.all) while batching change\n * detection over the entire operation such that change detection occurs exactly once before\n * resolving the values and once after.\n * @param values A getter for the async values to resolve in parallel with batched change detection.\n * @return The resolved values.\n */\nexport function parallel<T>(values: () => (T | PromiseLike<T>)[]): Promise<T[]>;\n\n/**\n * Resolves the given list of async values in parallel (i.e. via Promise.all) while batching change\n * detection over the entire operation such that change detection occurs exactly once before\n * resolving the values and once after.\n * @param values A getter for the async values to resolve in parallel with batched change detection.\n * @return The resolved values.\n */\nexport async function parallel<T>(values: () => Iterable<T | PromiseLike<T>>): Promise<T[]> {\n  return batchChangeDetection(() => Promise.all(values()), true);\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {parallel} from './change-detection';\nimport {TestElement} from './test-element';\n\n/**\n * An async function that returns a promise when called.\n * @deprecated This was just an alias for `() => Promise<T>`. Use that instead.\n * @breaking-change 21.0.0 Remove this alias.\n * @docs-private\n */\nexport type AsyncFactoryFn<T> = () => Promise<T>;\n\n/** An async function that takes an item and returns a boolean promise */\nexport type AsyncPredicate<T> = (item: T) => Promise<boolean>;\n\n/** An async function that takes an item and an option value and returns a boolean promise. */\nexport type AsyncOptionPredicate<T, O> = (item: T, option: O) => Promise<boolean>;\n\n/**\n * A query for a `ComponentHarness`, which is expressed as either a `ComponentHarnessConstructor` or\n * a `HarnessPredicate`.\n */\nexport type HarnessQuery<T extends ComponentHarness> =\n  | ComponentHarnessConstructor<T>\n  | HarnessPredicate<T>;\n\n/**\n * The result type obtained when searching using a particular list of queries. This type depends on\n * the particular items being queried.\n * - If one of the queries is for a `ComponentHarnessConstructor<C1>`, it means that the result\n *   might be a harness of type `C1`\n * - If one of the queries is for a `HarnessPredicate<C2>`, it means that the result might be a\n *   harness of type `C2`\n * - If one of the queries is for a `string`, it means that the result might be a `TestElement`.\n *\n * Since we don't know for sure which query will match, the result type if the union of the types\n * for all possible results.\n *\n * @usageNotes\n * ### Example\n *\n * The type:\n * ```ts\n * LocatorFnResult<[\n *   ComponentHarnessConstructor<MyHarness>,\n *   HarnessPredicate<MyOtherHarness>,\n *   string\n * ]>\n * ```\n *\n * is equivalent to:\n *\n * ```ts\n * MyHarness | MyOtherHarness | TestElement\n * ```\n */\nexport type LocatorFnResult<T extends (HarnessQuery<any> | string)[]> = {\n  [I in keyof T]: T[I] extends new (...args: any[]) => infer C // Map `ComponentHarnessConstructor<C>` to `C`.\n    ? C\n    : // Map `HarnessPredicate<C>` to `C`.\n      T[I] extends {harnessType: new (...args: any[]) => infer C}\n      ? C\n      : // Map `string` to `TestElement`.\n        T[I] extends string\n        ? TestElement\n        : // Map everything else to `never` (should not happen due to the type constraint on `T`).\n          never;\n}[number];\n\n/**\n * Interface used to load ComponentHarness objects. This interface is used by test authors to\n * instantiate `ComponentHarness`es.\n */\nexport interface HarnessLoader {\n  /**\n   * Searches for an element with the given selector under the current instances's root element,\n   * and returns a `HarnessLoader` rooted at the matching element. If multiple elements match the\n   * selector, the first is used. If no elements match, an error is thrown.\n   * @param selector The selector for the root element of the new `HarnessLoader`\n   * @return A `HarnessLoader` rooted at the element matching the given selector.\n   * @throws If a matching element can't be found.\n   */\n  getChildLoader(selector: string): Promise<HarnessLoader>;\n\n  /**\n   * Searches for all elements with the given selector under the current instances's root element,\n   * and returns an array of `HarnessLoader`s, one for each matching element, rooted at that\n   * element.\n   * @param selector The selector for the root element of the new `HarnessLoader`\n   * @return A list of `HarnessLoader`s, one for each matching element, rooted at that element.\n   */\n  getAllChildLoaders(selector: string): Promise<HarnessLoader[]>;\n\n  /**\n   * Searches for an instance of the component corresponding to the given harness type under the\n   * `HarnessLoader`'s root element, and returns a `ComponentHarness` for that instance. If multiple\n   * matching components are found, a harness for the first one is returned. If no matching\n   * component is found, an error is thrown.\n   * @param query A query for a harness to create\n   * @return An instance of the given harness type\n   * @throws If a matching component instance can't be found.\n   */\n  getHarness<T extends ComponentHarness>(query: HarnessQuery<T>): Promise<T>;\n\n  /**\n   * Searches for an instance of the component corresponding to the given harness type under the\n   * `HarnessLoader`'s root element, and returns a `ComponentHarness` for that instance. If multiple\n   * matching components are found, a harness for the first one is returned. If no matching\n   * component is found, null is returned.\n   * @param query A query for a harness to create\n   * @return An instance of the given harness type (or null if not found).\n   */\n  getHarnessOrNull<T extends ComponentHarness>(query: HarnessQuery<T>): Promise<T | null>;\n\n  /**\n   * Searches for all instances of the component corresponding to the given harness type under the\n   * `HarnessLoader`'s root element, and returns a list `ComponentHarness` for each instance.\n   * @param query A query for a harness to create\n   * @return A list instances of the given harness type.\n   */\n  getAllHarnesses<T extends ComponentHarness>(query: HarnessQuery<T>): Promise<T[]>;\n\n  /**\n   * Searches for an instance of the component corresponding to the given harness type under the\n   * `HarnessLoader`'s root element, and returns a boolean indicating if any were found.\n   * @param query A query for a harness to create\n   * @return A boolean indicating if an instance was found.\n   */\n  hasHarness<T extends ComponentHarness>(query: HarnessQuery<T>): Promise<boolean>;\n}\n\n/**\n * Interface used to create asynchronous locator functions used find elements and component\n * harnesses. This interface is used by `ComponentHarness` authors to create locator functions for\n * their `ComponentHarness` subclass.\n */\nexport interface LocatorFactory {\n  /** Gets a locator factory rooted at the document root. */\n  documentRootLocatorFactory(): LocatorFactory;\n\n  /** The root element of this `LocatorFactory` as a `TestElement`. */\n  rootElement: TestElement;\n\n  /**\n   * Creates an asynchronous locator function that can be used to find a `ComponentHarness` instance\n   * or element under the root element of this `LocatorFactory`.\n   *\n   * For example, given the following DOM and assuming `DivHarness.hostSelector` is `'div'`\n   *\n   * ```html\n   * <div id=\"d1\"></div><div id=\"d2\"></div>\n   * ```\n   *\n   * then we expect:\n   *\n   * ```ts\n   * await lf.locatorFor(DivHarness, 'div')() // Gets a `DivHarness` instance for #d1\n   * await lf.locatorFor('div', DivHarness)() // Gets a `TestElement` instance for #d1\n   * await lf.locatorFor('span')()            // Throws because the `Promise` rejects\n   * ```\n   *\n   * @param queries A list of queries specifying which harnesses and elements to search for:\n   *   - A `string` searches for elements matching the CSS selector specified by the string.\n   *   - A `ComponentHarness` constructor searches for `ComponentHarness` instances matching the\n   *     given class.\n   *   - A `HarnessPredicate` searches for `ComponentHarness` instances matching the given\n   *     predicate.\n   * @return An asynchronous locator function that searches for and returns a `Promise` for the\n   *   first element or harness matching the given search criteria. Matches are ordered first by\n   *   order in the DOM, and second by order in the queries list. If no matches are found, the\n   *   `Promise` rejects. The type that the `Promise` resolves to is a union of all result types for\n   *   each query.\n   */\n  locatorFor<T extends (HarnessQuery<any> | string)[]>(\n    ...queries: T\n  ): () => Promise<LocatorFnResult<T>>;\n\n  /**\n   * Creates an asynchronous locator function that can be used to find a `ComponentHarness` instance\n   * or element under the root element of this `LocatorFactory`.\n   *\n   * For example, given the following DOM and assuming `DivHarness.hostSelector` is `'div'`\n   *\n   * ```html\n   * <div id=\"d1\"></div><div id=\"d2\"></div>\n   * ```\n   *\n   * then we expect:\n   *\n   * ```ts\n   * await lf.locatorForOptional(DivHarness, 'div')() // Gets a `DivHarness` instance for #d1\n   * await lf.locatorForOptional('div', DivHarness)() // Gets a `TestElement` instance for #d1\n   * await lf.locatorForOptional('span')()            // Gets `null`\n   * ```\n   *\n   * @param queries A list of queries specifying which harnesses and elements to search for:\n   *   - A `string` searches for elements matching the CSS selector specified by the string.\n   *   - A `ComponentHarness` constructor searches for `ComponentHarness` instances matching the\n   *     given class.\n   *   - A `HarnessPredicate` searches for `ComponentHarness` instances matching the given\n   *     predicate.\n   * @return An asynchronous locator function that searches for and returns a `Promise` for the\n   *   first element or harness matching the given search criteria. Matches are ordered first by\n   *   order in the DOM, and second by order in the queries list. If no matches are found, the\n   *   `Promise` is resolved with `null`. The type that the `Promise` resolves to is a union of all\n   *   result types for each query or null.\n   */\n  locatorForOptional<T extends (HarnessQuery<any> | string)[]>(\n    ...queries: T\n  ): () => Promise<LocatorFnResult<T> | null>;\n\n  /**\n   * Creates an asynchronous locator function that can be used to find `ComponentHarness` instances\n   * or elements under the root element of this `LocatorFactory`.\n   *\n   * For example, given the following DOM and assuming `DivHarness.hostSelector` is `'div'` and\n   * `IdIsD1Harness.hostSelector` is `'#d1'`\n   *\n   * ```html\n   * <div id=\"d1\"></div><div id=\"d2\"></div>\n   * ```\n   *\n   * then we expect:\n   *\n   * ```ts\n   * // Gets [DivHarness for #d1, TestElement for #d1, DivHarness for #d2, TestElement for #d2]\n   * await lf.locatorForAll(DivHarness, 'div')()\n   * // Gets [TestElement for #d1, TestElement for #d2]\n   * await lf.locatorForAll('div', '#d1')()\n   * // Gets [DivHarness for #d1, IdIsD1Harness for #d1, DivHarness for #d2]\n   * await lf.locatorForAll(DivHarness, IdIsD1Harness)()\n   * // Gets []\n   * await lf.locatorForAll('span')()\n   * ```\n   *\n   * @param queries A list of queries specifying which harnesses and elements to search for:\n   *   - A `string` searches for elements matching the CSS selector specified by the string.\n   *   - A `ComponentHarness` constructor searches for `ComponentHarness` instances matching the\n   *     given class.\n   *   - A `HarnessPredicate` searches for `ComponentHarness` instances matching the given\n   *     predicate.\n   * @return An asynchronous locator function that searches for and returns a `Promise` for all\n   *   elements and harnesses matching the given search criteria. Matches are ordered first by\n   *   order in the DOM, and second by order in the queries list. If an element matches more than\n   *   one `ComponentHarness` class, the locator gets an instance of each for the same element. If\n   *   an element matches multiple `string` selectors, only one `TestElement` instance is returned\n   *   for that element. The type that the `Promise` resolves to is an array where each element is\n   *   the union of all result types for each query.\n   */\n  locatorForAll<T extends (HarnessQuery<any> | string)[]>(\n    ...queries: T\n  ): () => Promise<LocatorFnResult<T>[]>;\n\n  /** @return A `HarnessLoader` rooted at the root element of this `LocatorFactory`. */\n  rootHarnessLoader(): Promise<HarnessLoader>;\n\n  /**\n   * Gets a `HarnessLoader` instance for an element under the root of this `LocatorFactory`.\n   * @param selector The selector for the root element.\n   * @return A `HarnessLoader` rooted at the first element matching the given selector.\n   * @throws If no matching element is found for the given selector.\n   */\n  harnessLoaderFor(selector: string): Promise<HarnessLoader>;\n\n  /**\n   * Gets a `HarnessLoader` instance for an element under the root of this `LocatorFactory`\n   * @param selector The selector for the root element.\n   * @return A `HarnessLoader` rooted at the first element matching the given selector, or null if\n   *     no matching element is found.\n   */\n  harnessLoaderForOptional(selector: string): Promise<HarnessLoader | null>;\n\n  /**\n   * Gets a list of `HarnessLoader` instances, one for each matching element.\n   * @param selector The selector for the root element.\n   * @return A list of `HarnessLoader`, one rooted at each element matching the given selector.\n   */\n  harnessLoaderForAll(selector: string): Promise<HarnessLoader[]>;\n\n  /**\n   * Flushes change detection and async tasks captured in the Angular zone.\n   * In most cases it should not be necessary to call this manually. However, there may be some edge\n   * cases where it is needed to fully flush animation events.\n   */\n  forceStabilize(): Promise<void>;\n\n  /**\n   * Waits for all scheduled or running async tasks to complete. This allows harness\n   * authors to wait for async tasks outside of the Angular zone.\n   */\n  waitForTasksOutsideAngular(): Promise<void>;\n}\n\n/**\n * Base class for component test harnesses that all component harness authors should extend. This\n * base component harness provides the basic ability to locate element and sub-component harnesses.\n */\nexport abstract class ComponentHarness {\n  constructor(protected readonly locatorFactory: LocatorFactory) {}\n\n  /** Gets a `Promise` for the `TestElement` representing the host element of the component. */\n  async host(): Promise<TestElement> {\n    return this.locatorFactory.rootElement;\n  }\n\n  /**\n   * Gets a `LocatorFactory` for the document root element. This factory can be used to create\n   * locators for elements that a component creates outside of its own root element. (e.g. by\n   * appending to document.body).\n   */\n  protected documentRootLocatorFactory(): LocatorFactory {\n    return this.locatorFactory.documentRootLocatorFactory();\n  }\n\n  /**\n   * Creates an asynchronous locator function that can be used to find a `ComponentHarness` instance\n   * or element under the host element of this `ComponentHarness`.\n   *\n   * For example, given the following DOM and assuming `DivHarness.hostSelector` is `'div'`\n   *\n   * ```html\n   * <div id=\"d1\"></div><div id=\"d2\"></div>\n   * ```\n   *\n   * then we expect:\n   *\n   * ```ts\n   * await ch.locatorFor(DivHarness, 'div')() // Gets a `DivHarness` instance for #d1\n   * await ch.locatorFor('div', DivHarness)() // Gets a `TestElement` instance for #d1\n   * await ch.locatorFor('span')()            // Throws because the `Promise` rejects\n   * ```\n   *\n   * @param queries A list of queries specifying which harnesses and elements to search for:\n   *   - A `string` searches for elements matching the CSS selector specified by the string.\n   *   - A `ComponentHarness` constructor searches for `ComponentHarness` instances matching the\n   *     given class.\n   *   - A `HarnessPredicate` searches for `ComponentHarness` instances matching the given\n   *     predicate.\n   * @return An asynchronous locator function that searches for and returns a `Promise` for the\n   *   first element or harness matching the given search criteria. Matches are ordered first by\n   *   order in the DOM, and second by order in the queries list. If no matches are found, the\n   *   `Promise` rejects. The type that the `Promise` resolves to is a union of all result types for\n   *   each query.\n   */\n  protected locatorFor<T extends (HarnessQuery<any> | string)[]>(\n    ...queries: T\n  ): () => Promise<LocatorFnResult<T>> {\n    return this.locatorFactory.locatorFor(...queries);\n  }\n\n  /**\n   * Creates an asynchronous locator function that can be used to find a `ComponentHarness` instance\n   * or element under the host element of this `ComponentHarness`.\n   *\n   * For example, given the following DOM and assuming `DivHarness.hostSelector` is `'div'`\n   *\n   * ```html\n   * <div id=\"d1\"></div><div id=\"d2\"></div>\n   * ```\n   *\n   * then we expect:\n   *\n   * ```ts\n   * await ch.locatorForOptional(DivHarness, 'div')() // Gets a `DivHarness` instance for #d1\n   * await ch.locatorForOptional('div', DivHarness)() // Gets a `TestElement` instance for #d1\n   * await ch.locatorForOptional('span')()            // Gets `null`\n   * ```\n   *\n   * @param queries A list of queries specifying which harnesses and elements to search for:\n   *   - A `string` searches for elements matching the CSS selector specified by the string.\n   *   - A `ComponentHarness` constructor searches for `ComponentHarness` instances matching the\n   *     given class.\n   *   - A `HarnessPredicate` searches for `ComponentHarness` instances matching the given\n   *     predicate.\n   * @return An asynchronous locator function that searches for and returns a `Promise` for the\n   *   first element or harness matching the given search criteria. Matches are ordered first by\n   *   order in the DOM, and second by order in the queries list. If no matches are found, the\n   *   `Promise` is resolved with `null`. The type that the `Promise` resolves to is a union of all\n   *   result types for each query or null.\n   */\n  protected locatorForOptional<T extends (HarnessQuery<any> | string)[]>(\n    ...queries: T\n  ): () => Promise<LocatorFnResult<T> | null> {\n    return this.locatorFactory.locatorForOptional(...queries);\n  }\n\n  /**\n   * Creates an asynchronous locator function that can be used to find `ComponentHarness` instances\n   * or elements under the host element of this `ComponentHarness`.\n   *\n   * For example, given the following DOM and assuming `DivHarness.hostSelector` is `'div'` and\n   * `IdIsD1Harness.hostSelector` is `'#d1'`\n   *\n   * ```html\n   * <div id=\"d1\"></div><div id=\"d2\"></div>\n   * ```\n   *\n   * then we expect:\n   *\n   * ```ts\n   * // Gets [DivHarness for #d1, TestElement for #d1, DivHarness for #d2, TestElement for #d2]\n   * await ch.locatorForAll(DivHarness, 'div')()\n   * // Gets [TestElement for #d1, TestElement for #d2]\n   * await ch.locatorForAll('div', '#d1')()\n   * // Gets [DivHarness for #d1, IdIsD1Harness for #d1, DivHarness for #d2]\n   * await ch.locatorForAll(DivHarness, IdIsD1Harness)()\n   * // Gets []\n   * await ch.locatorForAll('span')()\n   * ```\n   *\n   * @param queries A list of queries specifying which harnesses and elements to search for:\n   *   - A `string` searches for elements matching the CSS selector specified by the string.\n   *   - A `ComponentHarness` constructor searches for `ComponentHarness` instances matching the\n   *     given class.\n   *   - A `HarnessPredicate` searches for `ComponentHarness` instances matching the given\n   *     predicate.\n   * @return An asynchronous locator function that searches for and returns a `Promise` for all\n   *   elements and harnesses matching the given search criteria. Matches are ordered first by\n   *   order in the DOM, and second by order in the queries list. If an element matches more than\n   *   one `ComponentHarness` class, the locator gets an instance of each for the same element. If\n   *   an element matches multiple `string` selectors, only one `TestElement` instance is returned\n   *   for that element. The type that the `Promise` resolves to is an array where each element is\n   *   the union of all result types for each query.\n   */\n  protected locatorForAll<T extends (HarnessQuery<any> | string)[]>(\n    ...queries: T\n  ): () => Promise<LocatorFnResult<T>[]> {\n    return this.locatorFactory.locatorForAll(...queries);\n  }\n\n  /**\n   * Flushes change detection and async tasks in the Angular zone.\n   * In most cases it should not be necessary to call this manually. However, there may be some edge\n   * cases where it is needed to fully flush animation events.\n   */\n  protected async forceStabilize() {\n    return this.locatorFactory.forceStabilize();\n  }\n\n  /**\n   * Waits for all scheduled or running async tasks to complete. This allows harness\n   * authors to wait for async tasks outside of the Angular zone.\n   */\n  protected async waitForTasksOutsideAngular() {\n    return this.locatorFactory.waitForTasksOutsideAngular();\n  }\n}\n\n/**\n * Base class for component harnesses that authors should extend if they anticipate that consumers\n * of the harness may want to access other harnesses within the `<ng-content>` of the component.\n */\nexport abstract class ContentContainerComponentHarness<S extends string = string>\n  extends ComponentHarness\n  implements HarnessLoader\n{\n  /**\n   * Gets a `HarnessLoader` that searches for harnesses under the first element matching the given\n   * selector within the current harness's content.\n   * @param selector The selector for an element in the component's content.\n   * @returns A `HarnessLoader` that searches for harnesses under the given selector.\n   */\n  async getChildLoader(selector: S): Promise<HarnessLoader> {\n    return (await this.getRootHarnessLoader()).getChildLoader(selector);\n  }\n\n  /**\n   * Gets a list of `HarnessLoader` for each element matching the given selector under the current\n   * harness's cotnent that searches for harnesses under that element.\n   * @param selector The selector for elements in the component's content.\n   * @returns A list of `HarnessLoader` for each element matching the given selector.\n   */\n  async getAllChildLoaders(selector: S): Promise<HarnessLoader[]> {\n    return (await this.getRootHarnessLoader()).getAllChildLoaders(selector);\n  }\n\n  /**\n   * Gets the first matching harness for the given query within the current harness's content.\n   * @param query The harness query to search for.\n   * @returns The first harness matching the given query.\n   * @throws If no matching harness is found.\n   */\n  async getHarness<T extends ComponentHarness>(query: HarnessQuery<T>): Promise<T> {\n    return (await this.getRootHarnessLoader()).getHarness(query);\n  }\n\n  /**\n   * Gets the first matching harness for the given query within the current harness's content.\n   * @param query The harness query to search for.\n   * @returns The first harness matching the given query, or null if none is found.\n   */\n  async getHarnessOrNull<T extends ComponentHarness>(query: HarnessQuery<T>): Promise<T | null> {\n    return (await this.getRootHarnessLoader()).getHarnessOrNull(query);\n  }\n\n  /**\n   * Gets all matching harnesses for the given query within the current harness's content.\n   * @param query The harness query to search for.\n   * @returns The list of harness matching the given query.\n   */\n  async getAllHarnesses<T extends ComponentHarness>(query: HarnessQuery<T>): Promise<T[]> {\n    return (await this.getRootHarnessLoader()).getAllHarnesses(query);\n  }\n\n  /**\n   * Checks whether there is a matching harnesses for the given query within the current harness's\n   * content.\n   *\n   * @param query The harness query to search for.\n   * @returns Whetehr there is matching harnesses for the given query.\n   */\n  async hasHarness<T extends ComponentHarness>(query: HarnessQuery<T>): Promise<boolean> {\n    return (await this.getRootHarnessLoader()).hasHarness(query);\n  }\n\n  /**\n   * Gets the root harness loader from which to start\n   * searching for content contained by this harness.\n   */\n  protected async getRootHarnessLoader(): Promise<HarnessLoader> {\n    return this.locatorFactory.rootHarnessLoader();\n  }\n}\n\n/**\n * Constructor for a ComponentHarness subclass. To be a valid ComponentHarnessConstructor, the\n * class must also have a static `hostSelector` property.\n */\nexport interface ComponentHarnessConstructor<T extends ComponentHarness> {\n  new (locatorFactory: LocatorFactory): T;\n\n  /**\n   * `ComponentHarness` subclasses must specify a static `hostSelector` property that is used to\n   * find the host element for the corresponding component. This property should match the selector\n   * for the Angular component.\n   */\n  hostSelector: string;\n}\n\n/** A set of criteria that can be used to filter a list of `ComponentHarness` instances. */\nexport interface BaseHarnessFilters {\n  /** Only find instances whose host element matches the given selector. */\n  selector?: string;\n  /** Only find instances that are nested under an element with the given selector. */\n  ancestor?: string;\n}\n\n/**\n * A class used to associate a ComponentHarness class with predicate functions that can be used to\n * filter instances of the class to be matched.\n */\nexport class HarnessPredicate<T extends ComponentHarness> {\n  private _predicates: AsyncPredicate<T>[] = [];\n  private _descriptions: string[] = [];\n  private _ancestor: string;\n\n  constructor(\n    public harnessType: ComponentHarnessConstructor<T>,\n    options: BaseHarnessFilters,\n  ) {\n    this._addBaseOptions(options);\n  }\n\n  /**\n   * Checks if the specified nullable string value matches the given pattern.\n   * @param value The nullable string value to check, or a Promise resolving to the\n   *   nullable string value.\n   * @param pattern The pattern the value is expected to match. If `pattern` is a string,\n   *   `value` is expected to match exactly. If `pattern` is a regex, a partial match is\n   *   allowed. If `pattern` is `null`, the value is expected to be `null`.\n   * @return Whether the value matches the pattern.\n   */\n  static async stringMatches(\n    value: string | null | Promise<string | null>,\n    pattern: string | RegExp | null,\n  ): Promise<boolean> {\n    value = await value;\n    if (pattern === null) {\n      return value === null;\n    } else if (value === null) {\n      return false;\n    }\n    return typeof pattern === 'string' ? value === pattern : pattern.test(value);\n  }\n\n  /**\n   * Adds a predicate function to be run against candidate harnesses.\n   * @param description A description of this predicate that may be used in error messages.\n   * @param predicate An async predicate function.\n   * @return this (for method chaining).\n   */\n  add(description: string, predicate: AsyncPredicate<T>) {\n    this._descriptions.push(description);\n    this._predicates.push(predicate);\n    return this;\n  }\n\n  /**\n   * Adds a predicate function that depends on an option value to be run against candidate\n   * harnesses. If the option value is undefined, the predicate will be ignored.\n   * @param name The name of the option (may be used in error messages).\n   * @param option The option value.\n   * @param predicate The predicate function to run if the option value is not undefined.\n   * @return this (for method chaining).\n   */\n  addOption<O>(name: string, option: O | undefined, predicate: AsyncOptionPredicate<T, O>) {\n    if (option !== undefined) {\n      this.add(`${name} = ${_valueAsString(option)}`, item => predicate(item, option));\n    }\n    return this;\n  }\n\n  /**\n   * Filters a list of harnesses on this predicate.\n   * @param harnesses The list of harnesses to filter.\n   * @return A list of harnesses that satisfy this predicate.\n   */\n  async filter(harnesses: T[]): Promise<T[]> {\n    if (harnesses.length === 0) {\n      return [];\n    }\n    const results = await parallel(() => harnesses.map(h => this.evaluate(h)));\n    return harnesses.filter((_, i) => results[i]);\n  }\n\n  /**\n   * Evaluates whether the given harness satisfies this predicate.\n   * @param harness The harness to check\n   * @return A promise that resolves to true if the harness satisfies this predicate,\n   *   and resolves to false otherwise.\n   */\n  async evaluate(harness: T): Promise<boolean> {\n    const results = await parallel(() => this._predicates.map(p => p(harness)));\n    return results.reduce((combined, current) => combined && current, true);\n  }\n\n  /** Gets a description of this predicate for use in error messages. */\n  getDescription() {\n    return this._descriptions.join(', ');\n  }\n\n  /** Gets the selector used to find candidate elements. */\n  getSelector() {\n    // We don't have to go through the extra trouble if there are no ancestors.\n    if (!this._ancestor) {\n      return (this.harnessType.hostSelector || '').trim();\n    }\n\n    const [ancestors, ancestorPlaceholders] = _splitAndEscapeSelector(this._ancestor);\n    const [selectors, selectorPlaceholders] = _splitAndEscapeSelector(\n      this.harnessType.hostSelector || '',\n    );\n    const result: string[] = [];\n\n    // We have to add the ancestor to each part of the host compound selector, otherwise we can get\n    // incorrect results. E.g. `.ancestor .a, .ancestor .b` vs `.ancestor .a, .b`.\n    ancestors.forEach(escapedAncestor => {\n      const ancestor = _restoreSelector(escapedAncestor, ancestorPlaceholders);\n      return selectors.forEach(escapedSelector =>\n        result.push(`${ancestor} ${_restoreSelector(escapedSelector, selectorPlaceholders)}`),\n      );\n    });\n\n    return result.join(', ');\n  }\n\n  /** Adds base options common to all harness types. */\n  private _addBaseOptions(options: BaseHarnessFilters) {\n    this._ancestor = options.ancestor || '';\n    if (this._ancestor) {\n      this._descriptions.push(`has ancestor matching selector \"${this._ancestor}\"`);\n    }\n    const selector = options.selector;\n    if (selector !== undefined) {\n      this.add(`host matches selector \"${selector}\"`, async item => {\n        return (await item.host()).matchesSelector(selector);\n      });\n    }\n  }\n}\n\n/** Represent a value as a string for the purpose of logging. */\nfunction _valueAsString(value: unknown) {\n  if (value === undefined) {\n    return 'undefined';\n  }\n  try {\n    // `JSON.stringify` doesn't handle RegExp properly, so we need a custom replacer.\n    // Use a character that is unlikely to appear in real strings to denote the start and end of\n    // the regex. This allows us to strip out the extra quotes around the value added by\n    // `JSON.stringify`. Also do custom escaping on `\"` characters to prevent `JSON.stringify`\n    // from escaping them as if they were part of a string.\n    const stringifiedValue = JSON.stringify(value, (_, v) =>\n      v instanceof RegExp\n        ? `◬MAT_RE_ESCAPE◬${v.toString().replace(/\"/g, '◬MAT_RE_ESCAPE◬')}◬MAT_RE_ESCAPE◬`\n        : v,\n    );\n    // Strip out the extra quotes around regexes and put back the manually escaped `\"` characters.\n    return stringifiedValue\n      .replace(/\"◬MAT_RE_ESCAPE◬|◬MAT_RE_ESCAPE◬\"/g, '')\n      .replace(/◬MAT_RE_ESCAPE◬/g, '\"');\n  } catch {\n    // `JSON.stringify` will throw if the object is cyclical,\n    // in this case the best we can do is report the value as `{...}`.\n    return '{...}';\n  }\n}\n\n/**\n * Splits up a compound selector into its parts and escapes any quoted content. The quoted content\n * has to be escaped, because it can contain commas which will throw throw us off when trying to\n * split it.\n * @param selector Selector to be split.\n * @returns The escaped string where any quoted content is replaced with a placeholder. E.g.\n * `[foo=\"bar\"]` turns into `[foo=__cdkPlaceholder-0__]`. Use `_restoreSelector` to restore\n * the placeholders.\n */\nfunction _splitAndEscapeSelector(selector: string): [parts: string[], placeholders: string[]] {\n  const placeholders: string[] = [];\n\n  // Note that the regex doesn't account for nested quotes so something like `\"ab'cd'e\"` will be\n  // considered as two blocks. It's a bit of an edge case, but if we find that it's a problem,\n  // we can make it a bit smarter using a loop. Use this for now since it's more readable and\n  // compact. More complete implementation:\n  // https://github.com/angular/angular/blob/bd34bc9e89f18a/packages/compiler/src/shadow_css.ts#L655\n  const result = selector.replace(/([\"'][^[\"']*[\"'])/g, (_, keep) => {\n    const replaceBy = `__cdkPlaceholder-${placeholders.length}__`;\n    placeholders.push(keep);\n    return replaceBy;\n  });\n\n  return [result.split(',').map(part => part.trim()), placeholders];\n}\n\n/** Restores a selector whose content was escaped in `_splitAndEscapeSelector`. */\nfunction _restoreSelector(selector: string, placeholders: string[]): string {\n  return selector.replace(/__cdkPlaceholder-(\\d+)__/g, (_, index) => placeholders[+index]);\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {parallel} from './change-detection';\nimport {\n  ComponentHarness,\n  ComponentHarnessConstructor,\n  HarnessLoader,\n  HarnessPredicate,\n  HarnessQuery,\n  LocatorFactory,\n  LocatorFnResult,\n} from './component-harness';\nimport {TestElement} from './test-element';\n\n/** Parsed form of the queries passed to the `locatorFor*` methods. */\ntype ParsedQueries<T extends ComponentHarness> = {\n  /** The full list of queries, in their original order. */\n  allQueries: (string | HarnessPredicate<T>)[];\n  /**\n   * A filtered view of `allQueries` containing only the queries that are looking for a\n   * `ComponentHarness`\n   */\n  harnessQueries: HarnessPredicate<T>[];\n  /**\n   * A filtered view of `allQueries` containing only the queries that are looking for a\n   * `TestElement`\n   */\n  elementQueries: string[];\n  /** The set of all `ComponentHarness` subclasses represented in the original query list. */\n  harnessTypes: Set<ComponentHarnessConstructor<T>>;\n};\n\n/**\n * Base harness environment class that can be extended to allow `ComponentHarness`es to be used in\n * different test environments (e.g. testbed, protractor, etc.). This class implements the\n * functionality of both a `HarnessLoader` and `LocatorFactory`. This class is generic on the raw\n * element type, `E`, used by the particular test environment.\n */\nexport abstract class HarnessEnvironment<E> implements HarnessLoader, LocatorFactory {\n  /** The root element of this `HarnessEnvironment` as a `TestElement`. */\n  get rootElement(): TestElement {\n    this._rootElement = this._rootElement || this.createTestElement(this.rawRootElement);\n    return this._rootElement;\n  }\n  set rootElement(element: TestElement) {\n    this._rootElement = element;\n  }\n  private _rootElement: TestElement | undefined;\n\n  protected constructor(\n    /** The native root element of this `HarnessEnvironment`. */\n    protected rawRootElement: E,\n  ) {}\n\n  /** Gets a locator factory rooted at the document root. */\n  documentRootLocatorFactory(): LocatorFactory {\n    return this.createEnvironment(this.getDocumentRoot());\n  }\n\n  /**\n   * Creates an asynchronous locator function that can be used to find a `ComponentHarness` instance\n   * or element under the root element of this `HarnessEnvironment`.\n   *\n   * For example, given the following DOM and assuming `DivHarness.hostSelector` is `'div'`\n   *\n   * ```html\n   * <div id=\"d1\"></div><div id=\"d2\"></div>\n   * ```\n   *\n   * then we expect:\n   *\n   * ```ts\n   * await lf.locatorFor(DivHarness, 'div')() // Gets a `DivHarness` instance for #d1\n   * await lf.locatorFor('div', DivHarness)() // Gets a `TestElement` instance for #d1\n   * await lf.locatorFor('span')()            // Throws because the `Promise` rejects\n   * ```\n   *\n   * @param queries A list of queries specifying which harnesses and elements to search for:\n   *   - A `string` searches for elements matching the CSS selector specified by the string.\n   *   - A `ComponentHarness` constructor searches for `ComponentHarness` instances matching the\n   *     given class.\n   *   - A `HarnessPredicate` searches for `ComponentHarness` instances matching the given\n   *     predicate.\n   * @return An asynchronous locator function that searches for and returns a `Promise` for the\n   *   first element or harness matching the given search criteria. Matches are ordered first by\n   *   order in the DOM, and second by order in the queries list. If no matches are found, the\n   *   `Promise` rejects. The type that the `Promise` resolves to is a union of all result types for\n   *   each query.\n   */\n  locatorFor<T extends (HarnessQuery<any> | string)[]>(\n    ...queries: T\n  ): () => Promise<LocatorFnResult<T>> {\n    return () =>\n      _assertResultFound(\n        this._getAllHarnessesAndTestElements(queries),\n        _getDescriptionForLocatorForQueries(queries),\n      );\n  }\n\n  /**\n   * Creates an asynchronous locator function that can be used to find a `ComponentHarness` instance\n   * or element under the root element of this `HarnessEnvironmnet`.\n   *\n   * For example, given the following DOM and assuming `DivHarness.hostSelector` is `'div'`\n   *\n   * ```html\n   * <div id=\"d1\"></div><div id=\"d2\"></div>\n   * ```\n   *\n   * then we expect:\n   *\n   * ```ts\n   * await lf.locatorForOptional(DivHarness, 'div')() // Gets a `DivHarness` instance for #d1\n   * await lf.locatorForOptional('div', DivHarness)() // Gets a `TestElement` instance for #d1\n   * await lf.locatorForOptional('span')()            // Gets `null`\n   * ```\n   *\n   * @param queries A list of queries specifying which harnesses and elements to search for:\n   *   - A `string` searches for elements matching the CSS selector specified by the string.\n   *   - A `ComponentHarness` constructor searches for `ComponentHarness` instances matching the\n   *     given class.\n   *   - A `HarnessPredicate` searches for `ComponentHarness` instances matching the given\n   *     predicate.\n   * @return An asynchronous locator function that searches for and returns a `Promise` for the\n   *   first element or harness matching the given search criteria. Matches are ordered first by\n   *   order in the DOM, and second by order in the queries list. If no matches are found, the\n   *   `Promise` is resolved with `null`. The type that the `Promise` resolves to is a union of all\n   *   result types for each query or null.\n   */\n  locatorForOptional<T extends (HarnessQuery<any> | string)[]>(\n    ...queries: T\n  ): () => Promise<LocatorFnResult<T> | null> {\n    return async () => (await this._getAllHarnessesAndTestElements(queries))[0] || null;\n  }\n\n  /**\n   * Creates an asynchronous locator function that can be used to find `ComponentHarness` instances\n   * or elements under the root element of this `HarnessEnvironment`.\n   *\n   * For example, given the following DOM and assuming `DivHarness.hostSelector` is `'div'` and\n   * `IdIsD1Harness.hostSelector` is `'#d1'`\n   *\n   * ```html\n   * <div id=\"d1\"></div><div id=\"d2\"></div>\n   * ```\n   *\n   * then we expect:\n   *\n   * ```ts\n   * // Gets [DivHarness for #d1, TestElement for #d1, DivHarness for #d2, TestElement for #d2]\n   * await lf.locatorForAll(DivHarness, 'div')()\n   * // Gets [TestElement for #d1, TestElement for #d2]\n   * await lf.locatorForAll('div', '#d1')()\n   * // Gets [DivHarness for #d1, IdIsD1Harness for #d1, DivHarness for #d2]\n   * await lf.locatorForAll(DivHarness, IdIsD1Harness)()\n   * // Gets []\n   * await lf.locatorForAll('span')()\n   * ```\n   *\n   * @param queries A list of queries specifying which harnesses and elements to search for:\n   *   - A `string` searches for elements matching the CSS selector specified by the string.\n   *   - A `ComponentHarness` constructor searches for `ComponentHarness` instances matching the\n   *     given class.\n   *   - A `HarnessPredicate` searches for `ComponentHarness` instances matching the given\n   *     predicate.\n   * @return An asynchronous locator function that searches for and returns a `Promise` for all\n   *   elements and harnesses matching the given search criteria. Matches are ordered first by\n   *   order in the DOM, and second by order in the queries list. If an element matches more than\n   *   one `ComponentHarness` class, the locator gets an instance of each for the same element. If\n   *   an element matches multiple `string` selectors, only one `TestElement` instance is returned\n   *   for that element. The type that the `Promise` resolves to is an array where each element is\n   *   the union of all result types for each query.\n   */\n  locatorForAll<T extends (HarnessQuery<any> | string)[]>(\n    ...queries: T\n  ): () => Promise<LocatorFnResult<T>[]> {\n    return () => this._getAllHarnessesAndTestElements(queries);\n  }\n\n  /** @return A `HarnessLoader` rooted at the root element of this `HarnessEnvironment`. */\n  async rootHarnessLoader(): Promise<HarnessLoader> {\n    return this;\n  }\n\n  /**\n   * Gets a `HarnessLoader` instance for an element under the root of this `HarnessEnvironment`.\n   * @param selector The selector for the root element.\n   * @return A `HarnessLoader` rooted at the first element matching the given selector.\n   * @throws If no matching element is found for the given selector.\n   */\n  async harnessLoaderFor(selector: string): Promise<HarnessLoader> {\n    return this.createEnvironment(\n      await _assertResultFound(this.getAllRawElements(selector), [\n        _getDescriptionForHarnessLoaderQuery(selector),\n      ]),\n    );\n  }\n\n  /**\n   * Gets a `HarnessLoader` instance for an element under the root of this `HarnessEnvironment`.\n   * @param selector The selector for the root element.\n   * @return A `HarnessLoader` rooted at the first element matching the given selector, or null if\n   *     no matching element is found.\n   */\n  async harnessLoaderForOptional(selector: string): Promise<HarnessLoader | null> {\n    const elements = await this.getAllRawElements(selector);\n    return elements[0] ? this.createEnvironment(elements[0]) : null;\n  }\n\n  /**\n   * Gets a list of `HarnessLoader` instances, one for each matching element.\n   * @param selector The selector for the root element.\n   * @return A list of `HarnessLoader`, one rooted at each element matching the given selector.\n   */\n  async harnessLoaderForAll(selector: string): Promise<HarnessLoader[]> {\n    const elements = await this.getAllRawElements(selector);\n    return elements.map(element => this.createEnvironment(element));\n  }\n\n  /**\n   * Searches for an instance of the component corresponding to the given harness type under the\n   * `HarnessEnvironment`'s root element, and returns a `ComponentHarness` for that instance. If\n   * multiple matching components are found, a harness for the first one is returned. If no matching\n   * component is found, an error is thrown.\n   * @param query A query for a harness to create\n   * @return An instance of the given harness type\n   * @throws If a matching component instance can't be found.\n   */\n  getHarness<T extends ComponentHarness>(query: HarnessQuery<T>): Promise<T> {\n    return this.locatorFor(query)();\n  }\n\n  /**\n   * Searches for an instance of the component corresponding to the given harness type under the\n   * `HarnessEnvironment`'s root element, and returns a `ComponentHarness` for that instance. If\n   * multiple matching components are found, a harness for the first one is returned. If no matching\n   * component is found, null is returned.\n   * @param query A query for a harness to create\n   * @return An instance of the given harness type (or null if not found).\n   */\n  getHarnessOrNull<T extends ComponentHarness>(query: HarnessQuery<T>): Promise<T | null> {\n    return this.locatorForOptional(query)();\n  }\n\n  /**\n   * Searches for all instances of the component corresponding to the given harness type under the\n   * `HarnessEnvironment`'s root element, and returns a list `ComponentHarness` for each instance.\n   * @param query A query for a harness to create\n   * @return A list instances of the given harness type.\n   */\n  getAllHarnesses<T extends ComponentHarness>(query: HarnessQuery<T>): Promise<T[]> {\n    return this.locatorForAll(query)();\n  }\n\n  /**\n   * Searches for an instance of the component corresponding to the given harness type under the\n   * `HarnessEnvironment`'s root element, and returns a boolean indicating if any were found.\n   * @param query A query for a harness to create\n   * @return A boolean indicating if an instance was found.\n   */\n  async hasHarness<T extends ComponentHarness>(query: HarnessQuery<T>): Promise<boolean> {\n    return (await this.locatorForOptional(query)()) !== null;\n  }\n\n  /**\n   * Searches for an element with the given selector under the evironment's root element,\n   * and returns a `HarnessLoader` rooted at the matching element. If multiple elements match the\n   * selector, the first is used. If no elements match, an error is thrown.\n   * @param selector The selector for the root element of the new `HarnessLoader`\n   * @return A `HarnessLoader` rooted at the element matching the given selector.\n   * @throws If a matching element can't be found.\n   */\n  async getChildLoader(selector: string): Promise<HarnessLoader> {\n    return this.createEnvironment(\n      await _assertResultFound(this.getAllRawElements(selector), [\n        _getDescriptionForHarnessLoaderQuery(selector),\n      ]),\n    );\n  }\n\n  /**\n   * Searches for all elements with the given selector under the environment's root element,\n   * and returns an array of `HarnessLoader`s, one for each matching element, rooted at that\n   * element.\n   * @param selector The selector for the root element of the new `HarnessLoader`\n   * @return A list of `HarnessLoader`s, one for each matching element, rooted at that element.\n   */\n  async getAllChildLoaders(selector: string): Promise<HarnessLoader[]> {\n    return (await this.getAllRawElements(selector)).map(e => this.createEnvironment(e));\n  }\n\n  /** Creates a `ComponentHarness` for the given harness type with the given raw host element. */\n  protected createComponentHarness<T extends ComponentHarness>(\n    harnessType: ComponentHarnessConstructor<T>,\n    element: E,\n  ): T {\n    return new harnessType(this.createEnvironment(element));\n  }\n\n  /**\n   * Flushes change detection and async tasks captured in the Angular zone.\n   * In most cases it should not be necessary to call this manually. However, there may be some edge\n   * cases where it is needed to fully flush animation events.\n   * This is an abstrct method that must be implemented by subclasses.\n   */\n  abstract forceStabilize(): Promise<void>;\n\n  /**\n   * Waits for all scheduled or running async tasks to complete. This allows harness\n   * authors to wait for async tasks outside of the Angular zone.\n   * This is an abstrct method that must be implemented by subclasses.\n   */\n  abstract waitForTasksOutsideAngular(): Promise<void>;\n\n  /** Gets the root element for the document. */\n  protected abstract getDocumentRoot(): E;\n\n  /** Creates a `TestElement` from a raw element. */\n  protected abstract createTestElement(element: E): TestElement;\n\n  /** Creates a `HarnessEnvironment` rooted at the given raw element. */\n  protected abstract createEnvironment(element: E): HarnessEnvironment<E>;\n\n  /**\n   * Gets a list of all elements matching the given selector under this environment's root element.\n   */\n  protected abstract getAllRawElements(selector: string): Promise<E[]>;\n\n  /**\n   * Matches the given raw elements with the given list of element and harness queries to produce a\n   * list of matched harnesses and test elements.\n   */\n  private async _getAllHarnessesAndTestElements<T extends (HarnessQuery<any> | string)[]>(\n    queries: T,\n  ): Promise<LocatorFnResult<T>[]> {\n    if (!queries.length) {\n      throw Error('CDK Component harness query must contain at least one element.');\n    }\n\n    const {allQueries, harnessQueries, elementQueries, harnessTypes} = _parseQueries(queries);\n\n    // Combine all of the queries into one large comma-delimited selector and use it to get all raw\n    // elements matching any of the individual queries.\n    const rawElements = await this.getAllRawElements(\n      [...elementQueries, ...harnessQueries.map(predicate => predicate.getSelector())].join(','),\n    );\n\n    // If every query is searching for the same harness subclass, we know every result corresponds\n    // to an instance of that subclass. Likewise, if every query is for a `TestElement`, we know\n    // every result corresponds to a `TestElement`. Otherwise we need to verify which result was\n    // found by which selector so it can be matched to the appropriate instance.\n    const skipSelectorCheck =\n      (elementQueries.length === 0 && harnessTypes.size === 1) || harnessQueries.length === 0;\n\n    const perElementMatches = await parallel(() =>\n      rawElements.map(async rawElement => {\n        const testElement = this.createTestElement(rawElement);\n        const allResultsForElement = await parallel(\n          // For each query, get `null` if it doesn't match, or a `TestElement` or\n          // `ComponentHarness` as appropriate if it does match. This gives us everything that\n          // matches the current raw element, but it may contain duplicate entries (e.g.\n          // multiple `TestElement` or multiple `ComponentHarness` of the same type).\n          () =>\n            allQueries.map(query =>\n              this._getQueryResultForElement(query, rawElement, testElement, skipSelectorCheck),\n            ),\n        );\n        return _removeDuplicateQueryResults(allResultsForElement);\n      }),\n    );\n    return ([] as any).concat(...perElementMatches);\n  }\n\n  /**\n   * Check whether the given query matches the given element, if it does return the matched\n   * `TestElement` or `ComponentHarness`, if it does not, return null. In cases where the caller\n   * knows for sure that the query matches the element's selector, `skipSelectorCheck` can be used\n   * to skip verification and optimize performance.\n   */\n  private async _getQueryResultForElement<T extends ComponentHarness>(\n    query: string | HarnessPredicate<T>,\n    rawElement: E,\n    testElement: TestElement,\n    skipSelectorCheck: boolean = false,\n  ): Promise<T | TestElement | null> {\n    if (typeof query === 'string') {\n      return skipSelectorCheck || (await testElement.matchesSelector(query)) ? testElement : null;\n    }\n    if (skipSelectorCheck || (await testElement.matchesSelector(query.getSelector()))) {\n      const harness = this.createComponentHarness(query.harnessType, rawElement);\n      return (await query.evaluate(harness)) ? harness : null;\n    }\n    return null;\n  }\n}\n\n/**\n * Parses a list of queries in the format accepted by the `locatorFor*` methods into an easier to\n * work with format.\n */\nfunction _parseQueries<T extends (HarnessQuery<any> | string)[]>(\n  queries: T,\n): ParsedQueries<LocatorFnResult<T> & ComponentHarness> {\n  const allQueries = [];\n  const harnessQueries = [];\n  const elementQueries = [];\n  const harnessTypes = new Set<\n    ComponentHarnessConstructor<LocatorFnResult<T> & ComponentHarness>\n  >();\n\n  for (const query of queries) {\n    if (typeof query === 'string') {\n      allQueries.push(query);\n      elementQueries.push(query);\n    } else {\n      const predicate = query instanceof HarnessPredicate ? query : new HarnessPredicate(query, {});\n      allQueries.push(predicate);\n      harnessQueries.push(predicate);\n      harnessTypes.add(predicate.harnessType);\n    }\n  }\n\n  return {allQueries, harnessQueries, elementQueries, harnessTypes};\n}\n\n/**\n * Removes duplicate query results for a particular element. (e.g. multiple `TestElement`\n * instances or multiple instances of the same `ComponentHarness` class.\n */\nasync function _removeDuplicateQueryResults<T extends (ComponentHarness | TestElement | null)[]>(\n  results: T,\n): Promise<T> {\n  let testElementMatched = false;\n  let matchedHarnessTypes = new Set();\n  const dedupedMatches = [];\n  for (const result of results) {\n    if (!result) {\n      continue;\n    }\n    if (result instanceof ComponentHarness) {\n      if (!matchedHarnessTypes.has(result.constructor)) {\n        matchedHarnessTypes.add(result.constructor);\n        dedupedMatches.push(result);\n      }\n    } else if (!testElementMatched) {\n      testElementMatched = true;\n      dedupedMatches.push(result);\n    }\n  }\n  return dedupedMatches as T;\n}\n\n/** Verifies that there is at least one result in an array. */\nasync function _assertResultFound<T>(\n  results: Promise<T[]>,\n  queryDescriptions: string[],\n): Promise<T> {\n  const result = (await results)[0];\n  if (result == undefined) {\n    throw Error(\n      `Failed to find element matching one of the following queries:\\n` +\n        queryDescriptions.map(desc => `(${desc})`).join(',\\n'),\n    );\n  }\n  return result;\n}\n\n/** Gets a list of description strings from a list of queries. */\nfunction _getDescriptionForLocatorForQueries(queries: (string | HarnessQuery<any>)[]) {\n  return queries.map(query =>\n    typeof query === 'string'\n      ? _getDescriptionForTestElementQuery(query)\n      : _getDescriptionForComponentHarnessQuery(query),\n  );\n}\n\n/** Gets a description string for a `ComponentHarness` query. */\nfunction _getDescriptionForComponentHarnessQuery(query: HarnessQuery<any>) {\n  const harnessPredicate =\n    query instanceof HarnessPredicate ? query : new HarnessPredicate(query, {});\n  const {name, hostSelector} = harnessPredicate.harnessType;\n  const description = `${name} with host element matching selector: \"${hostSelector}\"`;\n  const constraints = harnessPredicate.getDescription();\n  return (\n    description +\n    (constraints ? ` satisfying the constraints: ${harnessPredicate.getDescription()}` : '')\n  );\n}\n\n/** Gets a description string for a `TestElement` query. */\nfunction _getDescriptionForTestElementQuery(selector: string) {\n  return `TestElement for element matching selector: \"${selector}\"`;\n}\n\n/** Gets a description string for a `HarnessLoader` query. */\nfunction _getDescriptionForHarnessLoaderQuery(selector: string) {\n  return `HarnessLoader for element matching selector: \"${selector}\"`;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {ElementDimensions} from './element-dimensions';\n\n/** Modifier keys that may be held while typing. */\nexport interface ModifierKeys {\n  control?: boolean;\n  alt?: boolean;\n  shift?: boolean;\n  meta?: boolean;\n}\n\n/** Data that can be attached to a custom event dispatched from a `TestElement`. */\nexport type EventData =\n  | string\n  | number\n  | boolean\n  | Function\n  | undefined\n  | null\n  | EventData[]\n  | {[key: string]: EventData};\n\n/** An enum of non-text keys that can be used with the `sendKeys` method. */\n// NOTE: This is a separate enum from `@angular/cdk/keycodes` because we don't necessarily want to\n// support every possible keyCode. We also can't rely on <PERSON>tractor's `Key` because we don't want a\n// dependency on any particular testing framework here. Instead we'll just maintain this supported\n// list of keys and let individual concrete `HarnessEnvironment` classes map them to whatever key\n// representation is used in its respective testing framework.\n// tslint:disable-next-line:prefer-const-enum Seems like this causes some issues with System.js\nexport enum TestKey {\n  BACKSPACE,\n  TAB,\n  ENTER,\n  SHIFT,\n  CONTROL,\n  ALT,\n  ESCAPE,\n  PAGE_UP,\n  PAGE_DOWN,\n  END,\n  HOME,\n  LEFT_ARROW,\n  UP_ARROW,\n  RIGHT_ARROW,\n  DOWN_ARROW,\n  INSERT,\n  DELETE,\n  F1,\n  F2,\n  F3,\n  F4,\n  F5,\n  F6,\n  F7,\n  F8,\n  F9,\n  F10,\n  F11,\n  F12,\n  META,\n  COMMA, // Commas are a common separator key.\n}\n\n/**\n * This acts as a common interface for DOM elements across both unit and e2e tests. It is the\n * interface through which the ComponentHarness interacts with the component's DOM.\n */\nexport interface TestElement {\n  /** Blur the element. */\n  blur(): Promise<void>;\n\n  /** Clear the element's input (for input and textarea elements only). */\n  clear(): Promise<void>;\n\n  /**\n   * Click the element at the default location for the current environment. If you need to guarantee\n   * the element is clicked at a specific location, consider using `click('center')` or\n   * `click(x, y)` instead.\n   */\n  click(modifiers?: ModifierKeys): Promise<void>;\n\n  /** Click the element at the element's center. */\n  click(location: 'center', modifiers?: ModifierKeys): Promise<void>;\n\n  /**\n   * Click the element at the specified coordinates relative to the top-left of the element.\n   * @param relativeX Coordinate within the element, along the X-axis at which to click.\n   * @param relativeY Coordinate within the element, along the Y-axis at which to click.\n   * @param modifiers Modifier keys held while clicking\n   */\n  click(relativeX: number, relativeY: number, modifiers?: ModifierKeys): Promise<void>;\n\n  /**\n   * Right clicks on the element at the specified coordinates relative to the top-left of it.\n   * @param relativeX Coordinate within the element, along the X-axis at which to click.\n   * @param relativeY Coordinate within the element, along the Y-axis at which to click.\n   * @param modifiers Modifier keys held while clicking\n   */\n  rightClick(relativeX: number, relativeY: number, modifiers?: ModifierKeys): Promise<void>;\n\n  /** Focus the element. */\n  focus(): Promise<void>;\n\n  /** Get the computed value of the given CSS property for the element. */\n  getCssValue(property: string): Promise<string>;\n\n  /** Hovers the mouse over the element. */\n  hover(): Promise<void>;\n\n  /** Moves the mouse away from the element. */\n  mouseAway(): Promise<void>;\n\n  /**\n   * Sends the given string to the input as a series of key presses. Also fires input events\n   * and attempts to add the string to the Element's value. Note that some environments cannot\n   * reproduce native browser behavior for keyboard shortcuts such as Tab, Ctrl + A, etc.\n   * @throws An error if no keys have been specified.\n   */\n  sendKeys(...keys: (string | TestKey)[]): Promise<void>;\n\n  /**\n   * Sends the given string to the input as a series of key presses. Also fires input\n   * events and attempts to add the string to the Element's value.\n   * @throws An error if no keys have been specified.\n   */\n  sendKeys(modifiers: ModifierKeys, ...keys: (string | TestKey)[]): Promise<void>;\n\n  /**\n   * Gets the text from the element.\n   * @param options Options that affect what text is included.\n   */\n  text(options?: TextOptions): Promise<string>;\n\n  /**\n   * Sets the value of a `contenteditable` element.\n   * @param value Value to be set on the element.\n   * @breaking-change 16.0.0 Will become a required method.\n   */\n  setContenteditableValue?(value: string): Promise<void>;\n\n  /** Gets the value for the given attribute from the element. */\n  getAttribute(name: string): Promise<string | null>;\n\n  /** Checks whether the element has the given class. */\n  hasClass(name: string): Promise<boolean>;\n\n  /** Gets the dimensions of the element. */\n  getDimensions(): Promise<ElementDimensions>;\n\n  /** Gets the value of a property of an element. */\n  getProperty<T = any>(name: string): Promise<T>;\n\n  /** Checks whether this element matches the given selector. */\n  matchesSelector(selector: string): Promise<boolean>;\n\n  /** Checks whether the element is focused. */\n  isFocused(): Promise<boolean>;\n\n  /** Sets the value of a property of an input. */\n  setInputValue(value: string): Promise<void>;\n\n  // Note that ideally here we'd be selecting options based on their value, rather than their\n  // index, but we're limited by `@angular/forms` which will modify the option value in some cases.\n  // Since the value will be truncated, we can't rely on it to do the lookup in the DOM. See:\n  // https://github.com/angular/angular/blob/main/packages/forms/src/directives/select_control_value_accessor.ts#L19\n  /** Selects the options at the specified indexes inside of a native `select` element. */\n  selectOptions(...optionIndexes: number[]): Promise<void>;\n\n  /**\n   * Dispatches an event with a particular name.\n   * @param name Name of the event to be dispatched.\n   */\n  dispatchEvent(name: string, data?: Record<string, EventData>): Promise<void>;\n}\n\n/**\n * Options that affect the text returned by `TestElement.text`.\n */\nexport interface TextOptions {\n  /** Optional selector for elements whose content should be excluded from the text string. */\n  exclude?: string;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n/**\n * Returns an error which reports that no keys have been specified.\n * @docs-private\n */\nexport function getNoKeysSpecifiedError() {\n  return Error('No keys have been specified.');\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n/**\n * Gets text of element excluding certain selectors within the element.\n * @param element Element to get text from,\n * @param excludeSelector Selector identifying which elements to exclude,\n */\nexport function _getTextWithExcludedElements(element: Element, excludeSelector: string) {\n  const clone = element.cloneNode(true) as Element;\n  const exclusions = clone.querySelectorAll(excludeSelector);\n  for (let i = 0; i < exclusions.length; i++) {\n    exclusions[i].remove();\n  }\n  return (clone.textContent || '').trim();\n}\n"], "names": [], "mappings": ";;AA0BA;AACA,MAAM,0BAA0B,GAAG,IAAI,eAAe,CAA4B;AAChF,IAAA,UAAU,EAAE,KAAK;AAClB,CAAA,CAAC;AAEF;AACA,IAAI,+BAAoD;AAExD;;;;AAIG;AACH,SAAS,iCAAiC,CAAC,MAAiC,EAAA;AAC1E,IAAA,MAAM,CAAC,kBAAkB,IAAI;AAC/B;AAEA;;;;AAIG;AACG,SAAU,+BAA+B,CAC7C,OAAoD,EAAA;AAEpD,IAAA,qCAAqC,EAAE;AACvC,IAAA,+BAA+B,GAAG,0BAA0B,CAAC,SAAS,CAAC,OAAO,CAAC;AACjF;AAEA;SACgB,qCAAqC,GAAA;IACnD,+BAA+B,EAAE,WAAW,EAAE;IAC9C,+BAA+B,GAAG,IAAI;AACxC;AAEA;;;;;;AAMG;AACH,eAAe,oBAAoB,CAAI,EAAoB,EAAE,qBAA8B,EAAA;;AAEzF,IAAA,IAAI,0BAA0B,CAAC,QAAQ,EAAE,CAAC,UAAU,EAAE;QACpD,OAAO,MAAM,EAAE,EAAE;;;IAInB,IAAI,CAAC,+BAA+B,EAAE;QACpC,+BAA+B,CAAC,iCAAiC,CAAC;;IAGpE,IAAI,qBAAqB,EAAE;QACzB,MAAM,IAAI,OAAO,CAAC,OAAO,IACvB,0BAA0B,CAAC,IAAI,CAAC;AAC9B,YAAA,UAAU,EAAE,IAAI;AAChB,YAAA,kBAAkB,EAAE,OAAqB;AAC1C,SAAA,CAAC,CACH;;;;AAID,QAAA,IAAI;YACF,OAAO,MAAM,EAAE,EAAE;;gBACT;YACR,MAAM,IAAI,OAAO,CAAC,OAAO,IACvB,0BAA0B,CAAC,IAAI,CAAC;AAC9B,gBAAA,UAAU,EAAE,KAAK;AACjB,gBAAA,kBAAkB,EAAE,OAAqB;AAC1C,aAAA,CAAC,CACH;;;SAEE;QACL,0BAA0B,CAAC,IAAI,CAAC,EAAC,UAAU,EAAE,IAAI,EAAC,CAAC;;;;AAInD,QAAA,IAAI;YACF,OAAO,MAAM,EAAE,EAAE;;gBACT;YACR,0BAA0B,CAAC,IAAI,CAAC,EAAC,UAAU,EAAE,KAAK,EAAC,CAAC;;;AAG1D;AAEA;;;;AAIG;AACI,eAAe,qBAAqB,CAAI,EAAoB,EAAA;AACjE,IAAA,OAAO,oBAAoB,CAAC,EAAE,EAAE,KAAK,CAAC;AACxC;AAkEA;;;;;;AAMG;AACI,eAAe,QAAQ,CAAI,MAA0C,EAAA;AAC1E,IAAA,OAAO,oBAAoB,CAAC,MAAM,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,IAAI,CAAC;AAChE;;AC0GA;;;AAGG;MACmB,gBAAgB,CAAA;AACL,IAAA,cAAA;AAA/B,IAAA,WAAA,CAA+B,cAA8B,EAAA;QAA9B,IAAc,CAAA,cAAA,GAAd,cAAc;;;AAG7C,IAAA,MAAM,IAAI,GAAA;AACR,QAAA,OAAO,IAAI,CAAC,cAAc,CAAC,WAAW;;AAGxC;;;;AAIG;IACO,0BAA0B,GAAA;AAClC,QAAA,OAAO,IAAI,CAAC,cAAc,CAAC,0BAA0B,EAAE;;AAGzD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6BG;IACO,UAAU,CAClB,GAAG,OAAU,EAAA;QAEb,OAAO,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,GAAG,OAAO,CAAC;;AAGnD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6BG;IACO,kBAAkB,CAC1B,GAAG,OAAU,EAAA;QAEb,OAAO,IAAI,CAAC,cAAc,CAAC,kBAAkB,CAAC,GAAG,OAAO,CAAC;;AAG3D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqCG;IACO,aAAa,CACrB,GAAG,OAAU,EAAA;QAEb,OAAO,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,GAAG,OAAO,CAAC;;AAGtD;;;;AAIG;AACO,IAAA,MAAM,cAAc,GAAA;AAC5B,QAAA,OAAO,IAAI,CAAC,cAAc,CAAC,cAAc,EAAE;;AAG7C;;;AAGG;AACO,IAAA,MAAM,0BAA0B,GAAA;AACxC,QAAA,OAAO,IAAI,CAAC,cAAc,CAAC,0BAA0B,EAAE;;AAE1D;AAED;;;AAGG;AACG,MAAgB,gCACpB,SAAQ,gBAAgB,CAAA;AAGxB;;;;;AAKG;IACH,MAAM,cAAc,CAAC,QAAW,EAAA;AAC9B,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,oBAAoB,EAAE,EAAE,cAAc,CAAC,QAAQ,CAAC;;AAGrE;;;;;AAKG;IACH,MAAM,kBAAkB,CAAC,QAAW,EAAA;AAClC,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,oBAAoB,EAAE,EAAE,kBAAkB,CAAC,QAAQ,CAAC;;AAGzE;;;;;AAKG;IACH,MAAM,UAAU,CAA6B,KAAsB,EAAA;AACjE,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,oBAAoB,EAAE,EAAE,UAAU,CAAC,KAAK,CAAC;;AAG9D;;;;AAIG;IACH,MAAM,gBAAgB,CAA6B,KAAsB,EAAA;AACvE,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,oBAAoB,EAAE,EAAE,gBAAgB,CAAC,KAAK,CAAC;;AAGpE;;;;AAIG;IACH,MAAM,eAAe,CAA6B,KAAsB,EAAA;AACtE,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,oBAAoB,EAAE,EAAE,eAAe,CAAC,KAAK,CAAC;;AAGnE;;;;;;AAMG;IACH,MAAM,UAAU,CAA6B,KAAsB,EAAA;AACjE,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,oBAAoB,EAAE,EAAE,UAAU,CAAC,KAAK,CAAC;;AAG9D;;;AAGG;AACO,IAAA,MAAM,oBAAoB,GAAA;AAClC,QAAA,OAAO,IAAI,CAAC,cAAc,CAAC,iBAAiB,EAAE;;AAEjD;AAyBD;;;AAGG;MACU,gBAAgB,CAAA;AAMlB,IAAA,WAAA;IALD,WAAW,GAAwB,EAAE;IACrC,aAAa,GAAa,EAAE;AAC5B,IAAA,SAAS;IAEjB,WACS,CAAA,WAA2C,EAClD,OAA2B,EAAA;QADpB,IAAW,CAAA,WAAA,GAAX,WAAW;AAGlB,QAAA,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC;;AAG/B;;;;;;;;AAQG;AACH,IAAA,aAAa,aAAa,CACxB,KAA6C,EAC7C,OAA+B,EAAA;QAE/B,KAAK,GAAG,MAAM,KAAK;AACnB,QAAA,IAAI,OAAO,KAAK,IAAI,EAAE;YACpB,OAAO,KAAK,KAAK,IAAI;;AAChB,aAAA,IAAI,KAAK,KAAK,IAAI,EAAE;AACzB,YAAA,OAAO,KAAK;;AAEd,QAAA,OAAO,OAAO,OAAO,KAAK,QAAQ,GAAG,KAAK,KAAK,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC;;AAG9E;;;;;AAKG;IACH,GAAG,CAAC,WAAmB,EAAE,SAA4B,EAAA;AACnD,QAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,WAAW,CAAC;AACpC,QAAA,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC;AAChC,QAAA,OAAO,IAAI;;AAGb;;;;;;;AAOG;AACH,IAAA,SAAS,CAAI,IAAY,EAAE,MAAqB,EAAE,SAAqC,EAAA;AACrF,QAAA,IAAI,MAAM,KAAK,SAAS,EAAE;YACxB,IAAI,CAAC,GAAG,CAAC,CAAA,EAAG,IAAI,CAAM,GAAA,EAAA,cAAc,CAAC,MAAM,CAAC,CAAA,CAAE,EAAE,IAAI,IAAI,SAAS,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;;AAElF,QAAA,OAAO,IAAI;;AAGb;;;;AAIG;IACH,MAAM,MAAM,CAAC,SAAc,EAAA;AACzB,QAAA,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;AAC1B,YAAA,OAAO,EAAE;;QAEX,MAAM,OAAO,GAAG,MAAM,QAAQ,CAAC,MAAM,SAAS,CAAC,GAAG,CAAC,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1E,QAAA,OAAO,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,OAAO,CAAC,CAAC,CAAC,CAAC;;AAG/C;;;;;AAKG;IACH,MAAM,QAAQ,CAAC,OAAU,EAAA;QACvB,MAAM,OAAO,GAAG,MAAM,QAAQ,CAAC,MAAM,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;AAC3E,QAAA,OAAO,OAAO,CAAC,MAAM,CAAC,CAAC,QAAQ,EAAE,OAAO,KAAK,QAAQ,IAAI,OAAO,EAAE,IAAI,CAAC;;;IAIzE,cAAc,GAAA;QACZ,OAAO,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC;;;IAItC,WAAW,GAAA;;AAET,QAAA,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;AACnB,YAAA,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,YAAY,IAAI,EAAE,EAAE,IAAI,EAAE;;AAGrD,QAAA,MAAM,CAAC,SAAS,EAAE,oBAAoB,CAAC,GAAG,uBAAuB,CAAC,IAAI,CAAC,SAAS,CAAC;AACjF,QAAA,MAAM,CAAC,SAAS,EAAE,oBAAoB,CAAC,GAAG,uBAAuB,CAC/D,IAAI,CAAC,WAAW,CAAC,YAAY,IAAI,EAAE,CACpC;QACD,MAAM,MAAM,GAAa,EAAE;;;AAI3B,QAAA,SAAS,CAAC,OAAO,CAAC,eAAe,IAAG;YAClC,MAAM,QAAQ,GAAG,gBAAgB,CAAC,eAAe,EAAE,oBAAoB,CAAC;YACxE,OAAO,SAAS,CAAC,OAAO,CAAC,eAAe,IACtC,MAAM,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAA,CAAA,EAAI,gBAAgB,CAAC,eAAe,EAAE,oBAAoB,CAAC,CAAA,CAAE,CAAC,CACtF;AACH,SAAC,CAAC;AAEF,QAAA,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC;;;AAIlB,IAAA,eAAe,CAAC,OAA2B,EAAA;QACjD,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,QAAQ,IAAI,EAAE;AACvC,QAAA,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAmC,gCAAA,EAAA,IAAI,CAAC,SAAS,CAAG,CAAA,CAAA,CAAC;;AAE/E,QAAA,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ;AACjC,QAAA,IAAI,QAAQ,KAAK,SAAS,EAAE;YAC1B,IAAI,CAAC,GAAG,CAAC,CAA0B,uBAAA,EAAA,QAAQ,CAAG,CAAA,CAAA,EAAE,OAAM,IAAI,KAAG;AAC3D,gBAAA,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,eAAe,CAAC,QAAQ,CAAC;AACtD,aAAC,CAAC;;;AAGP;AAED;AACA,SAAS,cAAc,CAAC,KAAc,EAAA;AACpC,IAAA,IAAI,KAAK,KAAK,SAAS,EAAE;AACvB,QAAA,OAAO,WAAW;;AAEpB,IAAA,IAAI;;;;;;AAMF,QAAA,MAAM,gBAAgB,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,KAClD,CAAC,YAAY;AACX,cAAE,CAAA,eAAA,EAAkB,CAAC,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,IAAI,EAAE,iBAAiB,CAAC,CAAiB,eAAA;cAChF,CAAC,CACN;;AAED,QAAA,OAAO;AACJ,aAAA,OAAO,CAAC,oCAAoC,EAAE,EAAE;AAChD,aAAA,OAAO,CAAC,kBAAkB,EAAE,GAAG,CAAC;;AACnC,IAAA,MAAM;;;AAGN,QAAA,OAAO,OAAO;;AAElB;AAEA;;;;;;;;AAQG;AACH,SAAS,uBAAuB,CAAC,QAAgB,EAAA;IAC/C,MAAM,YAAY,GAAa,EAAE;;;;;;AAOjC,IAAA,MAAM,MAAM,GAAG,QAAQ,CAAC,OAAO,CAAC,oBAAoB,EAAE,CAAC,CAAC,EAAE,IAAI,KAAI;AAChE,QAAA,MAAM,SAAS,GAAG,CAAA,iBAAA,EAAoB,YAAY,CAAC,MAAM,IAAI;AAC7D,QAAA,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC;AACvB,QAAA,OAAO,SAAS;AAClB,KAAC,CAAC;IAEF,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,YAAY,CAAC;AACnE;AAEA;AACA,SAAS,gBAAgB,CAAC,QAAgB,EAAE,YAAsB,EAAA;AAChE,IAAA,OAAO,QAAQ,CAAC,OAAO,CAAC,2BAA2B,EAAE,CAAC,CAAC,EAAE,KAAK,KAAK,YAAY,CAAC,CAAC,KAAK,CAAC,CAAC;AAC1F;;AClsBA;;;;;AAKG;MACmB,kBAAkB,CAAA;AAa1B,IAAA,cAAA;;AAXZ,IAAA,IAAI,WAAW,GAAA;AACb,QAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,cAAc,CAAC;QACpF,OAAO,IAAI,CAAC,YAAY;;IAE1B,IAAI,WAAW,CAAC,OAAoB,EAAA;AAClC,QAAA,IAAI,CAAC,YAAY,GAAG,OAAO;;AAErB,IAAA,YAAY;AAEpB,IAAA,WAAA;;IAEY,cAAiB,EAAA;QAAjB,IAAc,CAAA,cAAA,GAAd,cAAc;;;IAI1B,0BAA0B,GAAA;QACxB,OAAO,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC;;AAGvD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6BG;IACH,UAAU,CACR,GAAG,OAAU,EAAA;AAEb,QAAA,OAAO,MACL,kBAAkB,CAChB,IAAI,CAAC,+BAA+B,CAAC,OAAO,CAAC,EAC7C,mCAAmC,CAAC,OAAO,CAAC,CAC7C;;AAGL;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6BG;IACH,kBAAkB,CAChB,GAAG,OAAU,EAAA;AAEb,QAAA,OAAO,YAAY,CAAC,MAAM,IAAI,CAAC,+BAA+B,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,IAAI,IAAI;;AAGrF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqCG;IACH,aAAa,CACX,GAAG,OAAU,EAAA;QAEb,OAAO,MAAM,IAAI,CAAC,+BAA+B,CAAC,OAAO,CAAC;;;AAI5D,IAAA,MAAM,iBAAiB,GAAA;AACrB,QAAA,OAAO,IAAI;;AAGb;;;;;AAKG;IACH,MAAM,gBAAgB,CAAC,QAAgB,EAAA;AACrC,QAAA,OAAO,IAAI,CAAC,iBAAiB,CAC3B,MAAM,kBAAkB,CAAC,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,EAAE;YACzD,oCAAoC,CAAC,QAAQ,CAAC;AAC/C,SAAA,CAAC,CACH;;AAGH;;;;;AAKG;IACH,MAAM,wBAAwB,CAAC,QAAgB,EAAA;QAC7C,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC;QACvD,OAAO,QAAQ,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI;;AAGjE;;;;AAIG;IACH,MAAM,mBAAmB,CAAC,QAAgB,EAAA;QACxC,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC;AACvD,QAAA,OAAO,QAAQ,CAAC,GAAG,CAAC,OAAO,IAAI,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;;AAGjE;;;;;;;;AAQG;AACH,IAAA,UAAU,CAA6B,KAAsB,EAAA;AAC3D,QAAA,OAAO,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE;;AAGjC;;;;;;;AAOG;AACH,IAAA,gBAAgB,CAA6B,KAAsB,EAAA;AACjE,QAAA,OAAO,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,EAAE;;AAGzC;;;;;AAKG;AACH,IAAA,eAAe,CAA6B,KAAsB,EAAA;AAChE,QAAA,OAAO,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,EAAE;;AAGpC;;;;;AAKG;IACH,MAAM,UAAU,CAA6B,KAAsB,EAAA;AACjE,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,EAAE,MAAM,IAAI;;AAG1D;;;;;;;AAOG;IACH,MAAM,cAAc,CAAC,QAAgB,EAAA;AACnC,QAAA,OAAO,IAAI,CAAC,iBAAiB,CAC3B,MAAM,kBAAkB,CAAC,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,EAAE;YACzD,oCAAoC,CAAC,QAAQ,CAAC;AAC/C,SAAA,CAAC,CACH;;AAGH;;;;;;AAMG;IACH,MAAM,kBAAkB,CAAC,QAAgB,EAAA;QACvC,OAAO,CAAC,MAAM,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,EAAE,GAAG,CAAC,CAAC,IAAI,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC;;;IAI3E,sBAAsB,CAC9B,WAA2C,EAC3C,OAAU,EAAA;QAEV,OAAO,IAAI,WAAW,CAAC,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;;AAgCzD;;;AAGG;IACK,MAAM,+BAA+B,CAC3C,OAAU,EAAA;AAEV,QAAA,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;AACnB,YAAA,MAAM,KAAK,CAAC,gEAAgE,CAAC;;AAG/E,QAAA,MAAM,EAAC,UAAU,EAAE,cAAc,EAAE,cAAc,EAAE,YAAY,EAAC,GAAG,aAAa,CAAC,OAAO,CAAC;;;AAIzF,QAAA,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAC9C,CAAC,GAAG,cAAc,EAAE,GAAG,cAAc,CAAC,GAAG,CAAC,SAAS,IAAI,SAAS,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAC3F;;;;;QAMD,MAAM,iBAAiB,GACrB,CAAC,cAAc,CAAC,MAAM,KAAK,CAAC,IAAI,YAAY,CAAC,IAAI,KAAK,CAAC,KAAK,cAAc,CAAC,MAAM,KAAK,CAAC;AAEzF,QAAA,MAAM,iBAAiB,GAAG,MAAM,QAAQ,CAAC,MACvC,WAAW,CAAC,GAAG,CAAC,OAAM,UAAU,KAAG;YACjC,MAAM,WAAW,GAAG,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC;YACtD,MAAM,oBAAoB,GAAG,MAAM,QAAQ;;;;;YAKzC,MACE,UAAU,CAAC,GAAG,CAAC,KAAK,IAClB,IAAI,CAAC,yBAAyB,CAAC,KAAK,EAAE,UAAU,EAAE,WAAW,EAAE,iBAAiB,CAAC,CAClF,CACJ;AACD,YAAA,OAAO,4BAA4B,CAAC,oBAAoB,CAAC;SAC1D,CAAC,CACH;AACD,QAAA,OAAQ,EAAU,CAAC,MAAM,CAAC,GAAG,iBAAiB,CAAC;;AAGjD;;;;;AAKG;IACK,MAAM,yBAAyB,CACrC,KAAmC,EACnC,UAAa,EACb,WAAwB,EACxB,iBAAA,GAA6B,KAAK,EAAA;AAElC,QAAA,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;AAC7B,YAAA,OAAO,iBAAiB,KAAK,MAAM,WAAW,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC,GAAG,WAAW,GAAG,IAAI;;AAE7F,QAAA,IAAI,iBAAiB,KAAK,MAAM,WAAW,CAAC,eAAe,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC,CAAC,EAAE;AACjF,YAAA,MAAM,OAAO,GAAG,IAAI,CAAC,sBAAsB,CAAC,KAAK,CAAC,WAAW,EAAE,UAAU,CAAC;AAC1E,YAAA,OAAO,CAAC,MAAM,KAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,OAAO,GAAG,IAAI;;AAEzD,QAAA,OAAO,IAAI;;AAEd;AAED;;;AAGG;AACH,SAAS,aAAa,CACpB,OAAU,EAAA;IAEV,MAAM,UAAU,GAAG,EAAE;IACrB,MAAM,cAAc,GAAG,EAAE;IACzB,MAAM,cAAc,GAAG,EAAE;AACzB,IAAA,MAAM,YAAY,GAAG,IAAI,GAAG,EAEzB;AAEH,IAAA,KAAK,MAAM,KAAK,IAAI,OAAO,EAAE;AAC3B,QAAA,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;AAC7B,YAAA,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC;AACtB,YAAA,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC;;aACrB;AACL,YAAA,MAAM,SAAS,GAAG,KAAK,YAAY,gBAAgB,GAAG,KAAK,GAAG,IAAI,gBAAgB,CAAC,KAAK,EAAE,EAAE,CAAC;AAC7F,YAAA,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC;AAC1B,YAAA,cAAc,CAAC,IAAI,CAAC,SAAS,CAAC;AAC9B,YAAA,YAAY,CAAC,GAAG,CAAC,SAAS,CAAC,WAAW,CAAC;;;IAI3C,OAAO,EAAC,UAAU,EAAE,cAAc,EAAE,cAAc,EAAE,YAAY,EAAC;AACnE;AAEA;;;AAGG;AACH,eAAe,4BAA4B,CACzC,OAAU,EAAA;IAEV,IAAI,kBAAkB,GAAG,KAAK;AAC9B,IAAA,IAAI,mBAAmB,GAAG,IAAI,GAAG,EAAE;IACnC,MAAM,cAAc,GAAG,EAAE;AACzB,IAAA,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE;QAC5B,IAAI,CAAC,MAAM,EAAE;YACX;;AAEF,QAAA,IAAI,MAAM,YAAY,gBAAgB,EAAE;YACtC,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,MAAM,CAAC,WAAW,CAAC,EAAE;AAChD,gBAAA,mBAAmB,CAAC,GAAG,CAAC,MAAM,CAAC,WAAW,CAAC;AAC3C,gBAAA,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC;;;aAExB,IAAI,CAAC,kBAAkB,EAAE;YAC9B,kBAAkB,GAAG,IAAI;AACzB,YAAA,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC;;;AAG/B,IAAA,OAAO,cAAmB;AAC5B;AAEA;AACA,eAAe,kBAAkB,CAC/B,OAAqB,EACrB,iBAA2B,EAAA;IAE3B,MAAM,MAAM,GAAG,CAAC,MAAM,OAAO,EAAE,CAAC,CAAC;AACjC,IAAA,IAAI,MAAM,IAAI,SAAS,EAAE;QACvB,MAAM,KAAK,CACT,CAAiE,+DAAA,CAAA;AAC/D,YAAA,iBAAiB,CAAC,GAAG,CAAC,IAAI,IAAI,CAAA,CAAA,EAAI,IAAI,CAAA,CAAA,CAAG,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CACzD;;AAEH,IAAA,OAAO,MAAM;AACf;AAEA;AACA,SAAS,mCAAmC,CAAC,OAAuC,EAAA;IAClF,OAAO,OAAO,CAAC,GAAG,CAAC,KAAK,IACtB,OAAO,KAAK,KAAK;AACf,UAAE,kCAAkC,CAAC,KAAK;AAC1C,UAAE,uCAAuC,CAAC,KAAK,CAAC,CACnD;AACH;AAEA;AACA,SAAS,uCAAuC,CAAC,KAAwB,EAAA;AACvE,IAAA,MAAM,gBAAgB,GACpB,KAAK,YAAY,gBAAgB,GAAG,KAAK,GAAG,IAAI,gBAAgB,CAAC,KAAK,EAAE,EAAE,CAAC;IAC7E,MAAM,EAAC,IAAI,EAAE,YAAY,EAAC,GAAG,gBAAgB,CAAC,WAAW;AACzD,IAAA,MAAM,WAAW,GAAG,CAAA,EAAG,IAAI,CAA0C,uCAAA,EAAA,YAAY,GAAG;AACpF,IAAA,MAAM,WAAW,GAAG,gBAAgB,CAAC,cAAc,EAAE;AACrD,IAAA,QACE,WAAW;AACX,SAAC,WAAW,GAAG,CAAA,6BAAA,EAAgC,gBAAgB,CAAC,cAAc,EAAE,CAAA,CAAE,GAAG,EAAE,CAAC;AAE5F;AAEA;AACA,SAAS,kCAAkC,CAAC,QAAgB,EAAA;IAC1D,OAAO,CAAA,4CAAA,EAA+C,QAAQ,CAAA,CAAA,CAAG;AACnE;AAEA;AACA,SAAS,oCAAoC,CAAC,QAAgB,EAAA;IAC5D,OAAO,CAAA,8CAAA,EAAiD,QAAQ,CAAA,CAAA,CAAG;AACrE;;AC1dA;AACA;AACA;AACA;AACA;AACA;AACA;IACY;AAAZ,CAAA,UAAY,OAAO,EAAA;AACjB,IAAA,OAAA,CAAA,OAAA,CAAA,WAAA,CAAA,GAAA,CAAA,CAAA,GAAA,WAAS;AACT,IAAA,OAAA,CAAA,OAAA,CAAA,KAAA,CAAA,GAAA,CAAA,CAAA,GAAA,KAAG;AACH,IAAA,OAAA,CAAA,OAAA,CAAA,OAAA,CAAA,GAAA,CAAA,CAAA,GAAA,OAAK;AACL,IAAA,OAAA,CAAA,OAAA,CAAA,OAAA,CAAA,GAAA,CAAA,CAAA,GAAA,OAAK;AACL,IAAA,OAAA,CAAA,OAAA,CAAA,SAAA,CAAA,GAAA,CAAA,CAAA,GAAA,SAAO;AACP,IAAA,OAAA,CAAA,OAAA,CAAA,KAAA,CAAA,GAAA,CAAA,CAAA,GAAA,KAAG;AACH,IAAA,OAAA,CAAA,OAAA,CAAA,QAAA,CAAA,GAAA,CAAA,CAAA,GAAA,QAAM;AACN,IAAA,OAAA,CAAA,OAAA,CAAA,SAAA,CAAA,GAAA,CAAA,CAAA,GAAA,SAAO;AACP,IAAA,OAAA,CAAA,OAAA,CAAA,WAAA,CAAA,GAAA,CAAA,CAAA,GAAA,WAAS;AACT,IAAA,OAAA,CAAA,OAAA,CAAA,KAAA,CAAA,GAAA,CAAA,CAAA,GAAA,KAAG;AACH,IAAA,OAAA,CAAA,OAAA,CAAA,MAAA,CAAA,GAAA,EAAA,CAAA,GAAA,MAAI;AACJ,IAAA,OAAA,CAAA,OAAA,CAAA,YAAA,CAAA,GAAA,EAAA,CAAA,GAAA,YAAU;AACV,IAAA,OAAA,CAAA,OAAA,CAAA,UAAA,CAAA,GAAA,EAAA,CAAA,GAAA,UAAQ;AACR,IAAA,OAAA,CAAA,OAAA,CAAA,aAAA,CAAA,GAAA,EAAA,CAAA,GAAA,aAAW;AACX,IAAA,OAAA,CAAA,OAAA,CAAA,YAAA,CAAA,GAAA,EAAA,CAAA,GAAA,YAAU;AACV,IAAA,OAAA,CAAA,OAAA,CAAA,QAAA,CAAA,GAAA,EAAA,CAAA,GAAA,QAAM;AACN,IAAA,OAAA,CAAA,OAAA,CAAA,QAAA,CAAA,GAAA,EAAA,CAAA,GAAA,QAAM;AACN,IAAA,OAAA,CAAA,OAAA,CAAA,IAAA,CAAA,GAAA,EAAA,CAAA,GAAA,IAAE;AACF,IAAA,OAAA,CAAA,OAAA,CAAA,IAAA,CAAA,GAAA,EAAA,CAAA,GAAA,IAAE;AACF,IAAA,OAAA,CAAA,OAAA,CAAA,IAAA,CAAA,GAAA,EAAA,CAAA,GAAA,IAAE;AACF,IAAA,OAAA,CAAA,OAAA,CAAA,IAAA,CAAA,GAAA,EAAA,CAAA,GAAA,IAAE;AACF,IAAA,OAAA,CAAA,OAAA,CAAA,IAAA,CAAA,GAAA,EAAA,CAAA,GAAA,IAAE;AACF,IAAA,OAAA,CAAA,OAAA,CAAA,IAAA,CAAA,GAAA,EAAA,CAAA,GAAA,IAAE;AACF,IAAA,OAAA,CAAA,OAAA,CAAA,IAAA,CAAA,GAAA,EAAA,CAAA,GAAA,IAAE;AACF,IAAA,OAAA,CAAA,OAAA,CAAA,IAAA,CAAA,GAAA,EAAA,CAAA,GAAA,IAAE;AACF,IAAA,OAAA,CAAA,OAAA,CAAA,IAAA,CAAA,GAAA,EAAA,CAAA,GAAA,IAAE;AACF,IAAA,OAAA,CAAA,OAAA,CAAA,KAAA,CAAA,GAAA,EAAA,CAAA,GAAA,KAAG;AACH,IAAA,OAAA,CAAA,OAAA,CAAA,KAAA,CAAA,GAAA,EAAA,CAAA,GAAA,KAAG;AACH,IAAA,OAAA,CAAA,OAAA,CAAA,KAAA,CAAA,GAAA,EAAA,CAAA,GAAA,KAAG;AACH,IAAA,OAAA,CAAA,OAAA,CAAA,MAAA,CAAA,GAAA,EAAA,CAAA,GAAA,MAAI;AACJ,IAAA,OAAA,CAAA,OAAA,CAAA,OAAA,CAAA,GAAA,EAAA,CAAA,GAAA,OAAK;AACP,CAAC,EAhCW,OAAO,KAAP,OAAO,GAgClB,EAAA,CAAA,CAAA;;AC5DD;;;AAGG;SACa,uBAAuB,GAAA;AACrC,IAAA,OAAO,KAAK,CAAC,8BAA8B,CAAC;AAC9C;;ACNA;;;;AAIG;AACa,SAAA,4BAA4B,CAAC,OAAgB,EAAE,eAAuB,EAAA;IACpF,MAAM,KAAK,GAAG,OAAO,CAAC,SAAS,CAAC,IAAI,CAAY;IAChD,MAAM,UAAU,GAAG,KAAK,CAAC,gBAAgB,CAAC,eAAe,CAAC;AAC1D,IAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAC1C,QAAA,UAAU,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE;;IAExB,OAAO,CAAC,KAAK,CAAC,WAAW,IAAI,EAAE,EAAE,IAAI,EAAE;AACzC;;;;"}