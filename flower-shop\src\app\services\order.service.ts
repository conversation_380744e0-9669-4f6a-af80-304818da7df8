import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BehaviorSubject, Observable, of } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import { Order, OrderStatus, Cart, CheckoutData } from '../models';

@Injectable({
  providedIn: 'root'
})
export class OrderService {
  private ordersSubject = new BehaviorSubject<Order[]>([]);
  public orders$ = this.ordersSubject.asObservable();

  constructor(private http: HttpClient) {
    this.loadOrders();
  }

  private loadOrders(): void {
    this.http.get<Order[]>('/assets/orders.json')
      .pipe(
        catchError(error => {
          console.error('Error loading orders:', error);
          return of([]);
        })
      )
      .subscribe(orders => {
        // Also load from localStorage for demo purposes
        const savedOrders = localStorage.getItem('orders');
        if (savedOrders) {
          try {
            const localOrders = JSON.parse(savedOrders);
            this.ordersSubject.next(localOrders);
          } catch (error) {
            console.error('Error parsing saved orders:', error);
            this.ordersSubject.next(orders);
          }
        } else {
          this.ordersSubject.next(orders);
        }
      });
  }

  createOrder(cart: Cart, checkoutData: CheckoutData): Observable<Order> {
    const currentOrders = this.ordersSubject.value;
    const newId = Math.max(...currentOrders.map(o => o.id), 0) + 1;
    
    const newOrder: Order = {
      id: newId,
      items: cart.items,
      customerInfo: checkoutData,
      totalAmount: cart.totalPrice,
      status: OrderStatus.PENDING,
      orderDate: new Date()
    };

    const updatedOrders = [...currentOrders, newOrder];
    this.ordersSubject.next(updatedOrders);
    this.saveOrdersToStorage(updatedOrders);

    return of(newOrder);
  }

  getOrders(): Observable<Order[]> {
    return this.orders$;
  }

  getOrderById(id: number): Observable<Order | undefined> {
    return this.orders$.pipe(
      map(orders => orders.find(order => order.id === id))
    );
  }

  updateOrderStatus(id: number, status: OrderStatus): Observable<Order | null> {
    const currentOrders = this.ordersSubject.value;
    const orderIndex = currentOrders.findIndex(o => o.id === id);
    
    if (orderIndex === -1) {
      return of(null);
    }

    const updatedOrder: Order = {
      ...currentOrders[orderIndex],
      status
    };

    const updatedOrders = [...currentOrders];
    updatedOrders[orderIndex] = updatedOrder;
    
    this.ordersSubject.next(updatedOrders);
    this.saveOrdersToStorage(updatedOrders);
    
    return of(updatedOrder);
  }

  deleteOrder(id: number): Observable<boolean> {
    const currentOrders = this.ordersSubject.value;
    const updatedOrders = currentOrders.filter(o => o.id !== id);
    
    if (updatedOrders.length === currentOrders.length) {
      return of(false); // Order not found
    }

    this.ordersSubject.next(updatedOrders);
    this.saveOrdersToStorage(updatedOrders);
    
    return of(true);
  }

  getOrdersByStatus(status: OrderStatus): Observable<Order[]> {
    return this.orders$.pipe(
      map(orders => orders.filter(order => order.status === status))
    );
  }

  private saveOrdersToStorage(orders: Order[]): void {
    localStorage.setItem('orders', JSON.stringify(orders));
  }
}
